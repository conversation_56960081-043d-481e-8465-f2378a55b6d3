#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复制前端文件到正确位置
"""

import shutil
from pathlib import Path

def copy_frontend_files():
    """复制前端文件"""
    project_root = Path(__file__).parent
    
    # 源文件路径
    source_html = project_root / "frontend" / "templates" / "index.html"
    
    # 目标文件路径
    target_html = project_root / "frontend" / "index.html"
    
    # 复制HTML文件
    if source_html.exists():
        shutil.copy2(source_html, target_html)
        print(f"已复制: {source_html} -> {target_html}")
    else:
        print(f"源文件不存在: {source_html}")
    
    print("前端文件复制完成!")

if __name__ == "__main__":
    copy_frontend_files()

/**
 * 系统管理模块 - 负责系统状态监控和提示词管理
 */

class SystemModule {
    constructor() {
        this.systemStatus = {
            backend: 'unknown',
            ai_framework: 'unknown',
            deepseek_api: 'unknown'
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        
        // 注册到导航模块
        if (window.navigationModule) {
            window.navigationModule.pageHandlers.set('system', () => this.checkSystemHealth());
            window.navigationModule.pageHandlers.set('prompts', () => this.loadPrompts());
        }
    }

    setupEventListeners() {
        // 提示词管理相关事件
        const promptForm = document.getElementById('promptForm');
        if (promptForm) {
            promptForm.addEventListener('submit', (e) => this.handlePromptSubmit(e));
        }

        // 重置提示词按钮
        const resetBtn = document.querySelector('[onclick="resetPrompts()"]');
        if (resetBtn) {
            resetBtn.onclick = () => this.resetPrompts();
        }

        // 系统健康检查按钮
        const healthBtn = document.querySelector('[onclick="checkSystemHealth()"]');
        if (healthBtn) {
            healthBtn.onclick = () => this.checkSystemHealth();
        }
    }

    async checkSystemHealth() {
        const statusContainer = document.getElementById('systemStatus');
        if (!statusContainer) return;

        try {
            // 显示检查中状态
            statusContainer.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 24px; margin-bottom: 16px;">🔄</div>
                    <p>正在检查系统状态...</p>
                </div>
            `;

            const response = await fetch('/api/health');
            const data = await response.json();

            this.systemStatus = data;
            this.renderSystemStatus(statusContainer, data);

        } catch (error) {
            this.renderSystemError(statusContainer, error);
        }
    }

    renderSystemStatus(container, status) {
        const getStatusIcon = (status) => {
            switch (status) {
                case 'healthy': return '✅';
                case 'warning': return '⚠️';
                case 'error': return '❌';
                default: return '❓';
            }
        };

        const getStatusColor = (status) => {
            switch (status) {
                case 'healthy': return 'var(--success-green)';
                case 'warning': return 'var(--warning-orange)';
                case 'error': return 'var(--error-red)';
                default: return 'var(--text-secondary)';
            }
        };

        const html = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
                <div class="status-card" style="background: var(--bg-white); border: 1px solid var(--border-light); border-radius: 8px; padding: 16px;">
                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <span style="font-size: 24px; margin-right: 12px;">${getStatusIcon(status.backend_status)}</span>
                        <h4 style="color: var(--text-primary); margin: 0;">后端服务</h4>
                    </div>
                    <p style="color: ${getStatusColor(status.backend_status)}; margin: 0;">
                        ${status.backend_message || '状态未知'}
                    </p>
                </div>

                <div class="status-card" style="background: var(--bg-white); border: 1px solid var(--border-light); border-radius: 8px; padding: 16px;">
                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <span style="font-size: 24px; margin-right: 12px;">${getStatusIcon(status.ai_framework_status)}</span>
                        <h4 style="color: var(--text-primary); margin: 0;">AI框架</h4>
                    </div>
                    <p style="color: ${getStatusColor(status.ai_framework_status)}; margin: 0;">
                        ${status.ai_framework_message || 'AutoGen框架状态未知'}
                    </p>
                </div>

                <div class="status-card" style="background: var(--bg-white); border: 1px solid var(--border-light); border-radius: 8px; padding: 16px;">
                    <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <span style="font-size: 24px; margin-right: 12px;">${getStatusIcon(status.deepseek_status)}</span>
                        <h4 style="color: var(--text-primary); margin: 0;">DeepSeek API</h4>
                    </div>
                    <p style="color: ${getStatusColor(status.deepseek_status)}; margin: 0;">
                        ${status.deepseek_message || 'API连接状态未知'}
                    </p>
                </div>
            </div>

            <div style="margin-top: 24px; background: var(--bg-light); padding: 16px; border-radius: 8px;">
                <h4 style="color: var(--text-primary); margin-bottom: 12px;">系统信息</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 12px;">
                    <div>
                        <strong>系统版本:</strong><br>
                        <span style="color: var(--text-secondary);">${status.version || '未知'}</span>
                    </div>
                    <div>
                        <strong>运行时间:</strong><br>
                        <span style="color: var(--text-secondary);">${status.uptime || '未知'}</span>
                    </div>
                    <div>
                        <strong>最后检查:</strong><br>
                        <span style="color: var(--text-secondary);">${new Date().toLocaleString()}</span>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    renderSystemError(container, error) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: var(--error-red);">
                <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
                <h3>系统状态检查失败</h3>
                <p style="margin: 16px 0;">${error.message}</p>
                <button class="btn btn-primary" onclick="systemModule.checkSystemHealth()" style="margin-top: 16px;">
                    <span>🔄</span>
                    重新检查
                </button>
            </div>
        `;
    }

    async loadPrompts() {
        try {
            const response = await fetch('/api/prompts/default');
            const data = await response.json();

            if (data.status === 'success') {
                // 填充提示词到文本框
                document.getElementById('requirementAnalystPrompt').value = data.prompts.requirement_analyst;
                document.getElementById('dataTestExpertPrompt').value = data.prompts.data_test_expert;
                document.getElementById('documentAnalystPrompt').value = data.prompts.document_analyst;
                document.getElementById('requirementUnderstandingPrompt').value = data.prompts.requirement_understanding;

                this.addPromptLog('✅ 提示词加载成功', 'success');
            } else {
                this.addPromptLog(`❌ 加载失败: ${data.message}`, 'error');
            }
        } catch (error) {
            this.addPromptLog(`❌ 加载失败: ${error.message}`, 'error');
        }
    }

    async handlePromptSubmit(e) {
        e.preventDefault();

        const formData = new FormData();
        formData.append('requirement_analyst', document.getElementById('requirementAnalystPrompt').value);
        formData.append('data_test_expert', document.getElementById('dataTestExpertPrompt').value);
        formData.append('document_analyst', document.getElementById('documentAnalystPrompt').value);
        formData.append('requirement_understanding', document.getElementById('requirementUnderstandingPrompt').value);

        try {
            this.addPromptLog('🔄 正在保存提示词...', 'info');

            const response = await fetch('/api/prompts', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.addPromptLog('✅ 提示词保存成功', 'success');
                this.addPromptLog(`📝 ${data.message}`, 'info');
            } else {
                this.addPromptLog(`❌ 保存失败: ${data.message}`, 'error');
            }
        } catch (error) {
            this.addPromptLog(`❌ 保存失败: ${error.message}`, 'error');
        }
    }

    async resetPrompts() {
        if (!confirm('确定要重置所有提示词为默认值吗？')) {
            return;
        }

        try {
            const response = await fetch('/api/prompts/default');
            const data = await response.json();

            if (data.status === 'success') {
                // 填充默认提示词
                document.getElementById('requirementAnalystPrompt').value = data.prompts.requirement_analyst;
                document.getElementById('dataTestExpertPrompt').value = data.prompts.data_test_expert;
                document.getElementById('documentAnalystPrompt').value = data.prompts.document_analyst;
                document.getElementById('requirementUnderstandingPrompt').value = data.prompts.requirement_understanding;

                this.addPromptLog('🔄 提示词已重置为默认值', 'success');
            } else {
                this.addPromptLog(`❌ 重置失败: ${data.message}`, 'error');
            }
        } catch (error) {
            this.addPromptLog(`❌ 重置失败: ${error.message}`, 'error');
        }
    }

    addPromptLog(message, type = 'info') {
        const log = document.getElementById('promptLog');
        if (!log) return;

        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';

        const typeIcons = {
            'info': '💡',
            'success': '✅',
            'error': '❌'
        };

        logEntry.innerHTML = `
            <span class="log-timestamp">[${timestamp}]</span> 
            ${typeIcons[type] || '💡'} ${message}
        `;

        log.appendChild(logEntry);
        log.scrollTop = log.scrollHeight;
    }

    // 获取系统统计信息
    getSystemStats() {
        return {
            status: this.systemStatus,
            lastCheck: new Date().toISOString(),
            modules: {
                navigation: !!window.navigationModule,
                requirement: !!window.requirementModule,
                assets: !!window.requirementAssetsModule,
                testCase: !!window.testCaseModule,
                api: !!window.apiInterfaceModule
            }
        };
    }
}

// 全局实例
window.systemModule = new SystemModule();

// 向后兼容的全局函数
function checkSystemHealth() {
    window.systemModule.checkSystemHealth();
}

function resetPrompts() {
    window.systemModule.resetPrompts();
}

/**
 * 工具函数库
 * 提供通用的工具函数和辅助方法
 */

// ==================== 全局工具对象 ====================
window.Utils = {
    
    // ==================== 日期时间工具 ====================
    
    /**
     * 格式化日期时间
     * @param {string|Date} date - 日期对象或字符串
     * @param {string} format - 格式化模式
     * @returns {string} 格式化后的日期字符串
     */
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '-';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '-';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 获取相对时间描述
     * @param {string|Date} date - 日期
     * @returns {string} 相对时间描述
     */
    getRelativeTime(date) {
        if (!date) return '-';
        
        const now = new Date();
        const target = new Date(date);
        const diff = now - target;
        
        const minute = 60 * 1000;
        const hour = 60 * minute;
        const day = 24 * hour;
        
        if (diff < minute) {
            return '刚刚';
        } else if (diff < hour) {
            return `${Math.floor(diff / minute)}分钟前`;
        } else if (diff < day) {
            return `${Math.floor(diff / hour)}小时前`;
        } else if (diff < 7 * day) {
            return `${Math.floor(diff / day)}天前`;
        } else {
            return this.formatDate(date, 'YYYY-MM-DD');
        }
    },
    
    // ==================== 字符串工具 ====================
    
    /**
     * 截断文本
     * @param {string} text - 原始文本
     * @param {number} maxLength - 最大长度
     * @param {string} suffix - 后缀
     * @returns {string} 截断后的文本
     */
    truncateText(text, maxLength = 50, suffix = '...') {
        if (!text || text.length <= maxLength) return text || '';
        return text.substring(0, maxLength) + suffix;
    },
    
    /**
     * 转义HTML字符
     * @param {string} text - 原始文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },
    
    /**
     * 高亮关键词
     * @param {string} text - 原始文本
     * @param {string} keyword - 关键词
     * @returns {string} 高亮后的HTML
     */
    highlightKeyword(text, keyword) {
        if (!text || !keyword) return this.escapeHtml(text);
        
        const escapedText = this.escapeHtml(text);
        const escapedKeyword = this.escapeHtml(keyword);
        const regex = new RegExp(`(${escapedKeyword})`, 'gi');
        
        return escapedText.replace(regex, '<mark>$1</mark>');
    },
    
    // ==================== 数组和对象工具 ====================
    
    /**
     * 深拷贝对象
     * @param {any} obj - 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj);
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const cloned = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }
    },
    
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, delay = 300) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    },
    
    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 节流后的函数
     */
    throttle(func, delay = 300) {
        let lastCall = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    },
    
    // ==================== 文件工具 ====================
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (!bytes || bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 获取文件扩展名
     * @param {string} filename - 文件名
     * @returns {string} 文件扩展名
     */
    getFileExtension(filename) {
        if (!filename) return '';
        const lastDot = filename.lastIndexOf('.');
        return lastDot > 0 ? filename.substring(lastDot + 1).toLowerCase() : '';
    },
    
    /**
     * 获取文件图标类名
     * @param {string} filename - 文件名
     * @returns {string} 图标类名
     */
    getFileIcon(filename) {
        const ext = this.getFileExtension(filename);
        const iconMap = {
            'pdf': 'fas fa-file-pdf',
            'doc': 'fas fa-file-word',
            'docx': 'fas fa-file-word',
            'xls': 'fas fa-file-excel',
            'xlsx': 'fas fa-file-excel',
            'txt': 'fas fa-file-alt',
            'png': 'fas fa-file-image',
            'jpg': 'fas fa-file-image',
            'jpeg': 'fas fa-file-image',
            'gif': 'fas fa-file-image',
            'zip': 'fas fa-file-archive',
            'rar': 'fas fa-file-archive'
        };
        
        return iconMap[ext] || 'fas fa-file';
    },
    
    // ==================== DOM工具 ====================
    
    /**
     * 查询元素
     * @param {string} selector - 选择器
     * @param {Element} parent - 父元素
     * @returns {Element|null} 查询到的元素
     */
    $(selector, parent = document) {
        return parent.querySelector(selector);
    },
    
    /**
     * 查询所有元素
     * @param {string} selector - 选择器
     * @param {Element} parent - 父元素
     * @returns {NodeList} 查询到的元素列表
     */
    $$(selector, parent = document) {
        return parent.querySelectorAll(selector);
    },
    
    /**
     * 添加事件监听器
     * @param {Element|string} element - 元素或选择器
     * @param {string} event - 事件名
     * @param {Function} handler - 事件处理函数
     * @param {boolean} useCapture - 是否使用捕获
     */
    on(element, event, handler, useCapture = false) {
        const el = typeof element === 'string' ? this.$(element) : element;
        if (el) {
            el.addEventListener(event, handler, useCapture);
        }
    },
    
    /**
     * 移除事件监听器
     * @param {Element|string} element - 元素或选择器
     * @param {string} event - 事件名
     * @param {Function} handler - 事件处理函数
     */
    off(element, event, handler) {
        const el = typeof element === 'string' ? this.$(element) : element;
        if (el) {
            el.removeEventListener(event, handler);
        }
    },
    
    /**
     * 显示/隐藏元素
     * @param {Element|string} element - 元素或选择器
     * @param {boolean} show - 是否显示
     */
    toggle(element, show) {
        const el = typeof element === 'string' ? this.$(element) : element;
        if (el) {
            if (show === undefined) {
                el.classList.toggle('hidden');
            } else {
                el.classList.toggle('hidden', !show);
            }
        }
    },
    
    // ==================== 验证工具 ====================
    
    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    isValidEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    },
    
    /**
     * 验证手机号格式
     * @param {string} phone - 手机号
     * @returns {boolean} 是否有效
     */
    isValidPhone(phone) {
        const regex = /^1[3-9]\d{9}$/;
        return regex.test(phone);
    },
    
    /**
     * 验证必填字段
     * @param {any} value - 值
     * @returns {boolean} 是否有效
     */
    isRequired(value) {
        if (value === null || value === undefined) return false;
        if (typeof value === 'string') return value.trim().length > 0;
        if (Array.isArray(value)) return value.length > 0;
        return true;
    },
    
    // ==================== 消息提示工具 ====================
    
    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, warning, error, info)
     * @param {number} duration - 显示时长
     */
    showMessage(message, type = 'info', duration = 3000) {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.innerHTML = `
            <div class="message-content">
                <i class="message-icon ${this.getMessageIcon(type)}"></i>
                <span class="message-text">${this.escapeHtml(message)}</span>
                <button class="message-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // 添加样式
        this.addMessageStyles();
        
        // 添加到页面
        document.body.appendChild(messageEl);
        
        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                if (messageEl.parentElement) {
                    messageEl.remove();
                }
            }, duration);
        }
        
        return messageEl;
    },
    
    /**
     * 获取消息图标
     * @param {string} type - 消息类型
     * @returns {string} 图标类名
     */
    getMessageIcon(type) {
        const iconMap = {
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle',
            info: 'fas fa-info-circle'
        };
        return iconMap[type] || iconMap.info;
    },
    
    /**
     * 添加消息样式
     */
    addMessageStyles() {
        if (document.getElementById('message-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'message-styles';
        style.textContent = `
            .message {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                max-width: 500px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                animation: slideInRight 0.3s ease;
            }
            
            .message-content {
                display: flex;
                align-items: center;
                padding: 16px;
                gap: 12px;
            }
            
            .message-icon {
                font-size: 18px;
                flex-shrink: 0;
            }
            
            .message-text {
                flex: 1;
                font-size: 14px;
                line-height: 1.4;
            }
            
            .message-close {
                background: none;
                border: none;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                opacity: 0.6;
                transition: opacity 0.2s;
            }
            
            .message-close:hover {
                opacity: 1;
            }
            
            .message-success {
                border-left: 4px solid #4caf50;
            }
            
            .message-success .message-icon {
                color: #4caf50;
            }
            
            .message-warning {
                border-left: 4px solid #ff9800;
            }
            
            .message-warning .message-icon {
                color: #ff9800;
            }
            
            .message-error {
                border-left: 4px solid #f44336;
            }
            
            .message-error .message-icon {
                color: #f44336;
            }
            
            .message-info {
                border-left: 4px solid #2196f3;
            }
            
            .message-info .message-icon {
                color: #2196f3;
            }
            
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        
        document.head.appendChild(style);
    },
    
    // ==================== 便捷方法 ====================
    
    /**
     * 成功消息
     * @param {string} message - 消息内容
     */
    success(message) {
        this.showMessage(message, 'success');
    },
    
    /**
     * 警告消息
     * @param {string} message - 消息内容
     */
    warning(message) {
        this.showMessage(message, 'warning');
    },
    
    /**
     * 错误消息
     * @param {string} message - 消息内容
     */
    error(message) {
        this.showMessage(message, 'error');
    },
    
    /**
     * 信息消息
     * @param {string} message - 消息内容
     */
    info(message) {
        this.showMessage(message, 'info');
    }
};

// ==================== 全局快捷方式 ====================
window.$ = Utils.$;
window.$$ = Utils.$$;

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖修复脚本 - 解决AutoGen + OpenAI依赖问题
"""

import subprocess
import sys

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def main():
    print("🔧 修复AutoGen + DeepSeek依赖问题")
    print("=" * 40)
    
    # 必需的包列表
    packages = [
        "fastapi>=0.100.0",
        "uvicorn>=0.20.0",
        "python-multipart>=0.0.5",
        "pandas>=2.0.0",
        "openpyxl>=3.1.0",
        "requests>=2.28.0",
        "openai>=1.0.0",
        "Pillow>=9.0.0",
        "pyautogen>=0.2.0"
    ]
    
    success_count = 0
    
    for package in packages:
        print(f"📦 安装 {package}...")
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 40)
    print(f"📊 安装结果: {success_count}/{len(packages)} 成功")
    
    if success_count == len(packages):
        print("✅ 所有依赖安装成功！")
        print("🚀 现在可以运行: python start.py")
    else:
        print("⚠️ 部分依赖安装失败，但系统可能仍能运行")
        print("💡 建议手动安装失败的包")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据中台智能测试系统 - Windows兼容启动脚本
专门为Windows系统优化的启动脚本，解决编码问题
"""

import subprocess
import os
import sys
import time
import socket
import threading
from pathlib import Path
import logging

# 设置Windows控制台编码
if os.name == 'nt':
    os.system('chcp 65001 > nul')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class WindowsSystemStarter:
    """Windows系统启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_process = None
        self.frontend_process = None
        self.processes = []
    
    def check_port(self, port):
        """检查端口是否被占用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            return result == 0
        except:
            return False
    
    def kill_port_processes(self, port):
        """杀死占用端口的进程（Windows）"""
        try:
            # 查找占用端口的进程
            result = subprocess.run(
                f'netstat -ano | findstr :{port}',
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                pids = set()
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        if pid.isdigit():
                            pids.add(pid)
                
                # 杀死进程
                for pid in pids:
                    subprocess.run(f'taskkill /F /PID {pid}', shell=True, capture_output=True)
                    
            time.sleep(2)
        except Exception as e:
            logger.warning(f"清理端口 {port} 失败: {e}")
    
    def check_dependencies(self):
        """检查依赖"""
        logger.info("检查系统依赖...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            logger.error("Python版本过低，需要Python 3.8+")
            return False
        
        # 检查必要的包
        required_packages = ['fastapi', 'uvicorn', 'sqlalchemy', 'pymysql']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"缺少必要的Python包: {', '.join(missing_packages)}")
            logger.info("请运行: pip install -r backend/requirements.txt")
            return False
        
        logger.info("依赖检查通过")
        return True
    
    def init_database(self):
        """初始化数据库"""
        logger.info("初始化数据库...")
        
        try:
            # 运行数据库初始化脚本
            init_script = self.project_root / "database" / "init_database.py"
            if not init_script.exists():
                logger.warning("数据库初始化脚本不存在，跳过初始化")
                return True
            
            # 设置环境变量确保UTF-8编码
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            
            result = subprocess.run(
                [sys.executable, str(init_script)], 
                capture_output=True, 
                text=True, 
                timeout=60,
                env=env,
                encoding='utf-8'
            )
            
            if result.returncode == 0:
                logger.info("数据库初始化成功")
                return True
            else:
                logger.warning(f"数据库初始化失败: {result.stderr}")
                logger.info("继续启动系统...")
                return True
                
        except subprocess.TimeoutExpired:
            logger.warning("数据库初始化超时，继续启动系统...")
            return True
        except Exception as e:
            logger.warning(f"数据库初始化异常: {e}")
            return True
    
    def start_backend(self):
        """启动后端服务"""
        logger.info("启动后端服务...")
        
        # 清理端口
        if self.check_port(8000):
            logger.info("清理后端端口 8000...")
            self.kill_port_processes(8000)
        
        try:
            backend_dir = self.project_root / "backend"
            main_file = backend_dir / "main.py"
            
            if not main_file.exists():
                logger.error("后端主文件不存在: backend/main.py")
                return False
            
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            
            # 启动后端进程
            self.backend_process = subprocess.Popen(
                [sys.executable, "main.py"],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                env=env,
                encoding='utf-8'
            )
            
            self.processes.append(self.backend_process)
            
            # 等待后端启动
            logger.info("等待后端服务启动...")
            start_time = time.time()
            max_wait = 30
            
            while time.time() - start_time < max_wait:
                if self.backend_process.poll() is not None:
                    # 读取错误输出
                    try:
                        stdout, stderr = self.backend_process.communicate(timeout=1)
                        if stdout:
                            logger.error(f"后端标准输出: {stdout}")
                        if stderr:
                            logger.error(f"后端错误输出: {stderr}")
                    except:
                        pass
                    logger.error("后端进程意外退出")
                    return False

                if self.check_port(8000):
                    logger.info("后端服务启动成功: http://localhost:8000")
                    return True

                time.sleep(1)
            
            logger.error("后端服务启动超时")
            return False
            
        except Exception as e:
            logger.error(f"启动后端服务失败: {e}")
            return False
    
    def create_simple_frontend(self):
        """创建简单的前端页面"""
        static_dir = self.project_root / "static_frontend"
        static_dir.mkdir(exist_ok=True)
        
        # 创建简单的HTML页面
        html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中台智能测试系统</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            min-height: 100vh; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            text-align: center; 
            padding: 40px 20px; 
        }
        .title { 
            font-size: 3em; 
            margin-bottom: 20px; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .subtitle { 
            font-size: 1.2em; 
            margin-bottom: 40px; 
            opacity: 0.9; 
        }
        .cards { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin-top: 40px; 
        }
        .card { 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 10px; 
            backdrop-filter: blur(10px); 
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 { 
            margin-bottom: 15px; 
            font-size: 1.5em;
        }
        .link { 
            display: inline-block; 
            margin: 10px; 
            padding: 12px 24px; 
            background: rgba(255,255,255,0.2); 
            color: white; 
            text-decoration: none; 
            border-radius: 6px; 
            transition: all 0.3s; 
        }
        .link:hover { 
            background: rgba(255,255,255,0.3); 
            transform: translateY(-2px); 
        }
        .status { 
            margin-top: 30px; 
            padding: 20px; 
            background: rgba(255,255,255,0.1); 
            border-radius: 8px; 
        }
        .footer {
            margin-top: 40px;
            font-size: 0.9em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">数据中台智能测试系统</h1>
        <p class="subtitle">基于AutoGen + DeepSeek的智能需求分析平台</p>
        
        <div class="status">
            <h3>系统运行正常</h3>
            <p>前端和后端服务已启动</p>
        </div>
        
        <div class="cards">
            <div class="card">
                <h3>需求管理</h3>
                <p>创建、编辑和管理数据开发需求</p>
                <a href="http://localhost:8000/api/v1/requirements/" class="link">需求API</a>
            </div>
            <div class="card">
                <h3>AI分析</h3>
                <p>使用AutoGen + DeepSeek进行智能分析</p>
                <a href="http://localhost:8000/api/v1/requirements/analyses/" class="link">分析API</a>
            </div>
            <div class="card">
                <h3>API文档</h3>
                <p>查看完整的API接口文档</p>
                <a href="http://localhost:8000/docs" class="link">API文档</a>
            </div>
            <div class="card">
                <h3>系统监控</h3>
                <p>查看系统运行状态和健康检查</p>
                <a href="http://localhost:8000/health" class="link">健康检查</a>
            </div>
        </div>
        
        <div class="footer">
            <p>后端服务: <a href="http://localhost:8000" class="link">http://localhost:8000</a></p>
            <p>前端服务: <a href="http://localhost:3000" class="link">http://localhost:3000</a></p>
        </div>
    </div>
    
    <script>
        // 定期检查后端状态
        setInterval(async () => {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    console.log('后端服务正常');
                }
            } catch (error) {
                console.warn('后端服务连接失败');
            }
        }, 30000);
    </script>
</body>
</html>'''
        
        with open(static_dir / "index.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        
        return static_dir
    
    def start_frontend(self):
        """启动前端服务"""
        logger.info("启动前端服务...")
        
        # 清理端口
        if self.check_port(3000):
            logger.info("清理前端端口 3000...")
            self.kill_port_processes(3000)
        
        try:
            # 创建前端页面
            frontend_path = self.create_simple_frontend()
            
            # 启动HTTP服务器
            self.frontend_process = subprocess.Popen(
                [sys.executable, "-m", "http.server", "3000"],
                cwd=frontend_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.processes.append(self.frontend_process)
            
            # 等待前端启动
            time.sleep(3)
            
            if self.check_port(3000):
                logger.info("前端服务启动成功: http://localhost:3000")
                return True
            else:
                logger.error("前端服务启动失败")
                return False
                
        except Exception as e:
            logger.error(f"启动前端服务失败: {e}")
            return False
    
    def show_system_info(self):
        """显示系统信息"""
        print("=" * 60)
        print("数据中台智能测试系统启动完成!")
        print("=" * 60)
        print("访问地址:")
        print("  前端界面: http://localhost:3000")
        print("  后端API: http://localhost:8000")
        print("  API文档: http://localhost:8000/docs")
        print("  健康检查: http://localhost:8000/health")
        print("")
        print("系统功能:")
        print("  需求管理: 创建、编辑、查看需求")
        print("  AI分析: AutoGen + DeepSeek智能分析")
        print("  数据管理: MySQL数据库存储")
        print("  实时同步: 前后端数据实时交互")
        print("")
        print("注意事项:")
        print("  - 请确保MySQL服务正在运行")
        print("  - 首次使用请配置DeepSeek API密钥")
        print("  - 按 Ctrl+C 停止所有服务")
        print("=" * 60)
    
    def cleanup(self):
        """清理资源"""
        logger.info("正在停止所有服务...")
        
        for process in self.processes:
            if process and process.poll() is None:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                except:
                    pass
        
        logger.info("所有服务已停止")
    
    def start(self):
        """启动系统"""
        try:
            logger.info("开始启动数据中台智能测试系统...")
            
            # 1. 检查依赖
            if not self.check_dependencies():
                return False
            
            # 2. 初始化数据库
            if not self.init_database():
                return False
            
            # 3. 启动后端
            if not self.start_backend():
                return False
            
            # 4. 启动前端
            if not self.start_frontend():
                return False
            
            # 5. 显示系统信息
            self.show_system_info()
            
            # 6. 等待用户中断
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("\n用户请求停止系统")
            
            return True
            
        except Exception as e:
            logger.error(f"系统启动失败: {e}")
            return False
        
        finally:
            self.cleanup()

def main():
    """主函数"""
    starter = WindowsSystemStarter()
    success = starter.start()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

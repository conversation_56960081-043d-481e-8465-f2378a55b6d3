# ==================== FastAPI核心框架 ====================
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# ==================== 数据库相关 ====================
# MySQL数据库驱动和ORM
sqlalchemy==2.0.23
pymysql==1.1.0
aiomysql==0.2.0
alembic==1.12.1

# ==================== AI相关依赖 ====================
# AutoGen框架和OpenAI客户端
pyautogen==0.2.0
openai==1.3.7

# ==================== 数据处理和验证 ====================
# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# ==================== 文件处理 ====================
# 异步文件操作
aiofiles==23.2.1

# ==================== HTTP客户端 ====================
# HTTP请求库
requests==2.31.0
httpx==0.25.2

# ==================== 配置管理 ====================
# 环境变量管理
python-dotenv==1.0.0

# ==================== 日志处理 ====================
# 高性能日志库
loguru==0.7.2

# ==================== 模板引擎 ====================
# Jinja2模板引擎
jinja2==3.1.2

# ==================== 开发和测试工具 ====================
# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1

# 代码格式化和检查
black==23.11.0
flake8==6.1.0

# ==================== 安全相关 ====================
# 密码哈希和JWT
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# ==================== 其他工具库 ====================
# 时间处理
python-dateutil==2.8.2

# JSON处理增强
orjson==3.9.10

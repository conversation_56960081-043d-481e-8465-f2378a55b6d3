#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接管理模块
负责MySQL数据库的连接、会话管理和健康检查
"""

import logging
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager
from sqlalchemy import create_engine, text, event
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from ..core.config import settings
from ..models.requirement import Base

# 配置日志
logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    数据库管理器类
    负责数据库连接的创建、管理和维护
    """
    
    def __init__(self):
        """初始化数据库管理器"""
        self.sync_engine = None
        self.async_engine = None
        self.sync_session_factory = None
        self.async_session_factory = None
        self._initialized = False
        
        logger.info("数据库管理器初始化")
    
    def initialize(self) -> bool:
        """
        初始化数据库连接和会话工厂
        
        Returns:
            bool: 初始化是否成功
        """
        if self._initialized:
            logger.info("数据库已初始化，跳过重复初始化")
            return True
        
        try:
            # 创建同步数据库引擎
            self.sync_engine = create_engine(
                settings.database_url,
                # 移除poolclass，让SQLAlchemy自动选择合适的连接池
                pool_size=settings.DB_POOL_SIZE,
                max_overflow=settings.DB_MAX_OVERFLOW,
                pool_timeout=settings.DB_POOL_TIMEOUT,
                pool_recycle=settings.DB_POOL_RECYCLE,
                pool_pre_ping=True,  # 连接前检查连接有效性
                echo=settings.DEBUG,  # 调试模式下打印SQL
                echo_pool=settings.DEBUG,  # 调试模式下打印连接池信息
                connect_args={
                    "charset": settings.MYSQL_CHARSET,
                    "autocommit": False
                }
            )
            
            # 创建异步数据库引擎 - 移除QueuePool，让SQLAlchemy自动选择
            self.async_engine = create_async_engine(
                settings.async_database_url,
                # 不指定poolclass，让SQLAlchemy为异步引擎自动选择合适的连接池
                pool_size=settings.DB_POOL_SIZE,
                max_overflow=settings.DB_MAX_OVERFLOW,
                pool_timeout=settings.DB_POOL_TIMEOUT,
                pool_recycle=settings.DB_POOL_RECYCLE,
                pool_pre_ping=True,
                echo=settings.DEBUG,
                echo_pool=settings.DEBUG,
                connect_args={
                    "charset": settings.MYSQL_CHARSET,
                    "autocommit": False
                }
            )
            
            # 创建同步会话工厂
            self.sync_session_factory = sessionmaker(
                bind=self.sync_engine,
                class_=Session,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False
            )
            
            # 创建异步会话工厂
            self.async_session_factory = async_sessionmaker(
                bind=self.async_engine,
                class_=AsyncSession,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False
            )
            
            # 添加连接事件监听器
            self._setup_event_listeners()
            
            self._initialized = True
            logger.info("数据库连接初始化成功")
            return True

        except Exception as e:
            logger.error(f"数据库连接初始化失败: {e}")
            return False
    
    def _setup_event_listeners(self):
        """设置数据库连接事件监听器"""
        
        @event.listens_for(self.sync_engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """连接时设置数据库参数"""
            if hasattr(dbapi_connection, 'cursor'):
                cursor = dbapi_connection.cursor()
                # 设置MySQL会话参数
                cursor.execute("SET SESSION sql_mode='STRICT_TRANS_TABLES'")
                cursor.execute("SET SESSION time_zone='+08:00'")
                cursor.close()
        
        @event.listens_for(self.sync_engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """连接检出时的处理"""
            logger.debug("数据库连接检出")
        
        @event.listens_for(self.sync_engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """连接检入时的处理"""
            logger.debug("数据库连接检入")
    
    async def test_connection(self) -> bool:
        """
        测试数据库连接是否正常
        
        Returns:
            bool: 连接是否正常
        """
        if not self._initialized:
            logger.warning("数据库未初始化，无法测试连接")
            return False
        
        try:
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text("SELECT 1 as test"))
                test_value = result.scalar()
                
                if test_value == 1:
                    logger.info("数据库连接测试成功")
                    return True
                else:
                    logger.error("数据库连接测试失败：返回值异常")
                    return False

        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    async def create_tables(self) -> bool:
        """
        创建数据库表
        
        Returns:
            bool: 创建是否成功
        """
        if not self._initialized:
            logger.error("数据库未初始化，无法创建表")
            return False
        
        try:
            async with self.async_engine.begin() as conn:
                # 创建所有表
                await conn.run_sync(Base.metadata.create_all)
            
            logger.info("数据库表创建成功")
            return True

        except Exception as e:
            logger.error(f"数据库表创建失败: {e}")
            return False
    
    async def drop_tables(self) -> bool:
        """
        删除所有数据库表（谨慎使用）
        
        Returns:
            bool: 删除是否成功
        """
        if not self._initialized:
            logger.error("数据库未初始化，无法删除表")
            return False
        
        try:
            async with self.async_engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)
            
            logger.warning("所有数据库表已删除")
            return True

        except Exception as e:
            logger.error(f"数据库表删除失败: {e}")
            return False
    
    async def get_connection_info(self) -> dict:
        """
        获取数据库连接信息
        
        Returns:
            dict: 连接信息字典
        """
        if not self._initialized:
            return {"status": "未初始化"}
        
        try:
            pool = self.async_engine.pool
            return {
                "status": "已连接",
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid()
            }
        except Exception as e:
            return {"status": "错误", "error": str(e)}
    
    async def close(self):
        """关闭数据库连接"""
        try:
            if self.async_engine:
                await self.async_engine.dispose()
                logger.info("异步数据库引擎已关闭")
            
            if self.sync_engine:
                self.sync_engine.dispose()
                logger.info("同步数据库引擎已关闭")
            
            self._initialized = False
            logger.info("数据库连接已关闭")

        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")

# ==================== 全局数据库管理器实例 ====================
db_manager = DatabaseManager()

# ==================== 依赖注入函数 ====================
def get_sync_db() -> Session:
    """
    获取同步数据库会话（用于依赖注入）
    
    Yields:
        Session: 同步数据库会话
    """
    if not db_manager._initialized:
        db_manager.initialize()
    
    session = db_manager.sync_session_factory()
    try:
        yield session
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"数据库会话错误: {e}")
        raise
    finally:
        session.close()

@asynccontextmanager
async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取异步数据库会话（异步上下文管理器）
    
    Yields:
        AsyncSession: 异步数据库会话
    """
    if not db_manager._initialized:
        db_manager.initialize()
    
    async with db_manager.async_session_factory() as session:
        try:
            yield session
        except SQLAlchemyError as e:
            await session.rollback()
            logger.error(f"异步数据库会话错误: {e}")
            raise
        finally:
            await session.close()

# ==================== 初始化和健康检查函数 ====================
async def init_database() -> bool:
    """
    初始化数据库（包括连接和表创建）
    
    Returns:
        bool: 初始化是否成功
    """
    try:
        logger.info("开始初始化数据库...")

        # 初始化连接
        if not db_manager.initialize():
            return False

        # 测试连接
        if not await db_manager.test_connection():
            return False

        # 创建表
        if not await db_manager.create_tables():
            return False

        logger.info("数据库初始化完成")
        return True

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False

async def health_check() -> dict:
    """
    数据库健康检查
    
    Returns:
        dict: 健康检查结果
    """
    try:
        # 测试连接
        is_connected = await db_manager.test_connection()
        
        # 获取连接信息
        connection_info = await db_manager.get_connection_info()
        
        return {
            "database": {
                "status": "healthy" if is_connected else "unhealthy",
                "connected": is_connected,
                "connection_info": connection_info,
                "config": {
                    "host": settings.MYSQL_HOST,
                    "port": settings.MYSQL_PORT,
                    "database": settings.MYSQL_DATABASE,
                    "charset": settings.MYSQL_CHARSET
                }
            }
        }
        
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return {
            "database": {
                "status": "error",
                "connected": False,
                "error": str(e)
            }
        }

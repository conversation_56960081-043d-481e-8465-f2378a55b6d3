<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态文件测试 - 数据中台智能助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { border-color: #4CAF50; background: #f8fff8; }
        .error { border-color: #f44336; background: #fff8f8; }
        .loading { border-color: #2196F3; background: #f8f8ff; }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976D2; }
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 静态文件测试页面</h1>
        <p>测试前端JavaScript模块是否能正确加载</p>
        
        <div class="test-item loading" id="test-app">
            <h3>📦 主应用模块 (app.js)</h3>
            <p>状态: <span id="app-status">检测中...</span></p>
            <button onclick="testAppModule()">测试加载</button>
        </div>
        
        <div class="test-item loading" id="test-navigation">
            <h3>🧭 导航模块 (navigation.js)</h3>
            <p>状态: <span id="nav-status">检测中...</span></p>
            <button onclick="testNavigationModule()">测试加载</button>
        </div>
        
        <div class="test-item loading" id="test-requirement">
            <h3>🧠 需求理解模块 (requirement-understanding.js)</h3>
            <p>状态: <span id="req-status">检测中...</span></p>
            <button onclick="testRequirementModule()">测试加载</button>
        </div>
        
        <div class="test-item">
            <h3>🔗 API测试</h3>
            <button onclick="testHealthAPI()">测试健康检查API</button>
            <button onclick="testConfigAPI()">测试配置API</button>
        </div>
        
        <div class="test-item">
            <h3>📋 测试日志</h3>
            <div class="log" id="test-log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-item">
            <h3>🔗 快速链接</h3>
            <a href="/" target="_blank">主页面</a> |
            <a href="/docs" target="_blank">API文档</a> |
            <a href="/api/health" target="_blank">健康检查</a> |
            <a href="/api/config/api-key" target="_blank">API密钥状态</a>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }
        
        function setStatus(elementId, status, className) {
            const element = document.getElementById(elementId);
            const statusSpan = element.querySelector('span');
            statusSpan.textContent = status;
            element.className = `test-item ${className}`;
        }
        
        function testAppModule() {
            log('测试 app.js 模块加载...');
            
            const script = document.createElement('script');
            script.src = '/js/app.js';
            script.onload = function() {
                log('✅ app.js 加载成功');
                setStatus('test-app', '✅ 加载成功', 'success');
                
                // 检查全局对象
                if (window.app) {
                    log('✅ 全局 app 对象存在');
                } else {
                    log('⚠️ 全局 app 对象不存在');
                }
            };
            script.onerror = function() {
                log('❌ app.js 加载失败');
                setStatus('test-app', '❌ 加载失败', 'error');
            };
            document.head.appendChild(script);
        }
        
        function testNavigationModule() {
            log('测试 navigation.js 模块加载...');
            
            const script = document.createElement('script');
            script.src = '/js/modules/navigation.js';
            script.onload = function() {
                log('✅ navigation.js 加载成功');
                setStatus('test-navigation', '✅ 加载成功', 'success');
                
                if (window.navigationModule) {
                    log('✅ navigationModule 对象存在');
                } else {
                    log('⚠️ navigationModule 对象不存在');
                }
            };
            script.onerror = function() {
                log('❌ navigation.js 加载失败');
                setStatus('test-navigation', '❌ 加载失败', 'error');
            };
            document.head.appendChild(script);
        }
        
        function testRequirementModule() {
            log('测试 requirement-understanding.js 模块加载...');
            
            const script = document.createElement('script');
            script.src = '/js/modules/requirement-understanding.js';
            script.onload = function() {
                log('✅ requirement-understanding.js 加载成功');
                setStatus('test-requirement', '✅ 加载成功', 'success');
                
                if (window.requirementModule) {
                    log('✅ requirementModule 对象存在');
                } else {
                    log('⚠️ requirementModule 对象不存在');
                }
            };
            script.onerror = function() {
                log('❌ requirement-understanding.js 加载失败');
                setStatus('test-requirement', '❌ 加载失败', 'error');
            };
            document.head.appendChild(script);
        }
        
        async function testHealthAPI() {
            log('测试健康检查API...');
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                log('✅ 健康检查API响应成功');
                log(`📊 状态: ${data.overall_status}`);
            } catch (error) {
                log(`❌ 健康检查API失败: ${error.message}`);
            }
        }
        
        async function testConfigAPI() {
            log('测试配置API...');
            try {
                const response = await fetch('/api/config/api-key');
                const data = await response.json();
                log('✅ 配置API响应成功');
                log(`🔑 API密钥配置: ${data.api_key_config.is_configured ? '已配置' : '未配置'}`);
            } catch (error) {
                log(`❌ 配置API失败: ${error.message}`);
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            log('🚀 页面加载完成，开始自动测试...');
            
            // 延迟测试以避免并发问题
            setTimeout(testAppModule, 500);
            setTimeout(testNavigationModule, 1000);
            setTimeout(testRequirementModule, 1500);
            setTimeout(testHealthAPI, 2000);
            setTimeout(testConfigAPI, 2500);
        };
    </script>
</body>
</html>

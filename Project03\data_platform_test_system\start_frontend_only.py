#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门的前端启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """启动前端服务"""
    print("数据中台智能测试系统 - 前端启动器")
    print("=" * 40)
    
    project_root = Path(__file__).parent
    frontend_dir = project_root / "frontend"
    
    # 检查前端目录
    if not frontend_dir.exists():
        print(f"❌ 前端目录不存在: {frontend_dir}")
        return False
    
    if not (frontend_dir / "index.html").exists():
        print(f"❌ index.html不存在: {frontend_dir / 'index.html'}")
        return False
    
    print(f"✅ 前端目录: {frontend_dir}")
    print(f"✅ 启动文件: {frontend_dir / 'index.html'}")
    
    try:
        print("\n🚀 启动前端服务...")
        print(f"📁 工作目录: {frontend_dir}")
        print("🌐 服务地址: http://localhost:3000")
        print("⏹️ 按 Ctrl+C 停止服务")
        print("-" * 40)
        
        # 切换到前端目录
        os.chdir(frontend_dir)
        
        # 启动HTTP服务器
        subprocess.run([
            sys.executable, "-m", "http.server", "3000"
        ])
        
    except KeyboardInterrupt:
        print("\n✅ 前端服务已停止")
        return True
    except Exception as e:
        print(f"\n❌ 前端服务启动失败: {e}")
        return False

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试模块导入
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    try:
        print("🔍 测试基础导入...")

        # 测试基础模块
        from ai_generator import AITestCaseGenerator
        print("✅ ai_generator 导入成功")

        from prompts_config import get_prompts
        print("✅ prompts_config 导入成功")

        print("\n🔍 测试模块导入...")

        # 测试模块化组件
        from modules.requirement_understanding import RequirementUnderstandingModule
        print("✅ RequirementUnderstandingModule 导入成功")

        from modules.test_case_generation import TestCaseGenerationModule
        print("✅ TestCaseGenerationModule 导入成功")

        from modules.api_interface import APIInterfaceModule
        print("✅ APIInterfaceModule 导入成功")

        from modules.system_management import SystemManagementModule
        print("✅ SystemManagementModule 导入成功")

        print("\n🔍 测试模块初始化...")

        # 测试模块初始化
        ai_gen = AITestCaseGenerator()
        print("✅ AITestCaseGenerator 初始化成功")

        req_module = RequirementUnderstandingModule(ai_gen)
        print("✅ RequirementUnderstandingModule 初始化成功")

        test_module = TestCaseGenerationModule(ai_gen)
        print("✅ TestCaseGenerationModule 初始化成功")

        api_module = APIInterfaceModule()
        print("✅ APIInterfaceModule 初始化成功")

        sys_module = SystemManagementModule(ai_gen)
        print("✅ SystemManagementModule 初始化成功")

        print("\n🎉 所有模块导入和初始化成功！")
        return True

    except Exception as e:
        print(f"\n❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本 - 确保在端口8001启动
"""

import sys
import os
import time
import webbrowser
import threading
from pathlib import Path

def main():
    """主启动函数"""
    print("🚀 数据中台智能助手 - 快速启动")
    print("=" * 50)
    
    # 切换到backend目录
    backend_dir = Path(__file__).parent / "backend"
    if not backend_dir.exists():
        print("❌ 找不到backend目录")
        return False
    
    print(f"📁 切换到目录: {backend_dir}")
    os.chdir(backend_dir)
    
    # 添加backend目录到Python路径
    sys.path.insert(0, str(backend_dir))
    
    try:
        print("📦 导入模块...")
        from main import app
        import uvicorn
        
        print("🔧 配置服务器...")
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(3)
            print("🌐 正在打开浏览器...")
            webbrowser.open("http://localhost:8001")
            print("✅ 浏览器已打开")
        
        # 启动浏览器线程
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        print("✅ 系统启动成功!")
        print("📍 访问地址: http://localhost:8001")
        print("📚 API文档: http://localhost:8001/docs")
        print("🧪 调试页面: http://localhost:8001/debug")
        print("📖 AI说明: http://localhost:8001/ai-explanation")
        print("🤖 AI框架: AutoGen + DeepSeek (100%真实AI)")
        print("🔑 DeepSeek API: ***********************************")
        print("=" * 50)
        print("💡 提示: 按 Ctrl+C 停止服务")
        print("")
        
        # 启动服务器
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            reload=False,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()

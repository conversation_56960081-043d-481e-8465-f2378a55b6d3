# -*- coding: utf-8 -*-
"""
数据模型定义
使用Pydantic定义API请求和响应的数据结构
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field


class FileType(str, Enum):
    """文件类型枚举"""
    MINDMAP = "mindmap"
    FLOWCHART = "flowchart"
    SCREENSHOT = "screenshot"
    IMAGE = "image"


class TestCaseStatus(str, Enum):
    """测试用例生成状态枚举"""
    PENDING = "pending"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"


class TestCaseRequest(BaseModel):
    """测试用例生成请求"""
    project_name: str = Field(..., description="项目名称")
    requirements: str = Field(..., description="需求描述")
    context_info: Optional[str] = Field(None, description="上下文信息")
    file_type: FileType = Field(FileType.IMAGE, description="文件类型")
    additional_notes: Optional[str] = Field(None, description="额外说明")

    class Config:
        json_schema_extra = {
            "example": {
                "project_name": "数据中台测试项目",
                "requirements": "用户登录功能，包括用户名密码验证、记住登录状态等",
                "context_info": "这是一个Web应用的登录模块",
                "file_type": "screenshot",
                "additional_notes": "需要考虑安全性测试"
            }
        }


class TestCase(BaseModel):
    """测试用例数据模型"""
    case_id: str = Field(..., description="用例ID")
    title: str = Field(..., description="用例标题")
    description: str = Field(..., description="用例描述")
    preconditions: str = Field(..., description="前置条件")
    test_steps: List[str] = Field(..., description="测试步骤")
    expected_result: str = Field(..., description="预期结果")
    priority: str = Field("中", description="优先级：高/中/低")
    test_type: str = Field("功能测试", description="测试类型")
    module: str = Field("数据中台", description="所属模块")

    class Config:
        json_schema_extra = {
            "example": {
                "case_id": "TC_20240101_001",
                "title": "用户登录功能测试",
                "description": "验证用户使用正确的用户名和密码能够成功登录系统",
                "preconditions": "系统正常运行，用户账号已创建",
                "test_steps": [
                    "打开登录页面",
                    "输入正确的用户名",
                    "输入正确的密码",
                    "点击登录按钮"
                ],
                "expected_result": "用户成功登录，跳转到主页面",
                "priority": "高",
                "test_type": "功能测试",
                "module": "用户管理模块"
            }
        }


class TestCaseResponse(BaseModel):
    """测试用例生成响应"""
    task_id: str = Field(..., description="任务ID")
    status: TestCaseStatus = Field(..., description="生成状态")
    test_cases: List[TestCase] = Field(default=[], description="生成的测试用例列表")
    total_count: int = Field(0, description="测试用例总数")
    generated_at: datetime = Field(..., description="生成时间")

    class Config:
        json_schema_extra = {
            "example": {
                "task_id": "task_123456",
                "status": "completed",
                "test_cases": [],
                "total_count": 5,
                "generated_at": "2024-01-01T12:00:00"
            }
        }


class StreamResponse(BaseModel):
    """流式响应数据模型"""
    type: str = Field(..., description="响应类型：progress/testcase/complete/error")
    data: Dict[str, Any] = Field(..., description="响应数据")

    class Config:
        json_schema_extra = {
            "example": {
                "type": "progress",
                "data": {
                    "message": "正在生成测试用例...",
                    "progress": 50
                }
            }
        }


class UploadFileResponse(BaseModel):
    """文件上传响应"""
    file_id: str = Field(..., description="文件ID")
    filename: str = Field(..., description="原始文件名")
    file_path: str = Field(..., description="文件保存路径")
    file_size: int = Field(..., description="文件大小（字节）")
    file_type: str = Field(..., description="文件MIME类型")

    class Config:
        json_schema_extra = {
            "example": {
                "file_id": "file_123456",
                "filename": "mindmap.png",
                "file_path": "uploads/file_123456.png",
                "file_size": 1024000,
                "file_type": "image/png"
            }
        }


class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")

    class Config:
        json_schema_extra = {
            "example": {
                "error": "ValidationError",
                "message": "请求参数验证失败",
                "details": {
                    "field": "project_name",
                    "issue": "项目名称不能为空"
                }
            }
        }


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="版本号")
    services: Dict[str, str] = Field(..., description="各服务状态")

    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "version": "1.0.0",
                "services": {
                    "ai_service": "AutoGen running",
                    "excel_generator": "running",
                    "file_upload": "running"
                }
            }
        }


class ExportRequest(BaseModel):
    """导出请求"""
    task_id: str = Field(..., description="任务ID")
    format: str = Field("excel", description="导出格式")
    include_summary: bool = Field(True, description="是否包含汇总信息")

    class Config:
        json_schema_extra = {
            "example": {
                "task_id": "task_123456",
                "format": "excel",
                "include_summary": True
            }
        }


class ExportResponse(BaseModel):
    """导出响应"""
    file_url: str = Field(..., description="文件下载URL")
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小")
    generated_at: datetime = Field(..., description="生成时间")

    class Config:
        json_schema_extra = {
            "example": {
                "file_url": "/download/test_cases_20240101.xlsx",
                "filename": "test_cases_20240101.xlsx",
                "file_size": 2048000,
                "generated_at": "2024-01-01T12:00:00"
            }
        }


# 用于API文档的标签定义
TAGS_METADATA = [
    {
        "name": "health",
        "description": "健康检查和系统状态",
    },
    {
        "name": "upload",
        "description": "文件上传功能",
    },
    {
        "name": "generate",
        "description": "测试用例生成",
    },
    {
        "name": "export",
        "description": "文件导出和下载",
    },
]

/* ==================== 按钮组件 ==================== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.3s ease;
    white-space: nowrap;
    user-select: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    color: white;
    box-shadow: 0 2px 8px rgba(79, 195, 247, 0.3);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #29b6f6, #0288d1);
    box-shadow: 0 4px 12px rgba(79, 195, 247, 0.4);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #66bb6a, #4caf50);
    color: white;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    transform: translateY(-1px);
}

.btn-warning {
    background: linear-gradient(135deg, #ffb74d, #ff9800);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.btn-warning:hover:not(:disabled) {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #ef5350, #f44336);
    color: white;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
    transform: translateY(-1px);
}

.btn-outline {
    background: white;
    color: #666;
    border: 1px solid #ddd;
}

.btn-outline:hover:not(:disabled) {
    background: #f8f9fa;
    border-color: #4fc3f7;
    color: #4fc3f7;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-lg {
    padding: 14px 24px;
    font-size: 16px;
}

/* ==================== 表格组件 ==================== */
.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaed;
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background: #f8f9fa;
    color: #333;
    font-weight: 600;
    padding: 16px 12px;
    text-align: left;
    border-bottom: 2px solid #e8eaed;
    white-space: nowrap;
}

.data-table td {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.data-table tbody tr {
    transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.data-table tbody tr.selected {
    background-color: #e3f2fd;
}

/* 表格状态标签 */
.status-tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.status-tag.draft {
    background: #f5f5f5;
    color: #666;
}

.status-tag.pending {
    background: #fff3e0;
    color: #f57c00;
}

.status-tag.analyzing {
    background: #e3f2fd;
    color: #1976d2;
}

.status-tag.analyzed {
    background: #e8f5e8;
    color: #388e3c;
}

.status-tag.completed {
    background: #e8f5e8;
    color: #2e7d32;
}

/* 优先级标签 */
.priority-tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 40px;
}

.priority-tag.high {
    background: #ffebee;
    color: #d32f2f;
}

.priority-tag.medium {
    background: #fff3e0;
    color: #f57c00;
}

.priority-tag.low {
    background: #e8f5e8;
    color: #388e3c;
}

/* ==================== 分页组件 ==================== */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaed;
}

.pagination-info {
    margin-right: auto;
    color: #666;
    font-size: 14px;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.pagination-btn:hover:not(:disabled) {
    background: #f8f9fa;
    border-color: #4fc3f7;
    color: #4fc3f7;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: #4fc3f7;
    color: white;
    border-color: #4fc3f7;
}

/* ==================== 表单组件 ==================== */
.form-container {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaed;
}

.requirement-form {
    max-width: 800px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
    min-width: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-group label.required::after {
    content: ' *';
    color: #f44336;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4fc3f7;
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-help {
    margin-top: 4px;
    font-size: 12px;
    color: #666;
}

/* 模块标签输入 */
.module-tags {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 8px;
    min-height: 40px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    background: white;
}

.module-tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.module-tag .remove-tag {
    cursor: pointer;
    font-size: 10px;
    opacity: 0.7;
}

.module-tag .remove-tag:hover {
    opacity: 1;
}

.tag-input-container {
    flex: 1;
    min-width: 120px;
}

.tag-input-container input {
    border: none;
    outline: none;
    background: transparent;
    width: 100%;
    padding: 4px 0;
}

/* 文件上传 */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.upload-area:hover {
    border-color: #4fc3f7;
    background: #f8f9fa;
}

.upload-area.dragover {
    border-color: #4fc3f7;
    background: #e3f2fd;
}

.upload-placeholder i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 16px;
}

.upload-placeholder p {
    margin: 8px 0;
    color: #666;
}

.upload-hint {
    font-size: 12px;
    color: #999;
}

.upload-area input[type="file"] {
    display: none;
}

.uploaded-files {
    margin-top: 16px;
}

.uploaded-file {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
    border: 1px solid #e8eaed;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.file-icon {
    color: #4fc3f7;
}

.file-name {
    font-size: 14px;
    color: #333;
}

.file-size {
    font-size: 12px;
    color: #666;
}

.remove-file {
    color: #f44336;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.remove-file:hover {
    background: #ffebee;
}

/* ==================== 筛选栏 ==================== */
.filter-bar {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 20px;
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaed;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.filter-group label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.filter-group select,
.filter-group input {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-width: 120px;
}

/* ==================== 页面头部 ==================== */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e8eaed;
}

.page-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* ==================== 加载和模态框 ==================== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    background: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
    font-size: 32px;
    color: #4fc3f7;
    margin-bottom: 16px;
}

.loading-spinner p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* ==================== 响应式调整 ==================== */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 16px;
    }
    
    .filter-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .filter-group {
        justify-content: space-between;
    }
    
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .page-actions {
        justify-content: center;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .pagination-info {
        margin-right: 0;
        margin-bottom: 8px;
        width: 100%;
        text-align: center;
    }
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据中台智能测试系统 - 主应用入口
基于FastAPI + AutoGen + DeepSeek的智能需求分析平台
"""

import uvicorn
import logging
import sys
import os
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings, init_config
from app.database.connection import init_database, health_check, db_manager
from app.api.v1.requirements import router as requirements_router
from app.services.ai_service import check_ai_service_status

# ==================== 日志配置 ====================
def setup_logging():
    """配置应用日志"""
    logging.basicConfig(
        level=getattr(logging, settings.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f"{settings.LOG_DIR}/app.log", encoding='utf-8')
        ]
    )

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)

# ==================== 应用生命周期管理 ====================
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    处理启动和关闭时的初始化和清理工作
    """
    # ==================== 启动时执行 ====================
    logger.info("启动数据中台智能测试系统...")

    try:
        # 初始化配置
        logger.info("初始化系统配置...")
        if not init_config():
            raise Exception("系统配置初始化失败")

        # 初始化数据库
        logger.info("初始化数据库连接...")
        if not await init_database():
            raise Exception("数据库初始化失败")

        # 检查AI服务状态
        logger.info("检查AI服务状态...")
        ai_status = await check_ai_service_status()
        if ai_status['status'] == 'ready':
            logger.info("AI服务就绪")
        else:
            logger.warning("AI服务状态异常，部分功能可能受限")

        logger.info("系统启动完成")
        logger.info(f"系统信息:")
        logger.info(f"  - 应用名称: {settings.APP_NAME}")
        logger.info(f"  - 版本: {settings.APP_VERSION}")
        logger.info(f"  - 服务地址: http://{settings.HOST}:{settings.PORT}")
        logger.info(f"  - API文档: http://{settings.HOST}:{settings.PORT}/docs")
        logger.info(f"  - 数据库: {settings.MYSQL_HOST}:{settings.MYSQL_PORT}/{settings.MYSQL_DATABASE}")
        logger.info(f"  - AI模型: {settings.DEEPSEEK_MODEL}")

    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        raise
    
    yield
    
    # ==================== 关闭时执行 ====================
    logger.info("正在关闭系统...")

    try:
        # 关闭数据库连接
        await db_manager.close()
        logger.info("数据库连接已关闭")

    except Exception as e:
        logger.error(f"系统关闭时发生错误: {e}")

    logger.info("系统已关闭")

# ==================== 创建FastAPI应用 ====================
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description=settings.APP_DESCRIPTION,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url=f"{settings.API_V1_PREFIX}/openapi.json",
    lifespan=lifespan
)

# ==================== 中间件配置 ====================
# CORS跨域配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)

# ==================== 静态文件和模板配置 ====================
# 配置静态文件服务
frontend_static_path = "../frontend/static"
if os.path.exists(frontend_static_path):
    app.mount("/static", StaticFiles(directory=frontend_static_path), name="static")
    logger.info("静态文件服务已配置")

# 配置模板引擎
frontend_templates_path = "../frontend/templates"
templates = None
if os.path.exists(frontend_templates_path):
    templates = Jinja2Templates(directory=frontend_templates_path)
    logger.info("模板引擎已配置")

# ==================== 注册API路由 ====================
app.include_router(requirements_router, prefix=settings.API_V1_PREFIX)

# ==================== 核心API端点 ====================
@app.get("/health", summary="健康检查", tags=["系统监控"])
async def health_check_endpoint():
    """
    系统健康检查端点
    检查数据库连接、AI服务状态等关键组件
    """
    try:
        # 数据库健康检查
        db_health = await health_check()
        
        # AI服务健康检查
        ai_health = await check_ai_service_status()
        
        # 系统整体状态判断
        overall_status = "healthy"
        if not db_health.get("database", {}).get("connected", False):
            overall_status = "unhealthy"
        elif ai_health.get("status") != "ready":
            overall_status = "degraded"
        
        return {
            "status": overall_status,
            "timestamp": "2024-01-01T00:00:00Z",
            "version": settings.APP_VERSION,
            "services": {
                **db_health,
                "ai_service": ai_health
            },
            "config": {
                "debug_mode": settings.DEBUG,
                "api_prefix": settings.API_V1_PREFIX,
                "cors_origins": settings.CORS_ORIGINS
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": "2024-01-01T00:00:00Z",
                "error": str(e),
                "version": settings.APP_VERSION
            }
        )

@app.get("/", response_class=HTMLResponse, summary="系统首页", tags=["前端页面"])
async def root(request: Request):
    """
    系统首页
    如果有前端模板则渲染模板，否则返回简单的欢迎页面
    """
    try:
        # 如果有模板文件，使用模板渲染
        if templates and os.path.exists(f"{frontend_templates_path}/index.html"):
            return templates.TemplateResponse("index.html", {
                "request": request,
                "app_name": settings.APP_NAME,
                "app_version": settings.APP_VERSION
            })
        else:
            # 返回简单的HTML欢迎页面
            return HTMLResponse(content=f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{settings.APP_NAME}</title>
    <style>
        body {{ 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0; 
            padding: 0; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .container {{
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 60px 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            max-width: 600px;
        }}
        .title {{ 
            font-size: 2.8em; 
            margin-bottom: 20px; 
            font-weight: 700;
        }}
        .subtitle {{ 
            font-size: 1.3em; 
            margin-bottom: 40px; 
            opacity: 0.9; 
            line-height: 1.5;
        }}
        .links {{ 
            margin-top: 40px; 
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }}
        .link {{ 
            display: inline-block; 
            padding: 15px 25px; 
            background: rgba(255,255,255,0.2); 
            color: white; 
            text-decoration: none; 
            border-radius: 10px; 
            transition: all 0.3s;
            font-weight: 500;
            border: 1px solid rgba(255,255,255,0.3);
        }}
        .link:hover {{ 
            background: rgba(255,255,255,0.3); 
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }}
        .status {{
            margin-top: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }}
        .version {{
            margin-top: 20px;
            font-size: 0.9em;
            opacity: 0.7;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🚀 {settings.APP_NAME}</h1>
        <p class="subtitle">{settings.APP_DESCRIPTION}</p>
        
        <div class="status">
            <h3>✅ 后端服务正常运行</h3>
            <p>系统已就绪，可以开始使用</p>
        </div>
        
        <div class="links">
            <a href="/docs" class="link">📚 API文档</a>
            <a href="/health" class="link">🔍 健康检查</a>
            <a href="{settings.API_V1_PREFIX}/requirements/" class="link">📋 需求API</a>
            <a href="http://localhost:3000" class="link">🎨 前端界面</a>
        </div>
        
        <div class="version">
            <p>版本: {settings.APP_VERSION} | 调试模式: {'开启' if settings.DEBUG else '关闭'}</p>
            <p>前端界面: <a href="http://localhost:3000" style="color: #FFD700;">http://localhost:3000</a></p>
        </div>
    </div>
    
    <script>
        // 自动检查系统健康状态
        fetch('/health')
            .then(response => response.json())
            .then(data => {{
                console.log('系统状态:', data);
                if (data.status === 'healthy') {{
                    console.log('✅ 系统运行正常');
                }} else {{
                    console.warn('⚠️ 系统状态异常:', data.status);
                }}
            }})
            .catch(error => {{
                console.error('❌ 健康检查失败:', error);
            }});
    </script>
</body>
</html>
            """)
            
    except Exception as e:
        logger.error(f"首页渲染失败: {e}")
        return HTMLResponse(
            content=f"<h1>系统错误</h1><p>{str(e)}</p>",
            status_code=500
        )

# ==================== 全局异常处理 ====================
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    全局异常处理器
    捕获未处理的异常并返回友好的错误信息
    """
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    
    # 根据调试模式决定错误详情的显示
    error_detail = str(exc) if settings.DEBUG else "内部服务器错误，请联系系统管理员"
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "detail": error_detail,
            "path": str(request.url),
            "method": request.method,
            "timestamp": "2024-01-01T00:00:00Z"
        }
    )

# ==================== 启动函数 ====================
def start_server():
    """
    启动服务器
    使用uvicorn启动FastAPI应用
    """
    logger.info(f"准备启动服务器...")
    logger.info(f"服务地址: http://{settings.HOST}:{settings.PORT}")
    logger.info(f"API文档: http://{settings.HOST}:{settings.PORT}/docs")
    logger.info(f"调试模式: {'开启' if settings.DEBUG else '关闭'}")
    
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
        reload_dirs=["app"] if settings.DEBUG else None
    )

# ==================== 主程序入口 ====================
if __name__ == "__main__":
    start_server()

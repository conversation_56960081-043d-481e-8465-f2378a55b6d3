<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .nav {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .nav button {
            padding: 10px 20px;
            border: none;
            background: #007bff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }
        .nav button:hover {
            background: #0056b3;
        }
        .nav button.active {
            background: #28a745;
        }
        .page {
            display: none;
        }
        .page.active {
            display: block;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .table th {
            background: #f8f9fa;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>数据中台智能测试系统 - 简化测试</h1>
    
    <div class="nav">
        <button onclick="showPage('home')" id="homeBtn" class="active">首页</button>
        <button onclick="showPage('requirements')" id="requirementsBtn">需求列表</button>
        <button onclick="showPage('analysis')" id="analysisBtn">分析列表</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="container">
        <h3>调试日志</h3>
        <div id="debugLog" class="log">页面加载完成，等待操作...</div>
    </div>
    
    <div id="home" class="page active">
        <div class="container">
            <h2>系统首页</h2>
            <p>欢迎使用数据中台智能测试系统</p>
            <button onclick="testConnection()">测试后端连接</button>
            <div id="systemStatus"></div>
        </div>
    </div>
    
    <div id="requirements" class="page">
        <div class="container">
            <h2>需求列表测试</h2>
            <button onclick="loadRequirements()">加载需求数据</button>
            <div id="requirementsContent">
                <div class="loading">点击按钮加载需求列表</div>
            </div>
        </div>
    </div>
    
    <div id="analysis" class="page">
        <div class="container">
            <h2>分析列表测试</h2>
            <button onclick="loadAnalysis()">加载分析数据</button>
            <div id="analysisContent">
                <div class="loading">点击按钮加载分析列表</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        const API_PREFIX = '/api/v1';
        
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `\n[${timestamp}] ${message}`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('debugLog').textContent = '日志已清空';
        }
        
        // 页面切换
        function showPage(pageId) {
            log(`切换到页面: ${pageId}`);
            
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 隐藏所有按钮的active状态
            document.querySelectorAll('.nav button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            document.getElementById(pageId + 'Btn').classList.add('active');
        }
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            document.body.insertBefore(messageDiv, document.body.firstChild);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }
        
        // 测试连接
        async function testConnection() {
            log('开始测试后端连接...');
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                log(`健康检查响应状态: ${response.status}`);
                
                const data = await response.json();
                log(`健康检查响应数据: ${JSON.stringify(data, null, 2)}`);
                
                const statusDiv = document.getElementById('systemStatus');
                if (response.ok) {
                    statusDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 后端连接正常</h3>
                            <p>状态: ${data.status}</p>
                            <p>数据库: ${data.services?.database?.connected ? '正常' : '异常'}</p>
                            <p>AI服务: ${data.services?.ai_service?.status || '未知'}</p>
                        </div>
                    `;
                    showMessage('后端连接测试成功', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`连接测试失败: ${error.message}`);
                const statusDiv = document.getElementById('systemStatus');
                statusDiv.innerHTML = `<div class="error">❌ 后端连接失败: ${error.message}</div>`;
                showMessage('后端连接测试失败', 'error');
            }
        }
        
        // 加载需求列表
        async function loadRequirements() {
            log('开始加载需求列表...');
            const container = document.getElementById('requirementsContent');
            container.innerHTML = '<div class="loading">正在加载需求列表...</div>';
            
            try {
                const url = `${API_BASE_URL}${API_PREFIX}/requirements/?page=1&page_size=10`;
                log(`请求URL: ${url}`);
                
                const response = await fetch(url);
                log(`需求API响应状态: ${response.status}`);
                
                const data = await response.json();
                log(`需求API响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (response.ok) {
                    if (data.requirements && data.requirements.length > 0) {
                        let html = `
                            <div class="success">✅ 需求列表加载成功</div>
                            <p><strong>共 ${data.total} 条需求</strong></p>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>标题</th>
                                        <th>优先级</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        data.requirements.forEach(req => {
                            html += `
                                <tr>
                                    <td>${req.id}</td>
                                    <td>${req.title}</td>
                                    <td>${req.priority}</td>
                                    <td>${req.status}</td>
                                    <td>${new Date(req.created_time).toLocaleString()}</td>
                                </tr>
                            `;
                        });
                        
                        html += '</tbody></table>';
                        container.innerHTML = html;
                        showMessage('需求列表加载成功', 'success');
                        log(`需求列表加载成功: ${data.requirements.length} 条记录`);
                    } else {
                        container.innerHTML = '<p>暂无需求数据</p>';
                        showMessage('暂无需求数据', 'info');
                        log('需求列表为空');
                    }
                } else {
                    throw new Error(`API错误: ${response.status}`);
                }
            } catch (error) {
                log(`加载需求列表失败: ${error.message}`);
                container.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
                showMessage('加载需求列表失败', 'error');
            }
        }
        
        // 加载分析列表
        async function loadAnalysis() {
            log('开始加载分析列表...');
            const container = document.getElementById('analysisContent');
            container.innerHTML = '<div class="loading">正在加载分析列表...</div>';
            
            try {
                const url = `${API_BASE_URL}${API_PREFIX}/requirements/analyses/?page=1&page_size=10`;
                log(`请求URL: ${url}`);
                
                const response = await fetch(url);
                log(`分析API响应状态: ${response.status}`);
                
                const data = await response.json();
                log(`分析API响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (response.ok) {
                    if (data.analyses && data.analyses.length > 0) {
                        let html = `
                            <div class="success">✅ 分析列表加载成功</div>
                            <p><strong>共 ${data.total} 条分析结果</strong></p>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>需求ID</th>
                                        <th>AI模型</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        data.analyses.forEach(analysis => {
                            html += `
                                <tr>
                                    <td>${analysis.id}</td>
                                    <td>${analysis.req_id}</td>
                                    <td>${analysis.ai_model}</td>
                                    <td>${analysis.analysis_status}</td>
                                    <td>${new Date(analysis.created_time).toLocaleString()}</td>
                                </tr>
                            `;
                        });
                        
                        html += '</tbody></table>';
                        container.innerHTML = html;
                        showMessage('分析列表加载成功', 'success');
                        log(`分析列表加载成功: ${data.analyses.length} 条记录`);
                    } else {
                        container.innerHTML = '<p>暂无分析数据</p>';
                        showMessage('暂无分析数据', 'info');
                        log('分析列表为空');
                    }
                } else {
                    throw new Error(`API错误: ${response.status}`);
                }
            } catch (error) {
                log(`加载分析列表失败: ${error.message}`);
                container.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
                showMessage('加载分析列表失败', 'error');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            log('页面加载完成，可以开始测试');
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>前端调试页面</h1>
    
    <div class="section">
        <h2>API连接测试</h2>
        <button onclick="testConnection()">测试连接</button>
        <button onclick="testRequirementsAPI()">测试需求API</button>
        <button onclick="testAnalysisAPI()">测试分析API</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="connectionResults"></div>
    </div>
    
    <div class="section">
        <h2>需求数据</h2>
        <button onclick="loadRequirements()">加载需求列表</button>
        <div id="requirementsData"></div>
    </div>
    
    <div class="section">
        <h2>分析数据</h2>
        <button onclick="loadAnalyses()">加载分析列表</button>
        <div id="analysisData"></div>
    </div>
    
    <div class="section">
        <h2>详细日志</h2>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        const API_PREFIX = '/api/v1';
        
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('debugLog').textContent = '';
            document.getElementById('connectionResults').innerHTML = '';
            document.getElementById('requirementsData').innerHTML = '';
            document.getElementById('analysisData').innerHTML = '';
        }
        
        async function testConnection() {
            log('测试基础连接...');
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('connectionResults', '✅ 后端连接正常', 'success');
                    log(`连接成功: ${JSON.stringify(data)}`);
                } else {
                    showResult('connectionResults', `❌ 连接失败: ${response.status}`, 'error');
                    log(`连接失败: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                showResult('connectionResults', `❌ 连接异常: ${error.message}`, 'error');
                log(`连接异常: ${error.message}`);
            }
        }
        
        async function testRequirementsAPI() {
            log('测试需求API...');
            try {
                const url = `${API_BASE_URL}${API_PREFIX}/requirements/?page=1&page_size=5`;
                log(`请求URL: ${url}`);
                
                const response = await fetch(url);
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (response.ok) {
                    showResult('connectionResults', `✅ 需求API正常，共 ${data.total} 条`, 'success');
                } else {
                    showResult('connectionResults', `❌ 需求API失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('connectionResults', `❌ 需求API异常: ${error.message}`, 'error');
                log(`需求API异常: ${error.message}`);
            }
        }
        
        async function testAnalysisAPI() {
            log('测试分析API...');
            try {
                const url = `${API_BASE_URL}${API_PREFIX}/requirements/analyses/?page=1&page_size=5`;
                log(`请求URL: ${url}`);
                
                const response = await fetch(url);
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (response.ok) {
                    showResult('connectionResults', `✅ 分析API正常，共 ${data.total} 条`, 'success');
                } else {
                    showResult('connectionResults', `❌ 分析API失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('connectionResults', `❌ 分析API异常: ${error.message}`, 'error');
                log(`分析API异常: ${error.message}`);
            }
        }
        
        async function loadRequirements() {
            log('加载需求列表...');
            try {
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/?page=1&page_size=10`);
                const data = await response.json();
                
                if (response.ok && data.requirements) {
                    const container = document.getElementById('requirementsData');
                    
                    let html = `<h3>需求列表 (共 ${data.total} 条)</h3>`;
                    
                    if (data.requirements.length > 0) {
                        html += `
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>标题</th>
                                        <th>优先级</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        data.requirements.forEach(req => {
                            html += `
                                <tr>
                                    <td>${req.id}</td>
                                    <td>${req.title}</td>
                                    <td>${req.priority}</td>
                                    <td>${req.status}</td>
                                    <td>${new Date(req.created_time).toLocaleString()}</td>
                                </tr>
                            `;
                        });
                        
                        html += `
                                </tbody>
                            </table>
                        `;
                    } else {
                        html += '<p>暂无需求数据</p>';
                    }
                    
                    container.innerHTML = html;
                    log(`需求列表加载成功: ${data.requirements.length} 条记录`);
                } else {
                    document.getElementById('requirementsData').innerHTML = '<p class="result error">需求列表加载失败</p>';
                    log(`需求列表加载失败: ${response.status}`);
                }
            } catch (error) {
                document.getElementById('requirementsData').innerHTML = '<p class="result error">需求列表加载异常</p>';
                log(`需求列表加载异常: ${error.message}`);
            }
        }
        
        async function loadAnalyses() {
            log('加载分析列表...');
            try {
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/analyses/?page=1&page_size=10`);
                const data = await response.json();
                
                if (response.ok && data.analyses) {
                    const container = document.getElementById('analysisData');
                    
                    let html = `<h3>分析列表 (共 ${data.total} 条)</h3>`;
                    
                    if (data.analyses.length > 0) {
                        html += `
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>需求ID</th>
                                        <th>AI模型</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        data.analyses.forEach(analysis => {
                            html += `
                                <tr>
                                    <td>${analysis.id}</td>
                                    <td>${analysis.req_id}</td>
                                    <td>${analysis.ai_model}</td>
                                    <td>${analysis.analysis_status}</td>
                                    <td>${new Date(analysis.created_time).toLocaleString()}</td>
                                </tr>
                            `;
                        });
                        
                        html += `
                                </tbody>
                            </table>
                        `;
                    } else {
                        html += '<p>暂无分析数据</p>';
                    }
                    
                    container.innerHTML = html;
                    log(`分析列表加载成功: ${data.analyses.length} 条记录`);
                } else {
                    document.getElementById('analysisData').innerHTML = '<p class="result error">分析列表加载失败</p>';
                    log(`分析列表加载失败: ${response.status}`);
                }
            } catch (error) {
                document.getElementById('analysisData').innerHTML = '<p class="result error">分析列表加载异常</p>';
                log(`分析列表加载异常: ${error.message}`);
            }
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动测试...');
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>

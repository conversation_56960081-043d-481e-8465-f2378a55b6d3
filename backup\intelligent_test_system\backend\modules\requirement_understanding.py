"""
需求理解模块 - 负责需求智能理解功能
"""

import json
import base64
import uuid
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastapi import UploadFile
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ai_generator import AITestCaseGenerator


class RequirementUnderstandingModule:
    """需求理解模块"""
    
    def __init__(self, ai_generator: AITestCaseGenerator):
        self.ai_generator = ai_generator
        self.active_tasks = {}
    
    async def process_requirement_understanding(
        self,
        project_name: str,
        requirements: str,
        context_info: str = "",
        files: List[UploadFile] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        处理需求理解请求
        
        Args:
            project_name: 项目名称
            requirements: 需求描述
            context_info: 技术上下文
            files: 上传的文件列表
            
        Yields:
            Dict[str, Any]: 流式响应数据
        """
        task_id = str(uuid.uuid4())
        self.active_tasks[task_id] = {
            'status': 'processing',
            'start_time': datetime.now(),
            'project_name': project_name
        }
        
        try:
            # 发送开始信息
            yield {
                "type": "info",
                "message": f"🧠 开始需求智能理解分析...",
                "progress": 0,
                "task_id": task_id
            }
            
            # 处理上传的文件
            file_contents = []
            if files:
                yield {
                    "type": "progress",
                    "message": f"📁 处理上传文件 ({len(files)} 个)...",
                    "progress": 10
                }
                
                file_contents = await self._process_uploaded_files(files)
            
            # 构建完整的提示词
            yield {
                "type": "progress",
                "message": "🔧 构建AI提示词...",
                "progress": 20
            }
            
            full_prompt = self._build_requirement_prompt(
                project_name, requirements, context_info, file_contents
            )
            
            # 显示提示词
            yield {
                "type": "prompt",
                "message": "📝 需求智能理解专家提示词",
                "agent": "requirement_understanding",
                "prompt": full_prompt
            }
            
            # 调用AI进行需求理解
            yield {
                "type": "progress",
                "message": "🤖 AI专家分析中...",
                "progress": 30,
                "agent": "requirement_understanding"
            }
            
            try:
                ai_response = await self._call_ai_for_understanding(full_prompt)
                
                # 显示AI响应
                yield {
                    "type": "ai_response",
                    "message": "🤖 需求智能理解专家AI响应",
                    "agent": "requirement_understanding",
                    "response": ai_response
                }
                
                yield {
                    "type": "progress",
                    "message": "📊 解析结构化需求...",
                    "progress": 80
                }
                
                # 解析结构化需求
                structured_requirements = self._parse_and_structure_requirements(
                    ai_response, project_name
                )
                
                # 检查解析结果
                if isinstance(structured_requirements, dict) and "error" in structured_requirements:
                    yield {
                        "type": "error",
                        "message": f"❌ {structured_requirements['message']}",
                        "progress": 80
                    }
                    yield {
                        "type": "complete",
                        "message": "需求理解完成，但解析失败",
                        "progress": 100,
                        "result": {
                            "task_id": task_id,
                            "error": True,
                            "error_details": structured_requirements
                        }
                    }
                    return
                
                # 构建完整结果
                result = {
                    "task_id": task_id,
                    "project_name": project_name,
                    "requirements": requirements,
                    "context_info": context_info,
                    "structured_requirements": structured_requirements,
                    "ai_framework": "AutoGen + DeepSeek",
                    "generation_time": datetime.now().isoformat(),
                    "file_count": len(files) if files else 0
                }
                
                # 保存任务结果
                self.active_tasks[task_id].update({
                    'status': 'completed',
                    'result': result,
                    'end_time': datetime.now()
                })
                
                yield {
                    "type": "progress",
                    "message": "✅ 需求理解分析完成",
                    "progress": 100
                }
                
                yield {
                    "type": "complete",
                    "message": "🎉 需求智能理解成功完成！",
                    "progress": 100,
                    "result": result
                }
                
            except Exception as ai_error:
                print(f"[ERROR] 需求理解专家调用失败: {ai_error}")
                yield {
                    "type": "error",
                    "message": f"⚠️ DeepSeek API调用失败: {str(ai_error)}",
                    "progress": 75,
                    "agent": "requirement_understanding"
                }
                yield {
                    "type": "error",
                    "message": "需求智能理解失败，请检查网络连接和API配置",
                    "progress": 0
                }
                return
                
        except Exception as e:
            print(f"[ERROR] 需求理解处理失败: {e}")
            yield {
                "type": "error",
                "message": f"处理失败: {str(e)}",
                "progress": 0
            }
        finally:
            # 清理任务状态
            if task_id in self.active_tasks:
                if self.active_tasks[task_id]['status'] == 'processing':
                    self.active_tasks[task_id]['status'] = 'failed'
    
    async def _process_uploaded_files(self, files: List[UploadFile]) -> List[Dict[str, Any]]:
        """处理上传的文件"""
        file_contents = []
        
        for file in files:
            try:
                content = await file.read()
                
                # 检查是否是图片文件
                if file.content_type and file.content_type.startswith('image/'):
                    # 图片文件转换为base64
                    base64_content = base64.b64encode(content).decode('utf-8')
                    file_contents.append({
                        "filename": file.filename,
                        "type": "image",
                        "content": base64_content,
                        "mime_type": file.content_type
                    })
                else:
                    # 其他文件类型暂时只记录文件名和大小
                    file_contents.append({
                        "filename": file.filename,
                        "type": "document",
                        "size": len(content),
                        "mime_type": file.content_type
                    })
                    
            except Exception as e:
                print(f"处理文件 {file.filename} 失败: {e}")
                continue
        
        return file_contents
    
    def _build_requirement_prompt(
        self, 
        project_name: str, 
        requirements: str, 
        context_info: str, 
        file_contents: List[Dict[str, Any]]
    ) -> str:
        """构建需求理解的完整提示词"""
        
        # 获取基础提示词
        from prompts_config import REQUIREMENT_UNDERSTANDING_PROMPT
        base_prompt = REQUIREMENT_UNDERSTANDING_PROMPT
        
        # 构建项目信息
        project_info = f"""
## 项目信息
**项目名称**: {project_name}

**需求描述**:
{requirements}
"""
        
        if context_info:
            project_info += f"""
**技术上下文**:
{context_info}
"""
        
        # 构建文件信息
        file_info = ""
        if file_contents:
            file_info = "\n## 上传文件信息\n"
            for i, file_data in enumerate(file_contents, 1):
                file_info += f"{i}. **{file_data['filename']}** ({file_data['type']})\n"
                if file_data['type'] == 'image':
                    file_info += f"   - 图片内容: [base64编码的图片数据]\n"
                else:
                    file_info += f"   - 文件大小: {file_data.get('size', 0)} bytes\n"
        
        # 组合完整提示词
        full_prompt = f"{base_prompt}\n{project_info}\n{file_info}"
        
        return full_prompt
    
    async def _call_ai_for_understanding(self, prompt: str) -> str:
        """调用真实AutoGen + DeepSeek进行需求理解"""
        try:
            # 使用AutoGen的generate_reply方法调用DeepSeek
            response = await asyncio.to_thread(
                self.ai_generator.requirement_understanding.generate_reply,
                messages=[{"role": "user", "content": prompt}]
            )

            # 处理响应
            if hasattr(response, 'content'):
                return response.content
            elif isinstance(response, str):
                return response
            else:
                return str(response)

        except Exception as e:
            raise Exception(f"AutoGen + DeepSeek调用失败: {str(e)}")
    
    def _parse_and_structure_requirements(self, ai_response: str, project_name: str) -> Dict[str, Any]:
        """解析AI响应并结构化需求"""
        try:
            # 尝试从AI响应中提取JSON
            import re
            
            # 清理AI响应，移除可能的干扰内容
            cleaned_response = ai_response.strip()
            
            # 多种JSON提取策略，按优先级排序
            json_patterns = [
                # 1. markdown代码块中的JSON
                r'```json\s*(\{[\s\S]*?\})\s*```',
                r'```JSON\s*(\{[\s\S]*?\})\s*```',
                # 2. 普通代码块中的JSON
                r'```\s*(\{[\s\S]*?\})\s*```',
                # 3. 直接的JSON对象（最严格匹配）
                r'(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
                # 4. 更宽松的JSON匹配
                r'(\{[\s\S]*\})',
            ]
            
            json_str = None
            used_pattern = None
            
            for i, pattern in enumerate(json_patterns):
                matches = re.findall(pattern, cleaned_response, re.DOTALL)
                if matches:
                    # 如果有多个匹配，选择最长的（通常是最完整的）
                    json_str = max(matches, key=len) if isinstance(matches[0], str) else matches[0]
                    used_pattern = f"模式{i+1}"
                    print(f"[DEBUG] 使用{used_pattern}找到JSON，长度: {len(json_str)}")
                    print(f"[DEBUG] JSON前100字符: {json_str[:100]}...")
                    break
            
            if json_str:
                # 清理JSON字符串
                json_str = json_str.strip()
                
                # 尝试修复常见的JSON问题
                json_str = self._fix_common_json_issues(json_str)
                
                try:
                    structured_requirements = json.loads(json_str)
                    if isinstance(structured_requirements, dict):
                        print(f"[DEBUG] 成功解析结构化需求，包含 {len(structured_requirements)} 个顶级字段")
                        return structured_requirements
                    else:
                        print(f"[DEBUG] 解析结果不是对象: {type(structured_requirements)}")
                        
                except json.JSONDecodeError as je:
                    print(f"[DEBUG] JSON解析失败: {je}")
                    print(f"[DEBUG] 错误位置附近的内容: {json_str[max(0, je.pos-20):je.pos+20]}")
                    
                    # 尝试修复并重新解析
                    fixed_json = self._attempt_json_repair(json_str, je)
                    if fixed_json:
                        try:
                            structured_requirements = json.loads(fixed_json)
                            if isinstance(structured_requirements, dict):
                                print(f"[DEBUG] 修复后成功解析结构化需求")
                                return structured_requirements
                        except:
                            print(f"[DEBUG] 修复后仍然解析失败")
            else:
                print("[DEBUG] 未找到JSON格式的结构化需求")
                
        except Exception as e:
            print(f"❌ 解析结构化需求失败: {e}")
        
        # 如果解析失败，返回错误信息而不是默认模板
        print(f"[DEBUG] AI解析失败，返回解析错误信息")
        return {
            "error": "AI响应解析失败",
            "ai_response": ai_response[:1000] + "..." if len(ai_response) > 1000 else ai_response,
            "message": "无法从AI响应中提取有效的JSON格式结构化需求，请检查提示词配置或重试"
        }
    
    def _fix_common_json_issues(self, json_str: str) -> str:
        """修复常见的JSON格式问题"""
        import re
        
        # 移除可能的BOM标记
        json_str = json_str.lstrip('\ufeff')
        
        # 移除前后的非JSON内容
        json_str = json_str.strip()
        
        # 确保以{开始，以}结束
        start_idx = json_str.find('{')
        if start_idx > 0:
            json_str = json_str[start_idx:]
        
        end_idx = json_str.rfind('}')
        if end_idx > 0 and end_idx < len(json_str) - 1:
            json_str = json_str[:end_idx + 1]
        
        # 修复常见的引号问题
        json_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)
        
        # 修复尾随逗号
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
        
        return json_str
    
    def _attempt_json_repair(self, json_str: str, error: json.JSONDecodeError) -> Optional[str]:
        """尝试修复JSON解析错误"""
        try:
            # 如果错误是"Extra data"，尝试截取到第一个完整的JSON对象
            if "Extra data" in str(error):
                # 找到第一个完整的JSON对象
                brace_count = 0
                for i, char in enumerate(json_str):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            # 找到第一个完整的JSON对象
                            fixed_json = json_str[:i+1]
                            print(f"[DEBUG] 尝试修复：截取到位置 {i+1}")
                            return fixed_json
            
            # 如果错误在特定位置，尝试截取到错误位置之前
            if hasattr(error, 'pos') and error.pos > 0:
                # 向前查找最近的完整JSON结构
                pos = error.pos
                while pos > 0:
                    try:
                        test_json = json_str[:pos]
                        json.loads(test_json)
                        print(f"[DEBUG] 尝试修复：截取到位置 {pos}")
                        return test_json
                    except:
                        pos -= 1
                        
        except Exception as e:
            print(f"[DEBUG] JSON修复尝试失败: {e}")
        
        return None
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.active_tasks.get(task_id)
    
    def get_active_tasks(self) -> Dict[str, Any]:
        """获取所有活跃任务"""
        return self.active_tasks.copy()
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        current_time = datetime.now()
        tasks_to_remove = []
        
        for task_id, task_data in self.active_tasks.items():
            task_age = current_time - task_data['start_time']
            if task_age.total_seconds() > max_age_hours * 3600:
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.active_tasks[task_id]
        
        return len(tasks_to_remove)

    def get_module_stats(self) -> Dict[str, Any]:
        """获取模块统计信息"""
        total_tasks = len(self.active_tasks)
        completed_tasks = sum(1 for task in self.active_tasks.values() if task['status'] == 'completed')
        processing_tasks = sum(1 for task in self.active_tasks.values() if task['status'] == 'processing')
        failed_tasks = sum(1 for task in self.active_tasks.values() if task['status'] == 'failed')

        return {
            'module_name': 'RequirementUnderstanding',
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'processing_tasks': processing_tasks,
            'failed_tasks': failed_tasks,
            'success_rate': completed_tasks / total_tasks if total_tasks > 0 else 0
        }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端启动脚本
"""

import os
import sys
import socket
import subprocess
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
import threading

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result == 0
    except:
        return False

def kill_port_process(port):
    """杀死占用端口的进程"""
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(f'netstat -ano | findstr :{port}', shell=True, capture_output=True)
            subprocess.run(f'for /f "tokens=5" %a in (\'netstat -ano ^| findstr :{port}\') do taskkill /PID %a /F', shell=True)
        else:  # Linux/Mac
            subprocess.run(f'lsof -ti:{port} | xargs kill -9', shell=True)
        print(f"已清理端口 {port}")
    except Exception as e:
        print(f"清理端口失败: {e}")

class CustomHTTPRequestHandler(SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(Path(__file__).parent / "frontend"), **kwargs)
    
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # 简化日志输出
        print(f"[前端] {format % args}")

def start_frontend_server(port=3000):
    """启动前端服务器"""
    project_root = Path(__file__).parent
    frontend_dir = project_root / "frontend"
    
    if not frontend_dir.exists():
        print(f"❌ 前端目录不存在: {frontend_dir}")
        return False
    
    if not (frontend_dir / "index.html").exists():
        print(f"❌ index.html不存在: {frontend_dir / 'index.html'}")
        return False
    
    # 检查并清理端口
    if check_port(port):
        print(f"端口 {port} 被占用，尝试清理...")
        kill_port_process(port)
        
        # 等待端口释放
        import time
        for i in range(5):
            if not check_port(port):
                break
            time.sleep(1)
        
        if check_port(port):
            print(f"❌ 无法清理端口 {port}")
            return False
    
    try:
        # 切换到前端目录
        os.chdir(frontend_dir)
        
        # 创建HTTP服务器
        server = HTTPServer(('localhost', port), CustomHTTPRequestHandler)
        
        print(f"✅ 前端服务启动成功")
        print(f"📱 访问地址: http://localhost:{port}")
        print(f"📁 服务目录: {frontend_dir}")
        print("按 Ctrl+C 停止服务")
        
        # 启动服务器
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n正在停止前端服务...")
        server.shutdown()
        return True
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        return False

def main():
    """主函数"""
    print("前端服务启动器")
    print("=" * 40)
    
    # 检查前端文件
    project_root = Path(__file__).parent
    frontend_dir = project_root / "frontend"
    
    print(f"检查前端目录: {frontend_dir}")
    
    if not frontend_dir.exists():
        print("❌ 前端目录不存在")
        return False
    
    required_files = [
        "index.html",
        "static/js/utils.js",
        "static/js/api.js",
        "static/js/main.js"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = frontend_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺失文件: {missing_files}")
        print("请确保前端文件完整")
        return False
    
    print("\n所有文件检查通过，启动前端服务...")
    
    # 启动前端服务
    success = start_frontend_server(3000)
    
    if success:
        print("前端服务已停止")
    else:
        print("前端服务启动失败")
    
    return success

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
import http.server
import socketserver
import json
from urllib.parse import urlparse, parse_qs

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中台智能测试平台</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .status {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.2em;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }
        .feature-card h3 {
            color: #007bff;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .button-group {
            text-align: center;
            margin: 30px 0;
        }
        #result {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
            border-left: 5px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 数据中台智能测试平台</h1>
        
        <div class="status">
            ✅ 服务器运行正常 - 简化HTTP服务器版本
        </div>
        
        <div class="features">
            <div class="feature-card">
                <h3>🤖 AI智能生成</h3>
                <p>基于AutoGen + DeepSeek的智能测试用例生成，支持多模态输入，自动生成标准化测试用例。</p>
            </div>
            <div class="feature-card">
                <h3>🧠 需求理解</h3>
                <p>智能分析需求文档，自动提取输出字段和数据限制条件，支持在线编辑和交互操作。</p>
            </div>
            <div class="feature-card">
                <h3>📊 数据分析</h3>
                <p>实时数据监控和分析，提供丰富的图表展示和报告导出功能。</p>
            </div>
        </div>
        
        <div class="button-group">
            <button onclick="testJavaScript()">测试JavaScript</button>
            <button onclick="testAPI()">测试API</button>
            <button onclick="showInfo()">显示系统信息</button>
            <button onclick="location.reload()">刷新页面</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        console.log('页面加载完成 - 简化HTTP服务器版本');
        
        function testJavaScript() {
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.innerHTML = `
                <h4>✅ JavaScript测试成功</h4>
                <p><strong>当前时间:</strong> ${new Date().toLocaleString()}</p>
                <p><strong>浏览器:</strong> ${navigator.userAgent}</p>
                <p><strong>页面URL:</strong> ${window.location.href}</p>
            `;
        }
        
        function testAPI() {
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.innerHTML = '正在测试API...';
            
            fetch('/health')
                .then(response => response.json())
                .then(data => {
                    result.innerHTML = `
                        <h4>✅ API测试成功</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    result.innerHTML = `
                        <h4>⚠️ API测试说明</h4>
                        <p>当前使用简化HTTP服务器，不支持JSON API。</p>
                        <p>这是正常现象，页面功能正常。</p>
                        <p>错误信息: ${error.message}</p>
                    `;
                });
        }
        
        function showInfo() {
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.innerHTML = `
                <h4>📋 系统信息</h4>
                <p><strong>服务器类型:</strong> Python HTTP Server</p>
                <p><strong>端口:</strong> 8001</p>
                <p><strong>状态:</strong> 运行正常</p>
                <p><strong>说明:</strong> 这是一个简化版本，用于验证基本功能</p>
                <p><strong>完整版:</strong> 需要FastAPI环境支持</p>
            `;
        }
        
        // 页面加载完成后自动显示信息
        window.onload = function() {
            console.log('Window加载完成');
            setTimeout(() => {
                showInfo();
            }, 1000);
        };
    </script>
</body>
</html>
            """
            
            self.wfile.write(html_content.encode('utf-8'))
            
        elif parsed_path.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            health_data = {
                "status": "ok",
                "message": "简化HTTP服务器运行正常",
                "server_type": "Python HTTP Server",
                "port": 8001
            }
            
            self.wfile.write(json.dumps(health_data, ensure_ascii=False).encode('utf-8'))
            
        else:
            super().do_GET()

if __name__ == "__main__":
    PORT = 8001
    
    print("🚀 启动简化HTTP服务器...")
    print(f"📍 访问地址: http://localhost:{PORT}")
    print("💡 这是一个简化版本，用于验证基本功能")
    
    with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
        print(f"✅ 服务器已启动在端口 {PORT}")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")

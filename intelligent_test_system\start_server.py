#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能启动脚本 - 自动检测可用端口并启动服务器
"""

import socket
import subprocess
import sys
import time
from pathlib import Path

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=8000, max_attempts=10):
    """查找可用端口"""
    for port in range(start_port, start_port + max_attempts):
        if check_port_available(port):
            return port
    return None

def kill_process_on_port(port):
    """杀死占用指定端口的进程"""
    try:
        # Windows
        if sys.platform == "win32":
            # 查找占用端口的进程
            result = subprocess.run(
                f'netstat -ano | findstr :{port}',
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if f':{port}' in line and 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            print(f"🔍 发现进程 {pid} 占用端口 {port}")
                            
                            # 杀死进程
                            kill_result = subprocess.run(
                                f'taskkill /F /PID {pid}',
                                shell=True,
                                capture_output=True,
                                text=True
                            )
                            
                            if kill_result.returncode == 0:
                                print(f"✅ 成功杀死进程 {pid}")
                                time.sleep(1)  # 等待端口释放
                                return True
                            else:
                                print(f"❌ 无法杀死进程 {pid}: {kill_result.stderr}")
        
        # Linux/Mac
        else:
            result = subprocess.run(
                f'lsof -ti:{port}',
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                pid = result.stdout.strip()
                print(f"🔍 发现进程 {pid} 占用端口 {port}")
                
                kill_result = subprocess.run(
                    f'kill -9 {pid}',
                    shell=True,
                    capture_output=True,
                    text=True
                )
                
                if kill_result.returncode == 0:
                    print(f"✅ 成功杀死进程 {pid}")
                    time.sleep(1)
                    return True
                else:
                    print(f"❌ 无法杀死进程 {pid}")
                    
    except Exception as e:
        print(f"❌ 杀死进程时出错: {e}")
    
    return False

def start_server():
    """启动服务器"""
    print("🚀 数据中台智能助手启动器")
    print("=" * 50)
    
    # 检查工作目录
    backend_dir = Path(__file__).parent / "backend"
    if not backend_dir.exists():
        print("❌ 找不到backend目录")
        return False
    
    # 首选端口
    preferred_port = 8001
    
    print(f"🔍 检查端口 {preferred_port} 是否可用...")
    
    if not check_port_available(preferred_port):
        print(f"⚠️ 端口 {preferred_port} 被占用")
        
        # 尝试杀死占用进程
        print(f"🔧 尝试释放端口 {preferred_port}...")
        if kill_process_on_port(preferred_port):
            if check_port_available(preferred_port):
                print(f"✅ 端口 {preferred_port} 已释放")
            else:
                print(f"❌ 端口 {preferred_port} 仍被占用")
                preferred_port = None
        else:
            preferred_port = None
    
    # 如果首选端口不可用，查找其他端口
    if preferred_port is None:
        print("🔍 查找其他可用端口...")
        available_port = find_available_port(8002, 10)

        if available_port is None:
            print("❌ 找不到可用端口 (8002-8011)")
            return False
        
        preferred_port = available_port
        print(f"✅ 找到可用端口: {preferred_port}")
    
    # 修改main.py中的端口配置
    main_py_path = backend_dir / "main.py"
    if main_py_path.exists():
        try:
            with open(main_py_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换端口配置
            import re
            content = re.sub(
                r'port=\d+',
                f'port={preferred_port}',
                content
            )
            
            with open(main_py_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已更新端口配置为 {preferred_port}")
            
        except Exception as e:
            print(f"⚠️ 无法更新端口配置: {e}")
    
    # 启动服务器
    print(f"🚀 启动服务器在端口 {preferred_port}...")
    print(f"🌐 访问地址: http://localhost:{preferred_port}")
    print("💡 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        # 切换到backend目录
        import os
        os.chdir(backend_dir)
        
        # 启动服务器
        subprocess.run([
            sys.executable, "main.py"
        ], check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 服务器启动失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = start_server()
    if not success:
        print("\n❌ 启动失败，请检查错误信息")
        sys.exit(1)

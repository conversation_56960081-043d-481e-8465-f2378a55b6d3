#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
需求管理API路由
提供RESTful API接口，支持需求的CRUD操作和AI分析功能
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field, validator
from datetime import datetime

from ...database.connection import get_async_db
from ...services.requirement_service import requirement_service
from ...models.requirement import RequirementStatus, RequirementPriority
from ...core.config import settings

# 创建路由器
router = APIRouter(prefix="/requirements", tags=["需求管理"])

# ==================== Pydantic模型定义 ====================

class RequirementCreate(BaseModel):
    """创建需求的请求模型"""
    title: str = Field(..., min_length=1, max_length=255, description="需求标题（必填）")
    description: Optional[str] = Field(None, description="需求详细描述")
    priority: str = Field(..., description="优先级（高/中/低）")
    modules: Optional[List[str]] = Field(None, description="关联模块列表")
    created_by: Optional[str] = Field(None, max_length=100, description="创建者")
    attachments: Optional[List[Dict[str, Any]]] = Field(None, description="附件信息")
    
    @validator('priority')
    def validate_priority(cls, v):
        """验证优先级值"""
        if v not in ["高", "中", "低"]:
            raise ValueError('优先级必须是：高、中、低')
        return v

class RequirementUpdate(BaseModel):
    """更新需求的请求模型"""
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="需求标题")
    description: Optional[str] = Field(None, description="需求详细描述")
    priority: Optional[str] = Field(None, description="优先级（高/中/低）")
    status: Optional[str] = Field(None, description="需求状态")
    modules: Optional[List[str]] = Field(None, description="关联模块列表")
    attachments: Optional[List[Dict[str, Any]]] = Field(None, description="附件信息")
    
    @validator('priority')
    def validate_priority(cls, v):
        """验证优先级值"""
        if v is not None and v not in ["高", "中", "低"]:
            raise ValueError('优先级必须是：高、中、低')
        return v

class RequirementResponse(BaseModel):
    """需求响应模型"""
    id: int
    title: str
    description: Optional[str]
    priority: str
    status: str
    modules: Optional[List[str]]
    is_complete: bool
    missing_fields: Optional[List[str]]
    created_by: Optional[str]
    created_time: datetime
    updated_time: datetime
    attachments: Optional[List[Dict[str, Any]]]

class RequirementListResponse(BaseModel):
    """需求列表响应模型"""
    requirements: List[RequirementResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

class AnalysisRequest(BaseModel):
    """AI分析请求模型"""
    requirement_ids: List[int] = Field(..., min_items=1, max_items=5, description="需求ID列表（最多5个）")

class AnalysisResponse(BaseModel):
    """AI分析响应模型"""
    id: int
    req_id: int
    analysis_result: str
    structured_result: Dict[str, Any]
    key_points: List[str]
    suggestions: List[str]
    ai_model: str
    analysis_status: str
    is_manually_adjusted: bool
    manual_adjust: Optional[str]
    created_time: datetime

class ManualAdjustmentRequest(BaseModel):
    """人工调整请求模型"""
    manual_adjustment: str = Field(..., min_length=1, description="人工调整内容")
    adjusted_by: str = Field(..., min_length=1, max_length=100, description="调整者")

# ==================== API端点实现 ====================

@router.post("/", response_model=RequirementResponse, summary="创建需求")
async def create_requirement(
    requirement: RequirementCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """
    创建新的数据开发需求
    
    **请求参数：**
    - **title**: 需求标题（必填，1-255字符）
    - **description**: 需求详细描述（可选，支持富文本）
    - **priority**: 优先级（必填，可选值：高/中/低）
    - **modules**: 关联模块列表（可选，多选标签）
    - **created_by**: 创建者（可选）
    - **attachments**: 附件信息（可选，支持PDF/图片等）
    
    **返回：** 创建的需求详情
    """
    try:
        async with db as session:
            # 转换为字典格式
            requirement_data = requirement.dict()
            
            # 创建需求
            new_requirement = await requirement_service.create_requirement(requirement_data, session)
            
            # 转换响应格式
            return RequirementResponse(
                id=new_requirement.id,
                title=new_requirement.title,
                description=new_requirement.description,
                priority=new_requirement.priority.value,
                status=new_requirement.status.value,
                modules=new_requirement.modules,
                is_complete=new_requirement.is_complete,
                missing_fields=new_requirement.missing_fields,
                created_by=new_requirement.created_by,
                created_time=new_requirement.created_time,
                updated_time=new_requirement.updated_time,
                attachments=new_requirement.attachments
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建需求失败: {str(e)}"
        )

@router.get("/", response_model=RequirementListResponse, summary="获取需求列表")
async def get_requirements(
    page: int = Query(1, ge=1, description="页码（从1开始）"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小（1-100）"),
    status: Optional[str] = Query(None, description="状态筛选"),
    priority: Optional[str] = Query(None, description="优先级筛选（高/中/低）"),
    keyword: Optional[str] = Query(None, description="关键词搜索（标题、描述）"),
    db: AsyncSession = Depends(get_async_db)
):
    """
    获取需求列表，支持分页和筛选
    
    **查询参数：**
    - **page**: 页码（从1开始）
    - **page_size**: 每页大小（1-100）
    - **status**: 状态筛选（可选）
    - **priority**: 优先级筛选（高/中/低）
    - **keyword**: 关键词搜索（在标题和描述中搜索）
    
    **返回：** 需求列表和分页信息
    """
    try:
        async with db as session:
            requirements, total = await requirement_service.get_requirements_list(
                session=session,
                page=page,
                page_size=page_size,
                status=status,
                priority=priority,
                keyword=keyword
            )
            
            # 转换响应格式
            requirement_responses = []
            for req in requirements:
                requirement_responses.append(RequirementResponse(
                    id=req.id,
                    title=req.title,
                    description=req.description,
                    priority=req.priority.value,
                    status=req.status.value,
                    modules=req.modules,
                    is_complete=req.is_complete,
                    missing_fields=req.missing_fields,
                    created_by=req.created_by,
                    created_time=req.created_time,
                    updated_time=req.updated_time,
                    attachments=req.attachments
                ))
            
            total_pages = (total + page_size - 1) // page_size
            
            return RequirementListResponse(
                requirements=requirement_responses,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求列表失败: {str(e)}"
        )

@router.get("/{requirement_id}", response_model=RequirementResponse, summary="获取需求详情")
async def get_requirement(
    requirement_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """
    根据ID获取需求详情
    
    **路径参数：**
    - **requirement_id**: 需求ID
    
    **返回：** 需求详细信息
    """
    try:
        async with db as session:
            requirement = await requirement_service.get_requirement_by_id(requirement_id, session)
            
            if not requirement:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="需求不存在"
                )
            
            return RequirementResponse(
                id=requirement.id,
                title=requirement.title,
                description=requirement.description,
                priority=requirement.priority.value,
                status=requirement.status.value,
                modules=requirement.modules,
                is_complete=requirement.is_complete,
                missing_fields=requirement.missing_fields,
                created_by=requirement.created_by,
                created_time=requirement.created_time,
                updated_time=requirement.updated_time,
                attachments=requirement.attachments
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求详情失败: {str(e)}"
        )

@router.put("/{requirement_id}", response_model=RequirementResponse, summary="更新需求")
async def update_requirement(
    requirement_id: int,
    requirement: RequirementUpdate,
    db: AsyncSession = Depends(get_async_db)
):
    """
    更新需求信息（支持在线编辑）
    
    **路径参数：**
    - **requirement_id**: 需求ID
    
    **请求体：** 需要更新的字段（只传入需要修改的字段）
    
    **返回：** 更新后的需求信息
    """
    try:
        async with db as session:
            # 过滤None值
            update_data = {k: v for k, v in requirement.dict().items() if v is not None}
            
            updated_requirement = await requirement_service.update_requirement(
                requirement_id, update_data, session
            )
            
            if not updated_requirement:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="需求不存在"
                )
            
            return RequirementResponse(
                id=updated_requirement.id,
                title=updated_requirement.title,
                description=updated_requirement.description,
                priority=updated_requirement.priority.value,
                status=updated_requirement.status.value,
                modules=updated_requirement.modules,
                is_complete=updated_requirement.is_complete,
                missing_fields=updated_requirement.missing_fields,
                created_by=updated_requirement.created_by,
                created_time=updated_requirement.created_time,
                updated_time=updated_requirement.updated_time,
                attachments=updated_requirement.attachments
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新需求失败: {str(e)}"
        )

@router.delete("/{requirement_id}", summary="删除需求")
async def delete_requirement(
    requirement_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """
    删除需求

    **路径参数：**
    - **requirement_id**: 需求ID

    **返回：** 删除结果
    """
    try:
        async with db as session:
            success = await requirement_service.delete_requirement(requirement_id, session)

            if not success:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="需求不存在"
                )

            return {"message": "需求删除成功", "requirement_id": requirement_id}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除需求失败: {str(e)}"
        )

@router.post("/ai-analysis", summary="AI需求分析")
async def analyze_requirements(
    request: AnalysisRequest,
    db: AsyncSession = Depends(get_async_db)
):
    """
    对选中的需求进行AI分析

    **请求体：**
    - **requirement_ids**: 需求ID列表（最多5个）

    **功能说明：**
    - 使用AutoGen + DeepSeek进行真实AI分析
    - 支持批量分析，但限制数量避免超时
    - 分析结果自动保存到数据库
    - 支持分析进度跟踪

    **返回：** 分析结果摘要
    """
    try:
        async with db as session:
            # 验证需求ID数量
            if len(request.requirement_ids) > settings.ANALYSIS_BATCH_SIZE:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"批量分析数量不能超过{settings.ANALYSIS_BATCH_SIZE}个"
                )

            # 执行AI分析
            results = await requirement_service.analyze_requirements(
                request.requirement_ids, session
            )

            # 统计分析结果
            success_count = len([r for r in results if r.get('analysis_status') == 'completed'])
            failed_count = len(results) - success_count

            return {
                "message": f"AI分析完成",
                "total_requested": len(request.requirement_ids),
                "success_count": success_count,
                "failed_count": failed_count,
                "results_summary": [
                    {
                        "req_id": r.get('req_id'),
                        "status": r.get('analysis_status'),
                        "error": r.get('error_message') if r.get('analysis_status') == 'failed' else None
                    }
                    for r in results
                ]
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI分析失败: {str(e)}"
        )

@router.get("/analyses/", summary="获取分析结果列表")
async def get_analysis_results(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    requirement_id: Optional[int] = Query(None, description="需求ID筛选"),
    db: AsyncSession = Depends(get_async_db)
):
    """
    获取AI分析结果列表

    **查询参数：**
    - **page**: 页码（从1开始）
    - **page_size**: 每页大小（1-100）
    - **requirement_id**: 需求ID筛选（可选）

    **返回：** 分析结果列表和分页信息
    """
    try:
        async with db as session:
            analyses, total = await requirement_service.get_analysis_results(
                session=session,
                page=page,
                page_size=page_size,
                requirement_id=requirement_id
            )

            # 转换响应格式
            analysis_responses = []
            for analysis in analyses:
                analysis_responses.append(AnalysisResponse(
                    id=analysis.id,
                    req_id=analysis.req_id,
                    analysis_result=analysis.analysis_result or "",
                    structured_result=analysis.structured_result or {},
                    key_points=analysis.key_points or [],
                    suggestions=analysis.suggestions or [],
                    ai_model=analysis.ai_model or "",
                    analysis_status=analysis.analysis_status,
                    is_manually_adjusted=analysis.is_manually_adjusted,
                    manual_adjust=analysis.manual_adjust,
                    created_time=analysis.created_time
                ))

            total_pages = (total + page_size - 1) // page_size

            return {
                "analyses": analysis_responses,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分析结果失败: {str(e)}"
        )

@router.put("/analyses/{analysis_id}/adjust", summary="人工调整分析结果")
async def adjust_analysis_result(
    analysis_id: int,
    request: ManualAdjustmentRequest,
    db: AsyncSession = Depends(get_async_db)
):
    """
    人工调整AI分析结果

    **路径参数：**
    - **analysis_id**: 分析结果ID

    **请求体：**
    - **manual_adjustment**: 人工调整内容
    - **adjusted_by**: 调整者姓名

    **功能说明：**
    - 支持对AI分析结果进行人工补充和修正
    - 保留原始AI分析结果，人工调整作为补充
    - 记录调整者和调整时间

    **返回：** 调整结果确认
    """
    try:
        async with db as session:
            updated_analysis = await requirement_service.update_manual_adjustment(
                analysis_id=analysis_id,
                manual_adjustment=request.manual_adjustment,
                adjusted_by=request.adjusted_by,
                session=session
            )

            if not updated_analysis:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分析结果不存在"
                )

            return {
                "message": "人工调整保存成功",
                "analysis_id": analysis_id,
                "adjusted_by": request.adjusted_by,
                "adjusted_time": updated_analysis.adjusted_time.isoformat(),
                "is_manually_adjusted": True
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存人工调整失败: {str(e)}"
        )

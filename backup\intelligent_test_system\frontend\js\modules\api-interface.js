/**
 * API接口管理模块 - 负责API接口测试和管理
 */

class APIInterfaceModule {
    constructor() {
        this.uploadedFiles = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupFileUpload();
    }

    setupEventListeners() {
        // API测试表单提交
        const form = document.getElementById('apiTestForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    }

    setupFileUpload() {
        // API测试文件上传处理
        const uploadArea = document.getElementById('apiUploadArea');
        const fileInput = document.getElementById('apiFileInput');
        
        if (uploadArea && fileInput) {
            uploadArea.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
            
            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                this.handleFileSelect({ target: { files: e.dataTransfer.files } });
            });
        }
    }

    handleFileSelect(event) {
        const files = Array.from(event.target.files);
        const fileList = document.getElementById('apiFileList');
        
        files.forEach(file => {
            if (file.size > 10 * 1024 * 1024) {
                alert(`文件 ${file.name} 超过10MB限制`);
                return;
            }
            
            this.uploadedFiles.push(file);
            
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <span class="file-icon">${this.getFileIcon(file.name)}</span>
                    <span>${file.name}</span>
                    <span style="color: var(--text-secondary); font-size: 12px;">(${(file.size / 1024).toFixed(1)} KB)</span>
                </div>
                <button class="file-remove" onclick="apiInterfaceModule.removeFile(this, '${file.name}')">✕</button>
            `;
            fileList.appendChild(fileItem);
        });
    }

    removeFile(button, filename) {
        this.uploadedFiles = this.uploadedFiles.filter(file => file.name !== filename);
        button.parentElement.remove();
    }

    getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const icons = {
            'png': '🖼️', 'jpg': '🖼️', 'jpeg': '🖼️', 'gif': '🖼️', 'bmp': '🖼️',
            'pdf': '📄', 'doc': '📝', 'docx': '📝', 'xls': '📊', 'xlsx': '📊',
            'ppt': '📊', 'pptx': '📊'
        };
        return icons[ext] || '📎';
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        
        // 添加上传的文件
        this.uploadedFiles.forEach(file => {
            formData.append('files', file);
        });
        
        // 显示测试结果区域
        document.getElementById('apiTestResults').classList.remove('hidden');
        document.getElementById('apiTestBtn').disabled = true;
        
        // 清空之前的日志
        const logContainer = document.getElementById('apiTestLog');
        logContainer.innerHTML = '';
        this.addLog('🚀 开始API接口测试...', 'info');
        
        try {
            const response = await fetch('/api/requirement-understanding', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            this.addLog('✅ 接口连接成功，开始接收流式响应...', 'success');
            
            await this.processStreamResponse(response);
            
        } catch (error) {
            this.addLog(`❌ 接口调用失败: ${error.message}`, 'error');
        } finally {
            document.getElementById('apiTestBtn').disabled = false;
        }
    }

    async processStreamResponse(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let responseData = null;
        
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        
                        // 记录API响应
                        this.addLog(`📡 接收到响应: ${data.type} - ${data.message}`, 'info');
                        
                        if (data.type === 'complete' && data.result) {
                            responseData = data.result;
                            this.addLog('🎉 接口调用成功完成！', 'success');
                            
                            // 显示响应数据
                            this.displayResponse(responseData);
                        } else if (data.type === 'error') {
                            this.addLog(`❌ 接口返回错误: ${data.message}`, 'error');
                        }
                        
                    } catch (parseError) {
                        this.addLog(`⚠️ 响应解析失败: ${parseError.message}`, 'error');
                    }
                }
            }
        }
    }

    addLog(message, type = 'info') {
        const log = document.getElementById('apiTestLog');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        
        const typeIcons = {
            'info': '💡',
            'success': '✅',
            'error': '❌'
        };
        
        logEntry.innerHTML = `
            <span class="log-timestamp">[${timestamp}]</span> 
            ${typeIcons[type] || '💡'} ${message}
        `;
        
        log.appendChild(logEntry);
        log.scrollTop = log.scrollHeight;
    }

    displayResponse(responseData) {
        const container = document.getElementById('apiResponseData');
        
        const html = `
            <div class="card" style="margin-top: 16px;">
                <div class="card-title">
                    <span class="icon">📊</span>
                    API响应数据
                </div>
                <div style="background: var(--bg-light); padding: 16px; border-radius: 8px;">
                    <h4 style="margin-bottom: 12px;">响应摘要</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 12px; margin-bottom: 16px;">
                        <div>
                            <strong>任务ID:</strong><br>
                            <code>${responseData.task_id}</code>
                        </div>
                        <div>
                            <strong>AI框架:</strong><br>
                            <span>${responseData.ai_framework}</span>
                        </div>
                        <div>
                            <strong>生成时间:</strong><br>
                            <span>${new Date(responseData.generation_time).toLocaleString()}</span>
                        </div>
                        <div>
                            <strong>项目名称:</strong><br>
                            <span>${responseData.project_name}</span>
                        </div>
                    </div>
                    
                    <h4 style="margin-bottom: 12px;">结构化需求数据</h4>
                    <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid var(--border-light);">
                        <pre style="margin: 0; white-space: pre-wrap; font-size: 12px; max-height: 300px; overflow-y: auto;">${JSON.stringify(responseData.structured_requirements, null, 2)}</pre>
                    </div>
                    
                    <div style="margin-top: 16px; text-align: center;">
                        <button class="btn btn-primary" onclick="apiInterfaceModule.copyResponse('${responseData.task_id}')">
                            <span>📋</span>
                            复制响应数据
                        </button>
                        <button class="btn btn-success" onclick="apiInterfaceModule.downloadResponse('${responseData.task_id}')" style="margin-left: 12px;">
                            <span>💾</span>
                            下载JSON文件
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
        
        // 保存响应数据以供后续操作
        this.lastResponse = responseData;
    }

    copyResponse(taskId) {
        if (this.lastResponse) {
            navigator.clipboard.writeText(JSON.stringify(this.lastResponse, null, 2))
                .then(() => {
                    this.addLog('📋 响应数据已复制到剪贴板', 'success');
                })
                .catch(err => {
                    this.addLog(`❌ 复制失败: ${err.message}`, 'error');
                });
        }
    }

    downloadResponse(taskId) {
        if (this.lastResponse) {
            const dataStr = JSON.stringify(this.lastResponse, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `requirement-understanding-${taskId}.json`;
            link.click();
            
            this.addLog('💾 JSON文件下载已开始', 'success');
        }
    }

    // 清空测试数据
    clearTestData() {
        this.uploadedFiles = [];
        document.getElementById('apiFileList').innerHTML = '';
        document.getElementById('apiTestResults').classList.add('hidden');
        document.getElementById('apiTestForm').reset();
        this.addLog('🧹 测试数据已清空', 'info');
    }

    // 获取API使用统计
    getAPIStats() {
        return {
            totalCalls: this.totalCalls || 0,
            successCalls: this.successCalls || 0,
            errorCalls: this.errorCalls || 0,
            lastCallTime: this.lastCallTime || null
        };
    }
}

// 全局实例
window.apiInterfaceModule = new APIInterfaceModule();

// 向后兼容的全局函数
function removeApiFile(button, filename) {
    window.apiInterfaceModule.removeFile(button, filename);
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI测试用例生成平台 - AutoGen + DeepSeek实现
集成真正的AI代理协作功能
"""

import os
import json
import uuid
import asyncio
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.responses import HTMLResponse, StreamingResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# AutoGen相关导入
try:
    from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager
    # 尝试不同的导入方式
    try:
        from autogen.oai.openai import OpenAIChatCompletionClient
    except ImportError:
        try:
            from autogen import OpenAIChatCompletionClient
        except ImportError:
            # 使用配置字典方式
            OpenAIChatCompletionClient = None
    AUTOGEN_AVAILABLE = True
    print("✅ AutoGen库加载成功")
except ImportError as e:
    print(f"⚠️ AutoGen未安装或导入失败: {str(e)}")
    print("将使用模拟模式")
    AUTOGEN_AVAILABLE = False

# 创建必要的目录
os.makedirs("uploads", exist_ok=True)
os.makedirs("exports", exist_ok=True)
os.makedirs("temp", exist_ok=True)

app = FastAPI(title="AI测试用例生成平台", version="2.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# DeepSeek API配置
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_BASE_URL = "https://api.deepseek.com"

# 全局变量存储任务状态
task_storage = {}

class AITestCaseGenerator:
    """AI测试用例生成器 - 基于AutoGen + DeepSeek"""

    def __init__(self):
        self.deepseek_client = None
        self.test_expert = None
        self.requirement_analyst = None
        self.setup_agents()

    def setup_agents(self):
        """设置AI代理"""
        if not AUTOGEN_AVAILABLE:
            print("⚠️ AutoGen不可用，使用模拟模式")
            return

        try:
            # 配置DeepSeek客户端 - 使用新的配置方式
            llm_config = {
                "model": "deepseek-chat",
                "api_key": DEEPSEEK_API_KEY,
                "base_url": DEEPSEEK_BASE_URL,
                "temperature": 0.7,
                "timeout": 60
            }

            # 测试专家代理
            self.test_expert = AssistantAgent(
                name="test_case_expert",
                llm_config=llm_config,
                system_message="""您是一位拥有15年经验的专业测试专家。您的职责是：
1. 深度分析软件需求和功能规格
2. 设计全面的测试策略和测试用例
3. 确保测试用例覆盖所有功能点、边界条件和异常情况
4. 生成标准化的测试用例文档

请始终以专业、严谨的态度工作，确保测试用例的质量和完整性。
输出格式要求：
- 测试用例标题
- 测试目标
- 前置条件
- 测试步骤
- 预期结果
- 优先级
- 测试类型

请生成详细、专业的测试用例，确保覆盖正常流程、异常流程和边界条件。""",
                max_consecutive_auto_reply=3
            )

            # 需求分析师代理
            self.requirement_analyst = AssistantAgent(
                name="requirement_analyst",
                llm_config=llm_config,
                system_message="""您是一位专业的需求分析师。您的职责是：
1. 深入理解和分析用户提供的需求描述
2. 识别关键功能点和业务流程
3. 分析用户角色和使用场景
4. 提取测试要点和关注领域
5. 为测试专家提供清晰的需求解读

请以结构化的方式分析需求，为后续的测试用例设计提供坚实基础。
输出应包含：功能点分析、用户角色识别、业务流程梳理、关键测试点。""",
                max_consecutive_auto_reply=2
            )

            print("✅ AutoGen + DeepSeek代理初始化成功")

        except Exception as e:
            print(f"❌ AI代理初始化失败: {str(e)}")
            self.test_expert = None
            self.requirement_analyst = None

    async def generate_test_cases(self, project_name: str, requirements: str,
                                context_info: str = "", file_content: str = "") -> Dict[str, Any]:
        """生成测试用例"""
        if not AUTOGEN_AVAILABLE or not self.test_expert:
            return await self._simulate_generation(project_name, requirements, context_info)

        try:
            # 构建完整的需求描述
            full_requirements = f"""
项目名称：{project_name}

核心需求：
{requirements}

上下文信息：
{context_info if context_info else '无额外上下文信息'}

文件内容分析：
{file_content if file_content else '无上传文件'}
"""

            # 需求分析阶段
            requirement_analysis = await self._analyze_requirements(full_requirements)

            # 测试用例生成阶段
            test_cases = await self._generate_test_cases_with_expert(requirement_analysis, full_requirements)

            return {
                "success": True,
                "project_name": project_name,
                "requirement_analysis": requirement_analysis,
                "test_cases": test_cases,
                "generated_at": datetime.now().isoformat(),
                "agent_info": "AutoGen + DeepSeek双代理协作"
            }

        except Exception as e:
            print(f"❌ 测试用例生成失败: {str(e)}")
            return await self._simulate_generation(project_name, requirements, context_info)

    async def _analyze_requirements(self, requirements: str) -> str:
        """需求分析阶段"""
        try:
            analysis_prompt = f"""
请对以下需求进行深度分析：

{requirements}

请从以下维度进行分析：
1. 核心功能点识别
2. 用户角色和权限
3. 业务流程梳理
4. 数据流向分析
5. 关键测试点提取
6. 风险点识别

请提供结构化的分析结果。
"""

            # 在线程池中运行同步的AutoGen调用
            import asyncio
            import concurrent.futures

            def sync_generate():
                return self.requirement_analyst.generate_reply(
                    messages=[{"role": "user", "content": analysis_prompt}]
                )

            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                response = await loop.run_in_executor(executor, sync_generate)

            return response if isinstance(response, str) else str(response)

        except Exception as e:
            print(f"❌ 需求分析失败: {str(e)}")
            return f"需求分析：{requirements[:200]}..."

    async def _generate_test_cases_with_expert(self, analysis: str, requirements: str) -> list:
        """测试专家生成测试用例"""
        try:
            test_case_prompt = f"""
基于以下需求分析，请生成详细的测试用例：

需求分析结果：
{analysis}

原始需求：
{requirements}

请生成至少5个测试用例，每个测试用例包含：
1. 测试用例标题
2. 测试目标
3. 前置条件
4. 测试步骤（详细的操作步骤）
5. 预期结果
6. 优先级（高/中/低）
7. 测试类型（功能测试/性能测试/安全测试/兼容性测试等）

请确保测试用例覆盖正常流程、异常流程和边界条件。
"""

            # 在线程池中运行同步的AutoGen调用
            import asyncio
            import concurrent.futures

            def sync_generate():
                return self.test_expert.generate_reply(
                    messages=[{"role": "user", "content": test_case_prompt}]
                )

            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                response = await loop.run_in_executor(executor, sync_generate)

            # 解析测试用例
            test_cases_text = response if isinstance(response, str) else str(response)
            return self._parse_test_cases(test_cases_text)

        except Exception as e:
            print(f"❌ 测试用例生成失败: {str(e)}")
            return self._generate_fallback_test_cases()

    def _parse_test_cases(self, text: str) -> list:
        """解析测试用例文本"""
        test_cases = []

        # 简单的解析逻辑，实际可以更复杂
        lines = text.split('\n')
        current_case = {}

        for line in lines:
            line = line.strip()
            if not line:
                continue

            if '测试用例' in line and ('标题' in line or '名称' in line):
                if current_case:
                    test_cases.append(current_case)
                current_case = {"title": line.split('：')[-1] if '：' in line else line}
            elif '测试目标' in line:
                current_case["objective"] = line.split('：')[-1] if '：' in line else line
            elif '前置条件' in line:
                current_case["preconditions"] = line.split('：')[-1] if '：' in line else line
            elif '测试步骤' in line:
                current_case["steps"] = line.split('：')[-1] if '：' in line else line
            elif '预期结果' in line:
                current_case["expected_result"] = line.split('：')[-1] if '：' in line else line
            elif '优先级' in line:
                current_case["priority"] = line.split('：')[-1] if '：' in line else line
            elif '测试类型' in line:
                current_case["test_type"] = line.split('：')[-1] if '：' in line else line

        if current_case:
            test_cases.append(current_case)

        # 如果解析失败，返回默认格式
        if not test_cases:
            return [{"title": "AI生成的测试用例", "content": text[:500] + "..."}]

        return test_cases

    def _generate_fallback_test_cases(self) -> list:
        """生成备用测试用例"""
        return [
            {
                "title": "用户登录功能测试",
                "objective": "验证用户能够正常登录系统",
                "preconditions": "用户已注册且账号状态正常",
                "steps": "1. 打开登录页面\n2. 输入正确的用户名和密码\n3. 点击登录按钮",
                "expected_result": "用户成功登录，跳转到主页面",
                "priority": "高",
                "test_type": "功能测试"
            },
            {
                "title": "数据输入验证测试",
                "objective": "验证系统对无效数据的处理",
                "preconditions": "系统正常运行",
                "steps": "1. 输入无效数据\n2. 提交表单\n3. 观察系统反应",
                "expected_result": "系统显示相应的错误提示信息",
                "priority": "中",
                "test_type": "功能测试"
            }
        ]

    async def _simulate_generation(self, project_name: str, requirements: str, context_info: str) -> Dict[str, Any]:
        """模拟生成（当AutoGen不可用时）"""
        await asyncio.sleep(2)  # 模拟处理时间

        return {
            "success": True,
            "project_name": project_name,
            "requirement_analysis": f"模拟分析：{requirements[:100]}...",
            "test_cases": self._generate_fallback_test_cases(),
            "generated_at": datetime.now().isoformat(),
            "agent_info": "模拟模式（AutoGen不可用）"
        }

# 初始化AI生成器
ai_generator = AITestCaseGenerator()

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "message": "AI测试用例生成平台运行正常",
        "version": "2.0-AutoGen版",
        "features": ["首页", "测试用例生成"],
        "autogen_available": AUTOGEN_AVAILABLE,
        "deepseek_configured": bool(DEEPSEEK_API_KEY),
        "ai_framework": "AutoGen + DeepSeek"
    }

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """文件上传接口"""
    try:
        # 验证文件类型
        allowed_types = ["image/png", "image/jpeg", "image/jpg", "image/gif", "image/bmp", "image/webp"]
        if file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="不支持的文件类型")

        # 验证文件大小 (10MB)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="文件大小超过10MB限制")

        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        file_extension = file.filename.split('.')[-1] if '.' in file.filename else 'png'
        filename = f"{file_id}.{file_extension}"
        file_path = f"uploads/{filename}"

        # 保存文件
        with open(file_path, "wb") as f:
            f.write(file_content)

        return {
            "success": True,
            "file_id": file_id,
            "filename": filename,
            "original_name": file.filename,
            "size": len(file_content),
            "content_type": file.content_type,
            "message": "文件上传成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.post("/generate")
async def generate_test_cases(
    project_name: str = Form(...),
    requirements: str = Form(...),
    context_info: str = Form(""),
    file_id: Optional[str] = Form(None)
):
    """生成测试用例"""
    try:
        # 处理上传的文件
        file_content = ""
        if file_id:
            # 这里可以添加图像识别逻辑
            file_content = f"已上传文件ID: {file_id}"

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 开始生成测试用例
        result = await ai_generator.generate_test_cases(
            project_name=project_name,
            requirements=requirements,
            context_info=context_info,
            file_content=file_content
        )

        # 存储结果
        task_storage[task_id] = result

        return {
            "success": True,
            "task_id": task_id,
            "message": "测试用例生成完成",
            **result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成失败: {str(e)}")

@app.get("/task/{task_id}")
async def get_task_result(task_id: str):
    """获取任务结果"""
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="任务不存在")

    return {
        "success": True,
        "task_id": task_id,
        **task_storage[task_id]
    }

@app.post("/export/{task_id}")
async def export_test_cases(task_id: str):
    """导出测试用例为CSV"""
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="任务不存在")

    try:
        result = task_storage[task_id]

        # 生成CSV内容
        csv_content = "序号,测试用例标题,测试目标,前置条件,测试步骤,预期结果,优先级,测试类型\n"

        for i, test_case in enumerate(result.get("test_cases", []), 1):
            title = test_case.get("title", "").replace(",", "，").replace("\n", " ")
            objective = test_case.get("objective", "").replace(",", "，").replace("\n", " ")
            preconditions = test_case.get("preconditions", "").replace(",", "，").replace("\n", " ")
            steps = test_case.get("steps", "").replace(",", "，").replace("\n", " ")
            expected = test_case.get("expected_result", "").replace(",", "，").replace("\n", " ")
            priority = test_case.get("priority", "中").replace(",", "，")
            test_type = test_case.get("test_type", "功能测试").replace(",", "，")

            csv_content += f"{i},{title},{objective},{preconditions},{steps},{expected},{priority},{test_type}\n"

        # 保存CSV文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"测试用例_{result.get('project_name', 'unknown')}_{timestamp}.csv"
        file_path = f"exports/{filename}"

        with open(file_path, "w", encoding="utf-8-sig") as f:
            f.write(csv_content)

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type="text/csv"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.get("/generate-stream/{task_id}")
async def generate_stream(task_id: str):
    """流式生成测试用例"""
    async def generate():
        try:
            yield f"data: {json.dumps({'type': 'start', 'message': '开始生成测试用例...'})}\n\n"
            await asyncio.sleep(1)

            yield f"data: {json.dumps({'type': 'progress', 'message': '正在分析需求...', 'progress': 20})}\n\n"
            await asyncio.sleep(1)

            yield f"data: {json.dumps({'type': 'progress', 'message': '需求分析师正在工作...', 'progress': 40})}\n\n"
            await asyncio.sleep(1)

            yield f"data: {json.dumps({'type': 'progress', 'message': '测试专家正在设计用例...', 'progress': 60})}\n\n"
            await asyncio.sleep(1)

            yield f"data: {json.dumps({'type': 'progress', 'message': '优化测试用例质量...', 'progress': 80})}\n\n"
            await asyncio.sleep(1)

            # 获取结果
            if task_id in task_storage:
                result = task_storage[task_id]
                yield f"data: {json.dumps({'type': 'complete', 'message': '生成完成！', 'progress': 100, 'result': result})}\n\n"
            else:
                yield f"data: {json.dumps({'type': 'error', 'message': '任务不存在'})}\n\n"

        except Exception as e:
            yield f"data: {json.dumps({'type': 'error', 'message': f'生成失败: {str(e)}'})}\n\n"

    return StreamingResponse(generate(), media_type="text/plain")

@app.get("/", response_class=HTMLResponse)
async def root():
    """返回国企风格主页"""
    return """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AI测试用例生成平台</title>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
                background: #f5f7fa;
                color: #333;
                overflow-x: hidden;
            }

            /* 左侧导航栏 */
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                width: 280px;
                height: 100vh;
                background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
                color: white;
                z-index: 1000;
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }

            .logo-section {
                padding: 30px 25px;
                text-align: center;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }

            .logo-icon {
                font-size: 3em;
                color: #ffd700;
                margin-bottom: 15px;
            }

            .logo-title {
                font-size: 1.4em;
                font-weight: 700;
                margin-bottom: 8px;
                letter-spacing: 1px;
            }

            .logo-subtitle {
                font-size: 0.9em;
                opacity: 0.8;
                color: #b8d4f0;
            }

            .nav-menu {
                padding: 20px 0;
            }

            .nav-item {
                margin: 8px 20px;
            }

            .nav-link {
                display: flex;
                align-items: center;
                padding: 15px 20px;
                color: white;
                text-decoration: none;
                border-radius: 10px;
                transition: all 0.3s ease;
                position: relative;
                font-weight: 500;
            }

            .nav-link:hover {
                background: rgba(255,255,255,0.1);
                transform: translateX(5px);
            }

            .nav-link.active {
                background: rgba(255,215,0,0.2);
                border-left: 4px solid #ffd700;
            }

            .nav-link i {
                width: 20px;
                margin-right: 15px;
                font-size: 1.1em;
            }

            .nav-badge {
                background: #ff4757;
                color: white;
                font-size: 0.7em;
                padding: 2px 6px;
                border-radius: 10px;
                margin-left: auto;
                font-weight: 600;
            }

            .sidebar-footer {
                position: absolute;
                bottom: 20px;
                left: 20px;
                right: 20px;
                text-align: center;
                font-size: 0.8em;
                opacity: 0.7;
                border-top: 1px solid rgba(255,255,255,0.1);
                padding-top: 15px;
            }

            /* 主内容区域 */
            .main-content {
                margin-left: 280px;
                min-height: 100vh;
                background: #f5f7fa;
            }

            .header {
                background: white;
                padding: 20px 40px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                border-bottom: 1px solid #e9ecef;
            }

            .header-title {
                font-size: 1.8em;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 5px;
            }

            .breadcrumb {
                color: #6c757d;
                font-size: 0.9em;
            }

            .content-area {
                padding: 40px;
            }

            .welcome-banner {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 60px 40px;
                border-radius: 15px;
                text-align: center;
                margin-bottom: 40px;
                box-shadow: 0 10px 30px rgba(102,126,234,0.3);
            }

            .welcome-banner h1 {
                font-size: 2.8em;
                margin-bottom: 20px;
                font-weight: 700;
            }

            .welcome-banner p {
                font-size: 1.2em;
                opacity: 0.9;
                margin-bottom: 30px;
                max-width: 600px;
                margin-left: auto;
                margin-right: auto;
            }

            .feature-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 30px;
                margin-top: 40px;
            }

            .feature-card {
                background: white;
                padding: 40px;
                border-radius: 15px;
                box-shadow: 0 5px 20px rgba(0,0,0,0.08);
                border: 1px solid #e9ecef;
                transition: all 0.3s ease;
                text-align: center;
            }

            .feature-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            }

            .feature-icon {
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 25px;
                font-size: 2em;
                color: white;
            }

            .feature-title {
                font-size: 1.5em;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 15px;
            }

            .feature-desc {
                color: #6c757d;
                line-height: 1.6;
                margin-bottom: 25px;
            }

            .btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 1em;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-block;
            }

            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(102,126,234,0.4);
            }

            .stats-section {
                background: white;
                padding: 40px;
                border-radius: 15px;
                margin-top: 40px;
                box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 30px;
                text-align: center;
            }

            .stat-item {
                padding: 20px;
            }

            .stat-number {
                font-size: 2.5em;
                font-weight: 700;
                color: #667eea;
                margin-bottom: 10px;
            }

            .stat-label {
                color: #6c757d;
                font-weight: 500;
            }
        </style>
    </head>
    <body>
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <div class="logo-title">AI测试平台</div>
                <div class="logo-subtitle">智能 · 专业 · 高效</div>
            </div>

            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" onclick="showPage('home')">
                        <i class="fas fa-home"></i>
                        <span>平台首页</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showPage('generate')">
                        <i class="fas fa-magic"></i>
                        <span>测试用例生成</span>
                        <span class="nav-badge">AI</span>
                    </a>
                </div>
            </nav>

            <div class="sidebar-footer">
                <div>版本 v2.0</div>
                <div style="margin-top: 5px;">端口: 8002</div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <div class="header-title">AI测试用例生成平台</div>
                <div class="breadcrumb">首页 / 平台概览</div>
            </div>

            <div class="content-area" id="content-area">
                <!-- 欢迎横幅 -->
                <div class="welcome-banner">
                    <h1><i class="fas fa-rocket"></i> 欢迎使用AI测试用例生成平台</h1>
                    <p>基于AutoGen + DeepSeek的企业级智能测试解决方案，为您提供专业、高效的测试用例生成服务</p>
                </div>

                <!-- 功能特色 -->
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="feature-title">AI智能分析</div>
                        <div class="feature-desc">
                            采用AutoGen + DeepSeek双智能代理协作，深度理解需求，生成专业测试用例
                        </div>
                        <button class="btn" onclick="showPage('generate')">
                            立即体验 →
                        </button>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-file-upload"></i>
                        </div>
                        <div class="feature-title">多模态输入</div>
                        <div class="feature-desc">
                            支持思维导图、流程图、界面截图等多种文件格式，AI智能识别分析
                        </div>
                        <button class="btn" onclick="showPage('generate')">
                            开始上传 →
                        </button>
                    </div>
                </div>

                <!-- 平台数据 -->
                <div class="stats-section">
                    <h3 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">平台核心数据</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">需求实现度</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">2</div>
                            <div class="stat-label">核心功能</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">10MB</div>
                            <div class="stat-label">最大文件支持</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">6+</div>
                            <div class="stat-label">文件格式</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function showPage(pageId) {
                // 更新导航状态
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });

                if (pageId === 'home') {
                    document.querySelector('.nav-link[onclick*="home"]').classList.add('active');
                    updateHeader('平台首页', '首页 / 平台概览');
                    showHomePage();
                } else if (pageId === 'generate') {
                    document.querySelector('.nav-link[onclick*="generate"]').classList.add('active');
                    updateHeader('测试用例生成', '首页 / 测试用例生成');
                    showGeneratePage();
                }
            }

            function updateHeader(title, breadcrumb) {
                document.querySelector('.header-title').textContent = title;
                document.querySelector('.breadcrumb').textContent = breadcrumb;
            }

            function showHomePage() {
                // 当前已经是首页内容
                location.reload();
            }

            function showGeneratePage() {
                window.location.href = '/generate';
            }

            console.log('🎉 国企风格AI测试平台加载完成');
            console.log('🏢 设计风格: 专业、稳重、大方');
            console.log('🌐 访问地址: http://localhost:8002');
        </script>
    </body>
    </html>
    """

@app.get("/generate", response_class=HTMLResponse)
async def generate_page():
    """测试用例生成页面"""
    return """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>测试用例生成 - AI测试平台</title>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
                background: #f5f7fa;
                color: #333;
                overflow-x: hidden;
            }

            /* 左侧导航栏 */
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                width: 280px;
                height: 100vh;
                background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
                color: white;
                z-index: 1000;
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }

            .logo-section {
                padding: 30px 25px;
                text-align: center;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }

            .logo-icon {
                font-size: 3em;
                color: #ffd700;
                margin-bottom: 15px;
            }

            .logo-title {
                font-size: 1.4em;
                font-weight: 700;
                margin-bottom: 8px;
                letter-spacing: 1px;
            }

            .logo-subtitle {
                font-size: 0.9em;
                opacity: 0.8;
                color: #b8d4f0;
            }

            .nav-menu {
                padding: 20px 0;
            }

            .nav-item {
                margin: 8px 20px;
            }

            .nav-link {
                display: flex;
                align-items: center;
                padding: 15px 20px;
                color: white;
                text-decoration: none;
                border-radius: 10px;
                transition: all 0.3s ease;
                position: relative;
                font-weight: 500;
            }

            .nav-link:hover {
                background: rgba(255,255,255,0.1);
                transform: translateX(5px);
            }

            .nav-link.active {
                background: rgba(255,215,0,0.2);
                border-left: 4px solid #ffd700;
            }

            .nav-link i {
                width: 20px;
                margin-right: 15px;
                font-size: 1.1em;
            }

            .nav-badge {
                background: #ff4757;
                color: white;
                font-size: 0.7em;
                padding: 2px 6px;
                border-radius: 10px;
                margin-left: auto;
                font-weight: 600;
            }

            .sidebar-footer {
                position: absolute;
                bottom: 20px;
                left: 20px;
                right: 20px;
                text-align: center;
                font-size: 0.8em;
                opacity: 0.7;
                border-top: 1px solid rgba(255,255,255,0.1);
                padding-top: 15px;
            }

            /* 主内容区域 */
            .main-content {
                margin-left: 280px;
                min-height: 100vh;
                background: #f5f7fa;
            }

            .header {
                background: white;
                padding: 20px 40px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                border-bottom: 1px solid #e9ecef;
            }

            .header-title {
                font-size: 1.8em;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 5px;
            }

            .breadcrumb {
                color: #6c757d;
                font-size: 0.9em;
            }

            .content-area {
                padding: 40px;
            }

            .form-container {
                background: white;
                border-radius: 15px;
                padding: 40px;
                box-shadow: 0 5px 20px rgba(0,0,0,0.08);
                border: 1px solid #e9ecef;
                max-width: 800px;
                margin: 0 auto;
            }

            .form-header {
                text-align: center;
                margin-bottom: 40px;
                padding-bottom: 20px;
                border-bottom: 2px solid #f8f9fa;
            }

            .form-header h2 {
                color: #2c3e50;
                font-size: 1.8em;
                margin-bottom: 10px;
            }

            .form-header p {
                color: #6c757d;
                font-size: 1.1em;
            }

            .form-group {
                margin-bottom: 30px;
            }

            .form-label {
                display: block;
                margin-bottom: 10px;
                font-weight: 600;
                color: #2c3e50;
                font-size: 1.1em;
            }

            .required {
                color: #e74c3c;
            }

            .form-input {
                width: 100%;
                padding: 15px;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                font-size: 1em;
                transition: all 0.3s ease;
                font-family: inherit;
            }

            .form-input:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
            }

            .form-textarea {
                resize: vertical;
                min-height: 120px;
            }

            .file-upload-area {
                border: 2px dashed #d1d5db;
                border-radius: 10px;
                padding: 30px;
                text-align: center;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .file-upload-area:hover {
                border-color: #667eea;
                background: #f8f9ff;
            }

            .file-upload-icon {
                font-size: 3em;
                color: #667eea;
                margin-bottom: 15px;
            }

            .file-upload-text {
                color: #6c757d;
                margin-bottom: 10px;
            }

            .file-upload-hint {
                font-size: 0.9em;
                color: #9ca3af;
            }

            .btn-group {
                display: flex;
                gap: 15px;
                margin-top: 40px;
            }

            .btn {
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                font-size: 1.1em;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
            }

            .btn-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                flex: 1;
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(102,126,234,0.4);
            }

            .btn-secondary {
                background: #6c757d;
                color: white;
                flex: 0 0 auto;
            }

            .btn-secondary:hover {
                background: #5a6268;
            }

            .feature-tips {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 25px;
                border-radius: 10px;
                margin-bottom: 30px;
            }

            .feature-tips h4 {
                margin-bottom: 15px;
                font-size: 1.2em;
            }

            .tips-list {
                list-style: none;
                padding: 0;
            }

            .tips-list li {
                margin-bottom: 8px;
                padding-left: 20px;
                position: relative;
            }

            .tips-list li:before {
                content: "✓";
                position: absolute;
                left: 0;
                color: #ffd700;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <div class="logo-title">AI测试平台</div>
                <div class="logo-subtitle">智能 · 专业 · 高效</div>
            </div>

            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="/" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>平台首页</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="/generate" class="nav-link active">
                        <i class="fas fa-magic"></i>
                        <span>测试用例生成</span>
                        <span class="nav-badge">AI</span>
                    </a>
                </div>
            </nav>

            <div class="sidebar-footer">
                <div>版本 v2.0</div>
                <div style="margin-top: 5px;">端口: 8002</div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <div class="header-title">测试用例生成</div>
                <div class="breadcrumb">首页 / 测试用例生成</div>
            </div>

            <div class="content-area">
                <!-- 功能提示 -->
                <div class="feature-tips">
                    <h4><i class="fas fa-lightbulb"></i> AI智能生成特色</h4>
                    <ul class="tips-list">
                        <li>支持多模态文件上传（思维导图、流程图、界面截图）</li>
                        <li>AutoGen + DeepSeek双智能代理协作分析</li>
                        <li>生成专业标准化测试用例</li>
                        <li>支持Excel/CSV格式导出</li>
                    </ul>
                </div>

                <!-- 表单容器 -->
                <div class="form-container">
                    <div class="form-header">
                        <h2><i class="fas fa-magic"></i> AI测试用例生成</h2>
                        <p>请填写项目信息，AI将为您生成专业的测试用例</p>
                    </div>

                    <form id="generateForm">
                        <div class="form-group">
                            <label class="form-label">
                                项目名称 <span class="required">*</span>
                            </label>
                            <input type="text" id="projectName" name="project_name" class="form-input" placeholder="例如：电商平台用户管理系统" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                需求描述 <span class="required">*</span>
                            </label>
                            <textarea id="requirements" name="requirements" class="form-input form-textarea" placeholder="请详细描述功能需求，包括主要功能点、业务流程、用户角色等..." required></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                上下文信息
                            </label>
                            <textarea id="contextInfo" name="context_info" class="form-input form-textarea" style="min-height: 100px;" placeholder="请提供相关的上下文信息，如：&#10;• 系统架构信息&#10;• 技术栈说明&#10;• 业务背景&#10;• 用户群体特征&#10;• 性能要求&#10;• 安全要求&#10;• 集成系统信息等"></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">文件上传（可选）</label>
                            <div class="file-upload-area" onclick="document.getElementById('fileInput').click()">
                                <div class="file-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="file-upload-text">点击上传文件或拖拽文件到此处</div>
                                <div class="file-upload-hint">支持 PNG、JPG、JPEG、GIF、BMP、WEBP 格式，最大 10MB</div>
                                <input type="file" id="fileInput" accept="image/*" style="display: none;">
                            </div>
                        </div>

                        <div class="btn-group">
                            <button type="button" class="btn btn-secondary" onclick="window.location.href='/'">
                                <i class="fas fa-arrow-left"></i> 返回首页
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-rocket"></i> 开始生成测试用例
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script>
            let uploadedFileId = null;

            // 表单提交处理
            document.getElementById('generateForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const projectName = document.getElementById('projectName').value;
                const requirements = document.getElementById('requirements').value;
                const contextInfo = document.getElementById('contextInfo').value;

                if (!projectName || !requirements) {
                    alert('请填写项目名称和需求描述');
                    return;
                }

                // 显示加载状态
                const submitBtn = document.querySelector('.btn-primary');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在生成...';
                submitBtn.disabled = true;

                try {
                    // 准备表单数据
                    const formData = new FormData();
                    formData.append('project_name', projectName);
                    formData.append('requirements', requirements);
                    formData.append('context_info', contextInfo);
                    if (uploadedFileId) {
                        formData.append('file_id', uploadedFileId);
                    }

                    // 调用生成API
                    const response = await fetch('/generate', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 显示结果
                        showResults(result);
                    } else {
                        throw new Error(result.detail || '生成失败');
                    }

                } catch (error) {
                    console.error('生成失败:', error);
                    alert('生成失败: ' + error.message);
                } finally {
                    // 恢复按钮状态
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            });

            // 文件上传处理
            document.getElementById('fileInput').addEventListener('change', async function(e) {
                const file = e.target.files[0];
                if (!file) return;

                // 验证文件类型和大小
                const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    alert('不支持的文件类型，请上传图片文件');
                    return;
                }

                if (file.size > 10 * 1024 * 1024) {
                    alert('文件大小超过10MB限制');
                    return;
                }

                // 显示上传状态
                const uploadArea = document.querySelector('.file-upload-area');
                uploadArea.innerHTML = `
                    <div class="file-upload-icon" style="color: #667eea;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <div class="file-upload-text">正在上传文件...</div>
                `;

                try {
                    // 上传文件
                    const formData = new FormData();
                    formData.append('file', file);

                    const response = await fetch('/upload', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        uploadedFileId = result.file_id;
                        uploadArea.innerHTML = `
                            <div class="file-upload-icon" style="color: #28a745;">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="file-upload-text" style="color: #28a745;">
                                文件上传成功: ${result.original_name}
                            </div>
                            <div class="file-upload-hint">
                                文件大小: ${(result.size / 1024 / 1024).toFixed(2)} MB
                            </div>
                        `;
                    } else {
                        throw new Error(result.detail || '上传失败');
                    }

                } catch (error) {
                    console.error('上传失败:', error);
                    uploadArea.innerHTML = `
                        <div class="file-upload-icon" style="color: #dc3545;">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="file-upload-text" style="color: #dc3545;">
                            上传失败: ${error.message}
                        </div>
                        <div class="file-upload-hint">请重新选择文件</div>
                    `;
                    uploadedFileId = null;
                }
            });

            // 显示生成结果
            function showResults(result) {
                const resultHtml = `
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-top: 20px;">
                        <h3 style="color: #28a745; margin-bottom: 15px;">
                            <i class="fas fa-check-circle"></i> 测试用例生成成功！
                        </h3>
                        <p><strong>项目名称：</strong>${result.project_name}</p>
                        <p><strong>生成时间：</strong>${new Date(result.generated_at).toLocaleString()}</p>
                        <p><strong>AI框架：</strong>${result.agent_info}</p>
                        <p><strong>测试用例数量：</strong>${result.test_cases.length} 个</p>

                        <div style="margin-top: 15px;">
                            <button onclick="exportResults('${result.task_id || 'temp'}')" class="btn btn-primary" style="margin-right: 10px;">
                                <i class="fas fa-download"></i> 导出CSV
                            </button>
                            <button onclick="viewDetails()" class="btn btn-secondary">
                                <i class="fas fa-eye"></i> 查看详情
                            </button>
                        </div>
                    </div>
                `;

                document.querySelector('.form-container').innerHTML += resultHtml;

                // 存储结果供后续使用
                window.currentResult = result;
            }

            // 导出结果
            async function exportResults(taskId) {
                try {
                    const response = await fetch(`/export/${taskId}`, {
                        method: 'POST'
                    });

                    if (response.ok) {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `测试用例_${new Date().getTime()}.csv`;
                        a.click();
                        window.URL.revokeObjectURL(url);
                    } else {
                        throw new Error('导出失败');
                    }
                } catch (error) {
                    alert('导出失败: ' + error.message);
                }
            }

            // 查看详情
            function viewDetails() {
                if (window.currentResult) {
                    const details = window.currentResult.test_cases.map((tc, i) =>
                        `${i+1}. ${tc.title || tc.content || '测试用例'}`
                    ).join('\\n');
                    alert('生成的测试用例：\\n\\n' + details);
                }
            }

            console.log('🎉 测试用例生成页面加载完成');
            console.log('🏢 设计风格: 国企专业风格');
            console.log('🤖 功能: AutoGen + DeepSeek AI智能生成');
        </script>
    </body>
    </html>
    """

if __name__ == "__main__":
    print("🎉 启动简化版AI测试用例生成平台...")
    print("🌐 访问地址: http://localhost:8002")
    print("📋 功能: 首页 + 测试用例生成")
    uvicorn.run(app, host="0.0.0.0", port=8002)

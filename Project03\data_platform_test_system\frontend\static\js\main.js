/**
 * 主应用JavaScript文件
 * 负责页面导航、状态管理和全局功能
 */

// ==================== 全局应用状态 ====================
window.App = {
    currentPage: 'home',
    selectedRequirements: [],
    systemStatus: {
        database: 'unknown',
        ai_service: 'unknown'
    }
};

// ==================== 页面导航管理 ====================
class PageManager {
    constructor() {
        this.currentPage = 'home';
        this.pages = {};
        this.init();
    }
    
    init() {
        // 绑定导航事件
        this.bindNavigationEvents();
        
        // 绑定侧边栏切换
        this.bindSidebarToggle();
        
        // 初始化页面
        this.showPage('home');
        
        // 检查系统状态
        this.checkSystemStatus();
    }
    
    bindNavigationEvents() {
        // 导航项点击事件
        document.querySelectorAll('.nav-item, .nav-subitem').forEach(item => {
            item.addEventListener('click', (e) => {
                const page = item.getAttribute('data-page');
                if (page) {
                    this.showPage(page);
                }
            });
        });
        
        // 功能卡片导航
        document.querySelectorAll('.feature-card[data-navigate]').forEach(card => {
            card.addEventListener('click', (e) => {
                const page = card.getAttribute('data-navigate');
                if (page) {
                    this.showPage(page);
                }
            });
        });
        
        // 子菜单切换
        document.querySelectorAll('.nav-group-header').forEach(header => {
            header.addEventListener('click', (e) => {
                const toggle = header.getAttribute('data-toggle');
                if (toggle) {
                    this.toggleSubmenu(toggle);
                }
            });
        });
    }
    
    bindSidebarToggle() {
        const toggleBtn = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        
        if (toggleBtn && sidebar) {
            toggleBtn.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }
    }
    
    showPage(pageId) {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.add('hidden');
        });
        
        // 显示目标页面
        const targetPage = document.getElementById(`${pageId}-page`);
        if (targetPage) {
            targetPage.classList.remove('hidden');
            this.currentPage = pageId;
            
            // 更新导航状态
            this.updateNavigationState(pageId);
            
            // 更新面包屑
            this.updateBreadcrumb(pageId);
            
            // 初始化页面数据
            this.initPageData(pageId);
        }
    }
    
    updateNavigationState(pageId) {
        // 清除所有活动状态
        document.querySelectorAll('.nav-item, .nav-subitem').forEach(item => {
            item.classList.remove('active');
        });
        
        // 设置当前页面活动状态
        const activeItem = document.querySelector(`[data-page="${pageId}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
            
            // 如果是子菜单项，展开父菜单
            if (activeItem.classList.contains('nav-subitem')) {
                const parentGroup = activeItem.closest('.nav-group');
                if (parentGroup) {
                    const header = parentGroup.querySelector('.nav-group-header');
                    const submenu = parentGroup.querySelector('.nav-submenu');
                    if (header && submenu) {
                        header.classList.add('expanded');
                        submenu.classList.add('expanded');
                    }
                }
            }
        }
    }
    
    updateBreadcrumb(pageId) {
        const breadcrumb = document.getElementById('breadcrumb');
        if (!breadcrumb) return;
        
        const breadcrumbMap = {
            'home': '首页',
            'requirements-list': '需求管理 / 需求列表',
            'requirements-create': '需求管理 / 需求录入',
            'requirements-analysis': '需求管理 / 需求分析列表'
        };
        
        breadcrumb.innerHTML = `<span class="breadcrumb-item">${breadcrumbMap[pageId] || '未知页面'}</span>`;
    }
    
    toggleSubmenu(groupId) {
        const submenu = document.getElementById(`${groupId}-submenu`);
        const header = document.querySelector(`[data-toggle="${groupId}"]`);
        
        if (submenu && header) {
            submenu.classList.toggle('expanded');
            header.classList.toggle('expanded');
        }
    }
    
    async initPageData(pageId) {
        try {
            switch (pageId) {
                case 'home':
                    await this.initHomePage();
                    break;
                case 'requirements-list':
                    await this.initRequirementsListPage();
                    break;
                case 'requirements-create':
                    await this.initRequirementsCreatePage();
                    break;
                case 'requirements-analysis':
                    await this.initRequirementsAnalysisPage();
                    break;
            }
        } catch (error) {
            console.error(`初始化页面 ${pageId} 失败:`, error);
            Utils.error(`页面加载失败: ${error.message}`);
        }
    }
    
    async initHomePage() {
        // 加载首页统计数据
        try {
            if (!window.API) {
                console.error('API对象不存在，无法加载首页数据');
                return;
            }

            const requirementsData = await API.getRequirements({ page: 1, page_size: 1 });
            const totalRequirements = requirementsData.total || 0;

            const analysisData = await API.getAnalysisResults({ page: 1, page_size: 1 });
            const totalAnalyses = analysisData.total || 0;

            // 更新统计数字
            const totalReqElement = document.getElementById('totalRequirements');
            const totalAnalysisElement = document.getElementById('totalAnalyses');

            if (totalReqElement) totalReqElement.textContent = totalRequirements;
            if (totalAnalysisElement) totalAnalysisElement.textContent = totalAnalyses;

        } catch (error) {
            console.error('加载首页数据失败:', error);
        }
    }
    
    async initRequirementsListPage() {
        // 初始化需求列表页面
        console.log('初始化需求列表页面...');
        if (window.RequirementsListPage) {
            console.log('RequirementsListPage对象存在，开始初始化');
            try {
                await window.RequirementsListPage.init();
                console.log('需求列表页面初始化完成');
            } catch (error) {
                console.error('需求列表页面初始化失败:', error);
            }
        } else {
            console.error('RequirementsListPage对象不存在');
        }
    }
    
    async initRequirementsCreatePage() {
        // 初始化需求创建页面
        if (window.RequirementsCreatePage) {
            await window.RequirementsCreatePage.init();
        }
    }
    
    async initRequirementsAnalysisPage() {
        // 初始化需求分析页面
        console.log('初始化需求分析页面...');
        if (window.RequirementsAnalysisPage) {
            console.log('RequirementsAnalysisPage对象存在，开始初始化');
            try {
                await window.RequirementsAnalysisPage.init();
                console.log('需求分析页面初始化完成');
            } catch (error) {
                console.error('需求分析页面初始化失败:', error);
            }
        } else {
            console.error('RequirementsAnalysisPage对象不存在');
        }
    }
    
    async checkSystemStatus() {
        try {
            if (!window.API) {
                console.error('API对象不存在，无法检查系统状态');
                return;
            }

            const status = await API.healthCheck();

            // 更新系统状态
            App.systemStatus = {
                database: status.services?.database?.connected ? 'healthy' : 'error',
                ai_service: status.services?.ai_service?.status || 'unknown'
            };

            // 更新状态显示
            this.updateStatusDisplay(status);

        } catch (error) {
            console.error('系统状态检查失败:', error);
            App.systemStatus = {
                database: 'error',
                ai_service: 'error'
            };
        }
    }
    
    updateStatusDisplay(status) {
        // 更新侧边栏状态
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');
        
        if (statusIndicator && statusText) {
            if (status.status === 'healthy') {
                statusIndicator.style.color = '#4caf50';
                statusText.textContent = '系统正常';
            } else {
                statusIndicator.style.color = '#f44336';
                statusText.textContent = '系统异常';
            }
        }
        
        // 更新首页状态面板
        const dbStatus = document.getElementById('dbStatus');
        const aiStatus = document.getElementById('aiStatus');
        const systemVersion = document.getElementById('systemVersion');
        
        if (dbStatus) {
            dbStatus.textContent = status.services?.database?.connected ? '正常' : '异常';
            dbStatus.className = `status-value ${status.services?.database?.connected ? 'success' : 'error'}`;
        }
        
        if (aiStatus) {
            const aiServiceStatus = status.services?.ai_service?.status;
            aiStatus.textContent = aiServiceStatus === 'ready' ? '就绪' : '异常';
            aiStatus.className = `status-value ${aiServiceStatus === 'ready' ? 'success' : 'error'}`;
        }
        
        if (systemVersion) {
            systemVersion.textContent = status.version || '未知';
        }
    }
}

// ==================== 全局事件处理 ====================
class GlobalEventHandler {
    constructor() {
        this.init();
    }
    
    init() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                window.location.reload();
            });
        }
        
        // 全局键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl+R 刷新
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                window.location.reload();
            }
        });
    }
}

// ==================== 应用初始化 ====================
function initializeApp() {
    console.log('数据中台智能测试系统启动...');

    // 检查依赖对象
    console.log('检查依赖对象...');
    console.log('API对象:', window.API);
    console.log('Utils对象:', window.Utils);
    console.log('RequirementsListPage对象:', window.RequirementsListPage);
    console.log('RequirementsAnalysisPage对象:', window.RequirementsAnalysisPage);

    if (!window.API) {
        console.error('API对象未加载！');
        alert('系统初始化失败：API对象未加载，请刷新页面重试');
        return false;
    }

    // 检查API对象的方法
    const requiredMethods = ['getRequirements', 'getAnalysisResults', 'healthCheck'];
    const missingMethods = requiredMethods.filter(method => typeof window.API[method] !== 'function');

    if (missingMethods.length > 0) {
        console.error('API对象缺少方法:', missingMethods);
        console.log('API对象的所有方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(window.API)));
        alert('系统初始化失败：API对象不完整，缺少方法: ' + missingMethods.join(', '));
        return false;
    }

    if (!window.Utils) {
        console.error('Utils对象未加载！');
        alert('系统初始化失败：Utils对象未加载，请刷新页面重试');
        return false;
    }

    if (!window.RequirementsListPage) {
        console.error('RequirementsListPage对象未加载！');
        alert('系统初始化失败：需求列表页面未加载，请刷新页面重试');
        return false;
    }

    if (!window.RequirementsAnalysisPage) {
        console.error('RequirementsAnalysisPage对象未加载！');
        alert('系统初始化失败：需求分析页面未加载，请刷新页面重试');
        return false;
    }

    try {
        // 初始化页面管理器
        window.PageManager = new PageManager();
        console.log('✅ 页面管理器初始化完成');

        // 初始化全局事件处理
        window.GlobalEventHandler = new GlobalEventHandler();
        console.log('✅ 全局事件处理器初始化完成');

        console.log('🎉 系统初始化完成');
        return true;

    } catch (error) {
        console.error('❌ 系统初始化失败:', error);
        alert('系统初始化失败：' + error.message);
        return false;
    }
}

// 暴露初始化函数到全局，供HTML中的脚本加载器调用
window.initializeApp = initializeApp;

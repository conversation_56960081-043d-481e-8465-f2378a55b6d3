#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用例生成验证脚本
"""

import requests
import json
import time

def test_generation():
    """测试测试用例生成功能"""
    print("🧪 开始测试测试用例生成功能...")
    
    # 测试数据
    test_data = {
        'project_name': '用户管理系统',
        'requirements': '实现用户注册、登录、个人信息管理功能',
        'context_info': '基于Spring Boot + MySQL的Web应用',
        'test_case_count': 3
    }
    
    try:
        # 发送生成请求
        print("📤 发送测试用例生成请求...")
        response = requests.post(
            'http://localhost:8001/api/generate-test-cases',
            data=test_data,
            stream=True
        )
        
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            return False
        
        print("✅ 开始接收流式响应...")
        
        # 处理流式响应
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    try:
                        data = json.loads(line_str[6:])
                        print(f"📨 收到: {data.get('type', 'unknown')} - {data.get('message', '')}")
                        
                        if data.get('type') == 'complete':
                            print("🎉 生成完成!")
                            result = data.get('result')
                            if result:
                                print(f"📊 生成了 {len(result.get('test_cases', []))} 个测试用例")
                                print(f"🆔 任务ID: {result.get('task_id')}")
                                return True
                        elif data.get('type') == 'error':
                            print(f"❌ 生成失败: {data.get('message')}")
                            return False
                            
                    except json.JSONDecodeError as e:
                        print(f"⚠️ JSON解析失败: {e}")
                        continue
        
        print("⚠️ 流式响应结束，但未收到完成信号")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_library():
    """测试测试用例库功能"""
    print("\n📚 测试测试用例库功能...")
    
    try:
        response = requests.get('http://localhost:8001/api/test-cases')
        
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            return False
        
        data = response.json()
        print(f"📋 API响应: {data}")
        
        if data.get('status') == 'success':
            tasks = data.get('tasks', [])
            print(f"✅ 测试用例库中有 {len(tasks)} 个任务")
            
            for task in tasks:
                print(f"  - {task.get('project_name')} ({task.get('total_cases')} 个用例)")
            
            return len(tasks) > 0
        else:
            print(f"❌ 获取失败: {data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_health():
    """测试系统健康状态"""
    print("\n🏥 测试系统健康状态...")
    
    try:
        response = requests.get('http://localhost:8001/api/health')
        
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            return False
        
        data = response.json()
        print(f"✅ 系统状态: {data.get('status')}")
        print(f"🤖 AI框架: {data.get('ai_framework')}")
        print(f"📝 功能: {', '.join(data.get('features', []))}")
        
        return data.get('status') == 'healthy'
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始系统功能测试")
    print("=" * 50)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 测试健康状态
    health_ok = test_health()
    
    if not health_ok:
        print("❌ 系统健康检查失败，停止测试")
        return
    
    # 测试生成功能
    generation_ok = test_generation()
    
    # 等待一下再测试库
    time.sleep(1)
    
    # 测试库功能
    library_ok = test_library()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  🏥 健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
    print(f"  🧪 测试用例生成: {'✅ 通过' if generation_ok else '❌ 失败'}")
    print(f"  📚 测试用例库: {'✅ 通过' if library_ok else '❌ 失败'}")
    
    if health_ok and generation_ok and library_ok:
        print("\n🎉 所有测试通过！系统工作正常")
    else:
        print("\n⚠️ 部分测试失败，请检查系统配置")

if __name__ == "__main__":
    main()

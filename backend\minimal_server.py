from fastapi import FastAPI
from fastapi.responses import HTMLResponse
import uvicorn

app = FastAPI()

@app.get("/", response_class=HTMLResponse)
async def root():
    """返回最小化的测试页面"""
    return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .status {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 数据中台智能测试平台</h1>
        <div class="status">
            ✅ 服务器运行正常
        </div>
        <p>这是一个最小化的测试页面，用于验证服务器基本功能。</p>
        
        <div>
            <button onclick="testJavaScript()">测试JavaScript</button>
            <button onclick="testAPI()">测试API</button>
            <button onclick="location.reload()">刷新页面</button>
        </div>
        
        <div id="result" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; display: none;">
        </div>
    </div>

    <script>
        console.log('页面加载完成');
        
        function testJavaScript() {
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.innerHTML = '<strong>✅ JavaScript测试成功</strong><br>当前时间: ' + new Date().toLocaleString();
        }
        
        function testAPI() {
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.innerHTML = '正在测试API...';
            
            fetch('/health')
                .then(response => response.json())
                .then(data => {
                    result.innerHTML = '<strong>✅ API测试成功</strong><br>响应: ' + JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    result.innerHTML = '<strong>❌ API测试失败</strong><br>错误: ' + error.message;
                });
        }
        
        // 页面加载完成后自动测试
        window.onload = function() {
            console.log('Window加载完成');
            setTimeout(() => {
                testJavaScript();
            }, 1000);
        };
    </script>
</body>
</html>
    """

@app.get("/health")
async def health():
    """健康检查"""
    return {
        "status": "ok",
        "message": "最小化服务器运行正常",
        "timestamp": "2024-01-01 12:00:00"
    }

if __name__ == "__main__":
    print("🚀 启动最小化测试服务器...")
    print("📍 访问地址: http://localhost:8001")
    uvicorn.run(app, host="0.0.0.0", port=8001)

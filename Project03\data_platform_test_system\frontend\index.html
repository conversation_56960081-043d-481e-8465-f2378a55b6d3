<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中台智能测试系统</title>
    <link rel="stylesheet" href="static/css/main.css">
    <link rel="stylesheet" href="static/css/components.css">
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 主容器 -->
    <div class="app-container">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <!-- 系统标题 -->
            <div class="sidebar-header">
                <div class="logo-section">
                    <i class="fas fa-chart-line logo-icon"></i>
                    <h1 class="system-title">数据中台智能测试系统</h1>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <!-- 导航菜单 -->
            <div class="nav-menu">
                <!-- 首页 -->
                <div class="nav-item" data-page="home">
                    <i class="fas fa-home nav-icon"></i>
                    <span class="nav-text">首页</span>
                </div>
                
                <!-- 需求管理 -->
                <div class="nav-group">
                    <div class="nav-item nav-group-header" data-toggle="requirements">
                        <i class="fas fa-tasks nav-icon"></i>
                        <span class="nav-text">需求管理</span>
                        <i class="fas fa-chevron-down nav-arrow"></i>
                    </div>
                    <div class="nav-submenu" id="requirements-submenu">
                        <div class="nav-subitem" data-page="requirements-list">
                            <i class="fas fa-list nav-icon"></i>
                            <span class="nav-text">需求列表</span>
                        </div>
                        <div class="nav-subitem" data-page="requirements-create">
                            <i class="fas fa-plus nav-icon"></i>
                            <span class="nav-text">需求录入</span>
                        </div>
                        <div class="nav-subitem" data-page="requirements-analysis">
                            <i class="fas fa-brain nav-icon"></i>
                            <span class="nav-text">需求分析列表</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部信息 -->
            <div class="sidebar-footer">
                <div class="system-status" id="systemStatus">
                    <i class="fas fa-circle status-indicator"></i>
                    <span class="status-text">系统正常</span>
                </div>
            </div>
        </nav>
        
        <!-- 右侧主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="content-header">
                <div class="breadcrumb" id="breadcrumb">
                    <span class="breadcrumb-item">首页</span>
                </div>
                
                <div class="header-actions">
                    <button class="btn btn-outline" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i>
                        刷新
                    </button>
                    <div class="user-info">
                        <i class="fas fa-user-circle"></i>
                        <span>管理员</span>
                    </div>
                </div>
            </header>
            
            <!-- 页面内容区域 -->
            <div class="page-content" id="pageContent">
                <!-- 首页内容 -->
                <div class="page" id="home-page">
                    <div class="welcome-banner">
                        <h1>🚀 欢迎使用数据中台智能测试系统</h1>
                        <p>基于AutoGen + DeepSeek的智能需求分析平台，提供完整的需求管理和AI分析功能</p>
                    </div>
                    
                    <div class="feature-cards">
                        <div class="feature-card" data-navigate="requirements-list">
                            <div class="card-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <h3>需求列表</h3>
                            <p>查看和管理所有数据开发需求，支持在线编辑和批量操作</p>
                            <div class="card-stats">
                                <span id="totalRequirements">-</span> 个需求
                            </div>
                        </div>
                        
                        <div class="feature-card" data-navigate="requirements-create">
                            <div class="card-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            <h3>需求录入</h3>
                            <p>创建新的数据开发需求，支持富文本编辑和附件上传</p>
                            <div class="card-stats">
                                快速录入
                            </div>
                        </div>
                        
                        <div class="feature-card" data-navigate="requirements-analysis">
                            <div class="card-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h3>AI智能分析</h3>
                            <p>使用AutoGen + DeepSeek进行智能需求分析和建议</p>
                            <div class="card-stats">
                                <span id="totalAnalyses">-</span> 个分析
                            </div>
                        </div>
                        
                        <div class="feature-card" onclick="window.open('/docs', '_blank')">
                            <div class="card-icon">
                                <i class="fas fa-book"></i>
                            </div>
                            <h3>API文档</h3>
                            <p>查看完整的API接口文档和使用说明</p>
                            <div class="card-stats">
                                在线文档
                            </div>
                        </div>
                    </div>
                    
                    <!-- 系统状态面板 -->
                    <div class="status-panel">
                        <h3>系统状态</h3>
                        <div class="status-grid">
                            <div class="status-item">
                                <div class="status-label">数据库连接</div>
                                <div class="status-value" id="dbStatus">检查中...</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">AI服务</div>
                                <div class="status-value" id="aiStatus">检查中...</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">系统版本</div>
                                <div class="status-value" id="systemVersion">-</div>
                            </div>
                            <div class="status-item">
                                <div class="status-label">运行时间</div>
                                <div class="status-value" id="uptime">-</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 需求列表页面 -->
                <div class="page hidden" id="requirements-list-page">
                    <div class="page-header">
                        <h2>需求列表</h2>
                        <div class="page-actions">
                            <button class="btn btn-primary" id="createRequirementBtn">
                                <i class="fas fa-plus"></i>
                                新建需求
                            </button>
                            <button class="btn btn-success" id="batchAnalyzeBtn" disabled>
                                <i class="fas fa-brain"></i>
                                提交AI分析
                            </button>
                        </div>
                    </div>
                    
                    <!-- 筛选和搜索 -->
                    <div class="filter-bar">
                        <div class="filter-group">
                            <label>状态筛选：</label>
                            <select id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="草稿">草稿</option>
                                <option value="待分析">待分析</option>
                                <option value="分析中">分析中</option>
                                <option value="已分析">已分析</option>
                                <option value="已完成">已完成</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>优先级：</label>
                            <select id="priorityFilter">
                                <option value="">全部优先级</option>
                                <option value="高">高</option>
                                <option value="中">中</option>
                                <option value="低">低</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>搜索：</label>
                            <input type="text" id="keywordSearch" placeholder="搜索标题或描述...">
                        </div>
                        <button class="btn btn-outline" id="searchBtn">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                    </div>
                    
                    <!-- 需求表格 -->
                    <div class="table-container">
                        <table class="data-table" id="requirementsTable">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th width="80">ID</th>
                                    <th>标题</th>
                                    <th width="100">优先级</th>
                                    <th width="100">状态</th>
                                    <th width="120">创建时间</th>
                                    <th width="150">操作</th>
                                </tr>
                            </thead>
                            <tbody id="requirementsTableBody">
                                <!-- 动态加载数据 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination" id="requirementsPagination">
                        <!-- 动态生成分页 -->
                    </div>
                </div>
                
                <!-- 需求录入页面 -->
                <div class="page hidden" id="requirements-create-page">
                    <div class="page-header">
                        <h2>需求录入</h2>
                        <div class="page-actions">
                            <button class="btn btn-outline" id="cancelCreateBtn">
                                <i class="fas fa-times"></i>
                                取消
                            </button>
                            <button class="btn btn-primary" id="saveRequirementBtn">
                                <i class="fas fa-save"></i>
                                保存需求
                            </button>
                        </div>
                    </div>
                    
                    <!-- 需求表单 -->
                    <div class="form-container">
                        <form id="requirementForm" class="requirement-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="title" class="required">需求标题</label>
                                    <input type="text" id="title" name="title" required maxlength="255" 
                                           placeholder="请输入需求标题...">
                                    <div class="form-help">请简洁明确地描述需求的核心内容</div>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="description">详细描述</label>
                                    <textarea id="description" name="description" rows="8" 
                                              placeholder="请详细描述需求的背景、目标、功能要求等..."></textarea>
                                    <div class="form-help">支持富文本格式，建议包含需求背景、目标、功能要求等</div>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="priority" class="required">优先级</label>
                                    <select id="priority" name="priority" required>
                                        <option value="">请选择优先级</option>
                                        <option value="高">高</option>
                                        <option value="中">中</option>
                                        <option value="低">低</option>
                                    </select>
                                    <div class="form-help">根据业务重要性和紧急程度选择</div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="createdBy">创建者</label>
                                    <input type="text" id="createdBy" name="created_by" 
                                           placeholder="请输入创建者姓名...">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="modules">关联模块</label>
                                    <div class="module-tags" id="moduleTags">
                                        <div class="tag-input-container">
                                            <input type="text" id="moduleInput" placeholder="输入模块名称后按回车添加...">
                                        </div>
                                    </div>
                                    <div class="form-help">可以添加多个关联模块，用于分类管理</div>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="attachments">附件上传</label>
                                    <div class="upload-area" id="uploadArea">
                                        <div class="upload-placeholder">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <p>点击或拖拽文件到此处上传</p>
                                            <p class="upload-hint">支持 PDF、DOC、DOCX、TXT、PNG、JPG 等格式，最大 10MB</p>
                                        </div>
                                        <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.xlsx,.xls">
                                    </div>
                                    <div class="uploaded-files" id="uploadedFiles">
                                        <!-- 已上传文件列表 -->
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- 需求分析列表页面 -->
                <div class="page hidden" id="requirements-analysis-page">
                    <div class="page-header">
                        <h2>需求分析列表</h2>
                        <div class="page-actions">
                            <button class="btn btn-outline" id="refreshAnalysisBtn">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                        </div>
                    </div>
                    
                    <!-- 分析结果表格 -->
                    <div class="table-container">
                        <table class="data-table" id="analysisTable">
                            <thead>
                                <tr>
                                    <th width="80">分析ID</th>
                                    <th width="80">需求ID</th>
                                    <th>需求标题</th>
                                    <th width="100">AI模型</th>
                                    <th width="100">分析状态</th>
                                    <th width="120">分析时间</th>
                                    <th width="150">操作</th>
                                </tr>
                            </thead>
                            <tbody id="analysisTableBody">
                                <!-- 动态加载数据 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination" id="analysisPagination">
                        <!-- 动态生成分页 -->
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 模态框容器 -->
    <div id="modalContainer"></div>
    
    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>处理中...</p>
        </div>
    </div>
    
    <!-- 引入JavaScript文件 -->
    <script src="static/js/utils.js"></script>
    <script src="static/js/api.js"></script>
    <script src="static/js/components.js"></script>
    <script src="static/js/pages/home.js"></script>
    <script src="static/js/pages/requirements-list.js"></script>
    <script src="static/js/pages/requirements-create.js"></script>
    <script src="static/js/pages/requirements-analysis.js"></script>
    <script src="static/js/main.js"></script>
</body>
</html>

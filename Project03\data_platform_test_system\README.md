# 数据中台智能测试系统

基于AutoGen + DeepSeek的智能需求分析平台，提供完整的需求管理和AI分析功能。

## 🚀 系统特性

### 核心功能
- **需求管理**: 完整的需求CRUD操作，支持在线编辑和批量操作
- **AI智能分析**: 基于AutoGen + DeepSeek的真实AI需求分析
- **实时交互**: 前后端实时数据同步，支持流式输出
- **数据存储**: MySQL数据库，支持复杂查询和事务处理
- **模块化设计**: 清晰的代码结构，易于维护和扩展

### 技术架构
- **后端**: FastAPI + SQLAlchemy + AutoGen + DeepSeek
- **前端**: 原生JavaScript + HTML5 + CSS3（非Vue框架）
- **数据库**: MySQL 8.0+
- **AI引擎**: AutoGen 0.2.0 + DeepSeek API
- **部署**: Python 3.8+ + 一键启动脚本

## 📁 项目结构

```
data_platform_test_system/
├── backend/                    # 后端代码
│   ├── app/                   # 应用核心
│   │   ├── api/              # API路由
│   │   │   └── v1/           # API版本1
│   │   ├── core/             # 核心配置
│   │   ├── models/           # 数据模型
│   │   ├── services/         # 业务逻辑
│   │   ├── utils/            # 工具函数
│   │   └── database/         # 数据库连接
│   ├── uploads/              # 文件上传
│   ├── logs/                 # 日志文件
│   ├── requirements.txt      # Python依赖
│   └── main.py              # 应用入口
├── frontend/                  # 前端代码
│   ├── static/               # 静态资源
│   │   ├── css/             # 样式文件
│   │   ├── js/              # JavaScript文件
│   │   └── images/          # 图片资源
│   ├── templates/            # HTML模板
│   └── components/           # 组件
├── database/                  # 数据库脚本
│   └── init_database.py     # 数据库初始化
├── config/                   # 配置文件
├── docs/                     # 文档
├── tests/                    # 测试代码
├── start_system.py          # 一键启动脚本
└── README.md               # 项目说明
```

## 🛠️ 快速开始

### 环境要求
- Python 3.8+
- MySQL 8.0+
- 8GB+ 内存
- 网络连接（用于AI API调用）

### 1. 安装依赖

```bash
# 安装Python依赖
pip install -r backend/requirements.txt
```

### 2. 配置数据库

确保MySQL服务正在运行，并创建数据库：

```sql
CREATE DATABASE data_platform_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 配置AI服务

编辑 `backend/app/core/config.py`，配置DeepSeek API密钥：

```python
DEEPSEEK_API_KEY = "your-deepseek-api-key"
```

### 4. 一键启动

```bash
python start_system.py
```

启动成功后访问：
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 📋 功能模块

### 需求管理
- **需求列表**: 查看所有需求，支持筛选和搜索
- **需求录入**: 创建新需求，支持富文本和附件上传
- **在线编辑**: 直接在列表中编辑需求信息
- **批量操作**: 选择多个需求进行批量AI分析
- **预检查**: 自动检查必填字段完整性

### AI智能分析
- **真实AI**: 使用AutoGen + DeepSeek进行真实AI分析
- **批量分析**: 支持同时分析多个需求（最多5个）
- **结构化输出**: AI分析结果结构化存储和展示
- **人工调整**: 支持对AI分析结果进行人工补充和修正
- **分析历史**: 完整的分析历史记录和版本管理

### 数据管理
- **MySQL存储**: 使用MySQL数据库存储所有数据
- **事务支持**: 完整的数据库事务处理
- **数据完整性**: 外键约束和数据验证
- **性能优化**: 数据库索引和查询优化

## 🔧 配置说明

### 数据库配置
```python
# backend/app/core/config.py
MYSQL_HOST = "localhost"
MYSQL_PORT = 3306
MYSQL_USER = "root"
MYSQL_PASSWORD = "123456"
MYSQL_DATABASE = "data_platform_test"
```

### AI配置
```python
# DeepSeek API配置
DEEPSEEK_API_KEY = "sk-your-api-key"
DEEPSEEK_BASE_URL = "https://api.deepseek.com"
DEEPSEEK_MODEL = "deepseek-chat"

# AutoGen配置
AUTOGEN_MAX_ROUND = 10
AUTOGEN_TIMEOUT = 300
AUTOGEN_TEMPERATURE = 0.1
```

### 服务配置
```python
# 服务器配置
HOST = "0.0.0.0"
PORT = 8000

# 文件上传配置
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = ["pdf", "doc", "docx", "txt", "png", "jpg", "jpeg"]
```

## 📚 API文档

### 需求管理API

#### 获取需求列表
```http
GET /api/v1/requirements/
```

参数：
- `page`: 页码（默认1）
- `page_size`: 每页大小（默认20）
- `status`: 状态筛选
- `priority`: 优先级筛选
- `keyword`: 关键词搜索

#### 创建需求
```http
POST /api/v1/requirements/
```

请求体：
```json
{
  "title": "需求标题",
  "description": "需求描述",
  "priority": "高",
  "modules": ["模块1", "模块2"],
  "created_by": "创建者"
}
```

#### AI分析需求
```http
POST /api/v1/requirements/ai-analysis
```

请求体：
```json
{
  "requirement_ids": [1, 2, 3]
}
```

### 分析结果API

#### 获取分析结果
```http
GET /api/v1/requirements/analyses/
```

#### 人工调整分析结果
```http
PUT /api/v1/requirements/analyses/{analysis_id}/adjust
```

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd backend
python -m pytest tests/

# 前端测试（如果有）
cd frontend
npm test
```

### 手动测试
1. 访问 http://localhost:3000
2. 创建测试需求
3. 提交AI分析
4. 查看分析结果
5. 进行人工调整

## 🚀 部署

### 开发环境
```bash
python start_system.py
```

### 生产环境
```bash
# 启动后端
cd backend
uvicorn main:app --host 0.0.0.0 --port 8000

# 启动前端（使用Nginx等Web服务器）
# 配置Nginx代理静态文件
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库配置信息
   - 确认数据库用户权限

2. **AI分析失败**
   - 检查DeepSeek API密钥是否正确
   - 验证网络连接
   - 查看后端日志错误信息

3. **前端无法访问**
   - 检查端口3000是否被占用
   - 验证静态文件是否存在
   - 查看浏览器控制台错误

4. **文件上传失败**
   - 检查文件大小是否超限
   - 验证文件格式是否支持
   - 确认上传目录权限

### 日志查看
```bash
# 查看后端日志
tail -f backend/logs/app.log

# 查看系统启动日志
python start_system.py
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

### 开发规范
- 代码注释使用中文
- 遵循PEP 8代码规范
- 提交前运行测试
- 更新相关文档

## 📄 许可证

本项目采用MIT许可证。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**数据中台智能测试系统** - 让需求分析更智能，让开发更高效！

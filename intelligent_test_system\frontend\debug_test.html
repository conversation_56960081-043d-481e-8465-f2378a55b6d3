<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI生成调试测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #1557b0;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log-container {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 8px;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #1a73e8;
        }
        .log-details {
            margin-top: 8px;
            padding: 12px;
            background: #f0f0f0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .log-details.hidden {
            display: none;
        }
        .toggle-details {
            color: #1a73e8;
            cursor: pointer;
            font-size: 12px;
            margin-top: 5px;
        }
        .toggle-details:hover {
            text-decoration: underline;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #1a73e8;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AI生成调试测试</h1>
        <p>测试AI智能测试用例生成功能，检查异步错误和DOM操作问题</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="projectName">项目名称:</label>
                <input type="text" id="projectName" name="project_name" value="测试项目" required>
            </div>
            
            <div class="form-group">
                <label for="requirements">需求描述:</label>
                <textarea id="requirements" name="requirements" required>
数据中台用户管理系统，需要实现用户注册、登录、权限管理等功能。
包括用户信息的增删改查，角色分配，权限控制等核心功能。
                </textarea>
            </div>
            
            <div class="form-group">
                <label for="contextInfo">技术上下文:</label>
                <textarea id="contextInfo" name="context_info">
使用Spring Boot + MySQL + Redis架构
前端使用Vue.js，后端提供RESTful API
需要支持高并发访问，用户量预计10万+
                </textarea>
            </div>
            
            <div class="form-group">
                <label for="testCaseCount">测试用例数量:</label>
                <input type="number" id="testCaseCount" name="test_case_count" value="3" min="1" max="10">
            </div>
            
            <button type="submit" id="generateBtn">🚀 开始AI生成测试用例</button>
        </form>
        
        <div id="progressSection" style="display: none;">
            <h3>生成进度</h3>
            <div class="progress">
                <div class="progress-bar" id="progressBar" style="width: 0%"></div>
            </div>
            <div id="progressText">准备中...</div>
        </div>
        
        <div id="logSection" style="display: none;">
            <h3>生成日志</h3>
            <div class="log-container" id="logContainer"></div>
        </div>
        
        <div id="resultSection" style="display: none;">
            <h3>生成结果</h3>
            <div id="resultContainer"></div>
        </div>
    </div>

    <script>
        let logCounter = 0;
        
        function addLog(message, type = 'info', details = null) {
            const container = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logId = `log-${++logCounter}`;
            
            const typeIcons = {
                'info': '💡',
                'progress': '⚡',
                'prompt': '📝',
                'ai_response': '🤖',
                'success': '✅',
                'error': '❌'
            };
            
            let detailsHtml = '';
            if (details) {
                detailsHtml = `
                    <div class="toggle-details" onclick="toggleDetails('${logId}')">
                        <span id="toggle-${logId}">▶</span> 查看详情
                    </div>
                    <div class="log-details hidden" id="details-${logId}">${escapeHtml(details)}</div>
                `;
            }
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span style="color: #666; font-size: 12px;">[${timestamp}]</span>
                ${typeIcons[type] || '💡'} ${message}
                ${detailsHtml}
            `;
            
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
        }
        
        function toggleDetails(logId) {
            const details = document.getElementById(`details-${logId}`);
            const toggle = document.getElementById(`toggle-${logId}`);
            
            if (details && toggle) {
                details.classList.toggle('hidden');
                toggle.textContent = details.classList.contains('hidden') ? '▶' : '▼';
            }
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function updateProgress(progress) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
            }
            
            if (progressText) {
                progressText.textContent = `进度: ${progress}%`;
            }
        }
        
        document.getElementById('testForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // 显示进度和日志区域
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('logSection').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            
            // 禁用按钮
            const generateBtn = document.getElementById('generateBtn');
            generateBtn.disabled = true;
            generateBtn.textContent = '生成中...';
            
            // 清空日志
            document.getElementById('logContainer').innerHTML = '';
            
            // 重置进度
            updateProgress(0);
            
            addLog('🚀 开始AI测试用例生成...', 'info');
            
            try {
                const formData = new FormData(e.target);
                
                const response = await fetch('/api/generate-test-cases', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                switch (data.type) {
                                    case 'progress':
                                        updateProgress(data.progress || 0);
                                        addLog(data.message, 'progress');
                                        break;
                                    case 'prompt':
                                        addLog(data.message, 'prompt', data.prompt);
                                        break;
                                    case 'ai_response':
                                        addLog(data.message, 'ai_response', data.response);
                                        break;
                                    case 'complete':
                                        updateProgress(100);
                                        addLog(data.message, 'success');
                                        
                                        // 显示结果
                                        if (data.result) {
                                            document.getElementById('resultSection').style.display = 'block';
                                            document.getElementById('resultContainer').innerHTML = `
                                                <p><strong>任务ID:</strong> ${data.result.task_id}</p>
                                                <p><strong>生成时间:</strong> ${data.result.generation_time}</p>
                                                <p><strong>测试用例数量:</strong> ${data.result.test_case_count}</p>
                                                <p><strong>AI框架:</strong> ${data.result.ai_framework}</p>
                                            `;
                                        }
                                        break;
                                    case 'error':
                                        addLog(data.message, 'error', data.error_details);
                                        break;
                                    default:
                                        addLog(data.message, 'info');
                                }
                            } catch (parseError) {
                                console.error('解析数据失败:', parseError, line);
                            }
                        }
                    }
                }
                
            } catch (error) {
                addLog(`请求失败: ${error.message}`, 'error');
                console.error('请求错误:', error);
            } finally {
                // 恢复按钮
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 开始AI生成测试用例';
            }
        });
    </script>
</body>
</html>

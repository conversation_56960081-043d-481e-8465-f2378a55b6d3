"""
系统管理模块 - 负责系统状态监控和配置管理
"""

import json
import psutil
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from prompts_config import get_prompts, update_prompts


class SystemManagementModule:
    """系统管理模块"""
    
    def __init__(self, ai_generator=None):
        self.ai_generator = ai_generator
        self.start_time = datetime.now()
        self.health_checks = {}
        self.system_metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'disk_usage': [],
            'network_io': []
        }
        self.config_history = []
    
    async def check_system_health(self, skip_api_test: bool = False) -> Dict[str, Any]:
        """检查系统健康状态"""
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'status': 'healthy',  # 前端期望的字段
            'overall_status': 'healthy',
            'backend_status': 'healthy',
            'backend_message': '后端服务运行正常',
            'ai_framework_status': 'unknown',
            'ai_framework_message': 'AutoGen框架状态检查中...',
            'ai_framework': 'AutoGen + DeepSeek',  # 前端期望的字段
            'deepseek_status': 'unknown',
            'deepseek_message': 'DeepSeek API连接检查中...',
            'version': '2.0.0',
            'uptime': str(datetime.now() - self.start_time).split('.')[0],
            'features': [  # 前端期望的字段
                'AI智能测试用例生成',
                '需求智能理解',
                '多模态文档分析',
                '流式响应输出',
                'Excel导出功能',
                '提示词管理',
                '系统状态监控'
            ]
        }
        
        try:
            # 检查AI框架状态
            ai_status = await self._check_ai_framework()
            health_status.update(ai_status)

            # 检查DeepSeek API状态（可选）
            if skip_api_test:
                health_status.update({
                    'deepseek_status': 'skipped',
                    'deepseek_message': 'API测试已跳过（避免超时）'
                })
            else:
                deepseek_status = await self._check_deepseek_api()
                health_status.update(deepseek_status)
            
            # 确定整体状态
            statuses = [
                health_status['backend_status'],
                health_status['ai_framework_status'],
                health_status['deepseek_status']
            ]

            if 'error' in statuses:
                health_status['overall_status'] = 'error'
                health_status['status'] = 'error'
            elif 'warning' in statuses:
                health_status['overall_status'] = 'warning'
                health_status['status'] = 'warning'
            else:
                health_status['overall_status'] = 'healthy'
                health_status['status'] = 'healthy'
            
        except Exception as e:
            health_status.update({
                'overall_status': 'error',
                'error_message': f'健康检查失败: {str(e)}'
            })
        
        # 保存健康检查结果
        self.health_checks[datetime.now().isoformat()] = health_status
        
        return health_status
    
    async def _check_ai_framework(self) -> Dict[str, str]:
        """检查AI框架状态"""
        try:
            if not self.ai_generator:
                return {
                    'ai_framework_status': 'error',
                    'ai_framework_message': 'AI生成器未初始化'
                }
            
            # 检查AI代理是否可用
            agents = ['requirement_analyst', 'data_test_expert', 'requirement_understanding']
            available_agents = []
            
            for agent_name in agents:
                if hasattr(self.ai_generator, agent_name):
                    agent = getattr(self.ai_generator, agent_name)
                    if agent:
                        available_agents.append(agent_name)
            
            if len(available_agents) == len(agents):
                return {
                    'ai_framework_status': 'healthy',
                    'ai_framework_message': f'AutoGen框架正常，{len(available_agents)}个AI专家就绪'
                }
            elif len(available_agents) > 0:
                return {
                    'ai_framework_status': 'warning',
                    'ai_framework_message': f'部分AI专家可用 ({len(available_agents)}/{len(agents)})'
                }
            else:
                return {
                    'ai_framework_status': 'error',
                    'ai_framework_message': 'AI专家不可用'
                }
                
        except Exception as e:
            return {
                'ai_framework_status': 'error',
                'ai_framework_message': f'AI框架检查失败: {str(e)}'
            }
    
    async def _check_deepseek_api(self) -> Dict[str, str]:
        """检查DeepSeek API状态"""
        try:
            if not self.ai_generator:
                return {
                    'deepseek_status': 'error',
                    'deepseek_message': 'AI生成器未初始化，无法检查API'
                }

            # 首先尝试简单的配置检查
            try:
                # 检查AI生成器配置
                if hasattr(self.ai_generator, 'llm_config') and self.ai_generator.llm_config:
                    config = self.ai_generator.llm_config.get('config_list', [])
                    if config and len(config) > 0:
                        api_config = config[0]
                        api_key = api_config.get('api_key', '')
                        base_url = api_config.get('base_url', '')
                        model = api_config.get('model', '')

                        if api_key and base_url and model:
                            print(f"🔍 DeepSeek配置检查: {model} @ {base_url}")

                            # 尝试简单的API测试
                            test_prompt = "Hi"

                            response = await asyncio.wait_for(
                                asyncio.to_thread(
                                    self.ai_generator.requirement_analyst.generate_reply,
                                    messages=[{"role": "user", "content": test_prompt}]
                                ),
                                timeout=20.0  # 增加超时时间到20秒
                            )

                            print(f"✅ DeepSeek API响应成功: {str(response)[:100]}...")

                            if response and str(response).strip():
                                return {
                                    'deepseek_status': 'healthy',
                                    'deepseek_message': f'DeepSeek API连接正常 (模型: {model})'
                                }
                            else:
                                return {
                                    'deepseek_status': 'warning',
                                    'deepseek_message': 'DeepSeek API响应为空'
                                }
                        else:
                            return {
                                'deepseek_status': 'error',
                                'deepseek_message': 'DeepSeek API配置不完整'
                            }
                    else:
                        return {
                            'deepseek_status': 'error',
                            'deepseek_message': 'DeepSeek API配置列表为空'
                        }
                else:
                    return {
                        'deepseek_status': 'error',
                        'deepseek_message': 'AI生成器配置缺失'
                    }

            except asyncio.TimeoutError:
                print("⚠️ DeepSeek API响应超时")
                return {
                    'deepseek_status': 'warning',
                    'deepseek_message': 'DeepSeek API响应超时 (20秒) - 可能是网络问题'
                }
            except Exception as api_error:
                print(f"❌ DeepSeek API调用错误: {api_error}")
                # 检查是否是网络相关错误
                error_msg = str(api_error).lower()
                if 'timeout' in error_msg or 'connection' in error_msg:
                    return {
                        'deepseek_status': 'warning',
                        'deepseek_message': f'网络连接问题: {str(api_error)[:100]}'
                    }
                else:
                    return {
                        'deepseek_status': 'error',
                        'deepseek_message': f'API调用失败: {str(api_error)[:100]}'
                    }

        except Exception as e:
            print(f"❌ DeepSeek API检查异常: {e}")
            return {
                'deepseek_status': 'error',
                'deepseek_message': f'DeepSeek API检查失败: {str(e)}'
            }
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统性能指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 网络IO
            network = psutil.net_io_counters()
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'usage_percent': cpu_percent,
                    'count': psutil.cpu_count(),
                    'count_logical': psutil.cpu_count(logical=True)
                },
                'memory': {
                    'total_gb': round(memory.total / (1024**3), 2),
                    'available_gb': round(memory.available / (1024**3), 2),
                    'used_gb': round(memory.used / (1024**3), 2),
                    'usage_percent': memory.percent
                },
                'disk': {
                    'total_gb': round(disk.total / (1024**3), 2),
                    'free_gb': round(disk.free / (1024**3), 2),
                    'used_gb': round(disk.used / (1024**3), 2),
                    'usage_percent': round((disk.used / disk.total) * 100, 2)
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                }
            }
            
            # 保存历史数据（保留最近100个数据点）
            self.system_metrics['cpu_usage'].append({
                'timestamp': metrics['timestamp'],
                'value': cpu_percent
            })
            self.system_metrics['memory_usage'].append({
                'timestamp': metrics['timestamp'],
                'value': memory.percent
            })
            
            # 限制历史数据大小
            for key in self.system_metrics:
                if len(self.system_metrics[key]) > 100:
                    self.system_metrics[key] = self.system_metrics[key][-100:]
            
            return metrics
            
        except Exception as e:
            return {
                'error': f'获取系统指标失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_prompts_config(self) -> Dict[str, Any]:
        """获取提示词配置"""
        try:
            prompts = get_prompts()
            return {
                'status': 'success',
                'prompts': prompts,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f'获取提示词配置失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def update_prompts_config(self, prompts_data: Dict[str, str]) -> Dict[str, Any]:
        """更新提示词配置"""
        try:
            # 记录配置变更历史
            self.config_history.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'update_prompts',
                'data': prompts_data.copy()
            })
            
            # 更新配置
            update_prompts(**prompts_data)
            
            # 更新AI代理的系统消息
            if self.ai_generator:
                if 'requirement_analyst' in prompts_data:
                    self.ai_generator.requirement_analyst.update_system_message(
                        prompts_data['requirement_analyst']
                    )
                if 'data_test_expert' in prompts_data:
                    self.ai_generator.data_test_expert.update_system_message(
                        prompts_data['data_test_expert']
                    )
                if 'document_analyst' in prompts_data:
                    self.ai_generator.document_analyst.update_system_message(
                        prompts_data['document_analyst']
                    )
                if 'requirement_understanding' in prompts_data:
                    self.ai_generator.requirement_understanding.update_system_message(
                        prompts_data['requirement_understanding']
                    )
            
            return {
                'status': 'success',
                'message': '提示词更新成功（已应用到AI代理和配置文件）',
                'updated_prompts': {
                    key: value[:100] + "..." if len(value) > 100 else value
                    for key, value in prompts_data.items()
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'更新提示词失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_config_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取配置变更历史"""
        return self.config_history[-limit:] if self.config_history else []
    
    def get_health_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取健康检查历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return [
            check for timestamp, check in self.health_checks.items()
            if datetime.fromisoformat(timestamp.replace('Z', '+00:00').replace('+00:00', '')) > cutoff_time
        ]
    
    def get_module_stats(self) -> Dict[str, Any]:
        """获取模块统计信息"""
        return {
            'module_name': 'SystemManagement',
            'uptime': str(datetime.now() - self.start_time).split('.')[0],
            'health_checks_count': len(self.health_checks),
            'config_changes_count': len(self.config_history),
            'last_health_check': max(self.health_checks.keys()) if self.health_checks else None,
            'ai_generator_available': self.ai_generator is not None
        }
    
    def cleanup_old_data(self, days: int = 7):
        """清理旧数据"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        # 清理旧的健康检查记录
        old_checks = [
            timestamp for timestamp in self.health_checks.keys()
            if datetime.fromisoformat(timestamp.replace('Z', '+00:00').replace('+00:00', '')) < cutoff_time
        ]
        
        for timestamp in old_checks:
            del self.health_checks[timestamp]
        
        # 清理旧的配置历史
        self.config_history = [
            config for config in self.config_history
            if datetime.fromisoformat(config['timestamp'].replace('Z', '+00:00').replace('+00:00', '')) > cutoff_time
        ]
        
        return {
            'cleaned_health_checks': len(old_checks),
            'remaining_config_history': len(self.config_history)
        }

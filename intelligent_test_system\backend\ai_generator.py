"""
AI测试用例生成器 - 基于AutoGen + DeepSeek
"""

import json
import uuid
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, AsyncGenerator
from pathlib import Path

# AutoGen导入 - 使用正确的导入路径
try:
    from autogen import AssistantAgent, UserProxyAgent
    import autogen
    AUTOGEN_AVAILABLE = True
    AUTOGEN_VERSION = getattr(autogen, '__version__', 'unknown')
    print(f"✅ AutoGen导入成功 (版本: {AUTOGEN_VERSION})")
except ImportError as e:
    AUTOGEN_AVAILABLE = False
    AUTOGEN_VERSION = None
    print(f"❌ AutoGen导入失败: {e}")

# 导入配置
from config import get_deepseek_config, validate_api_key, get_api_key_status


class AITestCaseGenerator:
    """AI测试用例生成器 - 基于AutoGen + DeepSeek真正AI模式"""
    
    def __init__(self):
        """初始化AI代理团队"""
        # 检查API密钥配置
        if not validate_api_key():
            api_status = get_api_key_status()
            print(f"⚠️ {api_status['message']}")
            print("📝 请配置您的DeepSeek API密钥，详见 backend/config.py")

        self.llm_config = get_deepseek_config()
        self._setup_agents()
        print("✅ AutoGen + DeepSeek AI代理团队初始化完成")
    
    def _setup_agents(self):
        """设置AutoGen代理团队"""
        if not AUTOGEN_AVAILABLE:
            raise Exception("❌ AutoGen不可用，无法使用真实AI模式。请安装AutoGen: pip install pyautogen")

        try:
            # 使用经过测试的AutoGen配置格式
            print(f"📋 使用配置: {json.dumps(self.llm_config, indent=2, ensure_ascii=False)}")
            
            # 需求分析专家
            self.requirement_analyst = AssistantAgent(
                name="requirement_analyst",
                llm_config=self.llm_config,
                system_message="""你是一位资深的数据中台需求分析专家，拥有10年以上的数据平台项目经验。

你的专业职责：
1. 深度分析用户提供的需求描述、技术上下文和上传的文件
2. 识别数据流程中的关键测试点和潜在风险场景
3. 理解业务流程、数据血缘关系和系统架构
4. 为测试用例生成提供专业的需求解读和测试策略

请用专业、准确的语言进行分析，确保测试覆盖全面。"""
            )
            
            # 数据测试专家
            self.data_test_expert = AssistantAgent(
                name="data_test_expert",
                llm_config=self.llm_config,
                system_message="""你是一位数据测试领域的资深专家，专注于数据中台和大数据系统的全面测试。

你的专业领域包括：
1. 数据质量测试：完整性、准确性、一致性、及时性、有效性
2. ETL流程测试：数据抽取、转换、加载的各个环节和异常处理
3. 数据仓库测试：维度建模、事实表、数据血缘、历史数据
4. 实时数据流测试：Kafka、Flink、Storm等流处理框架
5. 数据API测试：接口性能、数据格式、异常处理、安全性
6. 数据治理测试：数据标准、元数据管理、数据安全

请生成专业、全面、可执行的数据测试用例，包含详细的测试步骤和验证点。

**重要：请严格按照以下JSON格式输出测试用例数组：**

```json
[
  {
    "title": "测试用例标题",
    "objective": "测试目标描述",
    "preconditions": "前置条件",
    "test_steps": "详细测试步骤（用\\n分隔）",
    "expected_result": "预期结果",
    "priority": "高/中/低",
    "test_type": "测试类型",
    "risk_level": "高/中/低",
    "business_impact": "业务影响描述",
    "test_data": "测试数据要求",
    "automation_feasibility": "高/中/低"
  }
]
```

确保输出的是有效的JSON格式，不要包含其他文字说明。"""
            )
            
            # 文档分析专家
            self.document_analyst = AssistantAgent(
                name="document_analyst",
                llm_config=self.llm_config,
                system_message="""你是一位文档分析专家，擅长分析各种项目文档和需求材料。

你的能力包括：
1. 分析项目需求文档中的业务逻辑和数据流向
2. 理解技术架构文档中的系统设计和组件关系
3. 识别用户上传文件的类型和可能包含的关键信息
4. 基于文件名和描述推断文档内容和测试要点

请根据用户提供的文件信息，推断可能的测试场景和关键要素。"""
            )
            
            # 需求智能理解专家
            self.requirement_understanding = AssistantAgent(
                name="requirement_understanding",
                llm_config=self.llm_config,
                system_message="""你是一位资深的需求分析和产品设计专家，擅长从各种文档、图表和界面中提取和理解业务需求。

你的专业能力包括：
1. 分析思维导图中的业务逻辑和功能模块
2. 理解流程图中的业务流程和决策点
3. 识别界面截图中的功能特性和用户交互
4. 提取文档中的关键需求信息
5. 将非结构化信息转换为结构化的需求描述

## 输出要求：
请严格按照以下JSON格式输出结构化需求描述。

**重要：只输出JSON对象，不要包含任何解释文字、markdown标记或代码块标记。**

{
  "project_overview": {
    "project_name": "项目名称",
    "project_description": "项目整体描述",
    "business_domain": "业务领域",
    "target_users": "目标用户群体"
  },
  "functional_requirements": [
    {
      "module_name": "功能模块名称",
      "module_description": "模块详细描述",
      "priority": "高/中/低",
      "complexity": "高/中/低",
      "sub_functions": [
        {
          "function_name": "子功能名称",
          "function_description": "子功能描述",
          "input_data": "输入数据要求",
          "output_data": "输出数据格式",
          "business_rules": "业务规则说明"
        }
      ]
    }
  ],
  "non_functional_requirements": [
    {
      "category": "性能/安全/可用性/兼容性",
      "requirement": "具体要求描述",
      "acceptance_criteria": "验收标准"
    }
  ],
  "data_requirements": [
    {
      "data_entity": "数据实体名称",
      "data_description": "数据描述",
      "data_fields": [
        {
          "field_name": "字段名称",
          "field_type": "字段类型",
          "field_description": "字段说明",
          "is_required": true,
          "constraints": "约束条件"
        }
      ]
    }
  ]
}

## 重要提醒：
1. 只输出JSON对象，不要包含markdown代码块标记
2. 确保JSON格式正确，所有字符串都用双引号
3. 根据分析的内容填充相应的字段，如果某个部分没有信息可以设为空数组或空字符串
4. 优先级和复杂度使用"高"、"中"、"低"三个等级
5. 布尔值使用true/false，不要用字符串"""
            )
            
            print("✅ AutoGen AI代理团队设置完成")
            
        except Exception as e:
            print(f"❌ AutoGen代理设置失败: {e}")
            raise Exception(f"AutoGen + DeepSeek 初始化失败: {e}。请检查API密钥和网络连接。")
    
    async def test_ai_connection(self) -> Dict[str, Any]:
        """测试AI连接"""
        try:
            test_message = "请简单回复：AutoGen + DeepSeek连接测试成功"

            # 使用需求分析专家进行测试
            response = await asyncio.to_thread(
                self.requirement_analyst.generate_reply,
                messages=[{"role": "user", "content": test_message}]
            )

            return {
                "status": "success",
                "message": "AutoGen + DeepSeek连接正常",
                "ai_response": str(response)[:200] + "..." if len(str(response)) > 200 else str(response),
                "model": "deepseek-chat"
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"AutoGen + DeepSeek连接失败: {str(e)}",
                "error_details": str(e)
            }

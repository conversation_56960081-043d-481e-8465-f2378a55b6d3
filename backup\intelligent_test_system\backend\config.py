"""
配置文件 - 管理API密钥和系统配置
"""

import os
from typing import Dict, Any

# DeepSeek API配置
# 请在这里设置您的真实API密钥
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "***********************************")
DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"
DEEPSEEK_MODEL = "deepseek-chat"

# 系统配置
SYSTEM_CONFIG = {
    "app_name": "数据中台智能助手",
    "version": "2.0.0",
    "description": "基于AutoGen + DeepSeek的AI驱动数据中台智能助手",
    "host": "0.0.0.0",
    "port": 8000,
    "debug": False
}

# AI配置 - 修复为AutoGen兼容格式
AI_CONFIG = {
    "temperature": 0.7,
    "timeout": 120,
    "max_tokens": 4000,
    "cache_seed": None  # 禁用缓存，确保真实调用
}

def get_deepseek_config() -> Dict[str, Any]:
    """获取DeepSeek配置 - AutoGen兼容格式"""
    return {
        "config_list": [
            {
                "model": DEEPSEEK_MODEL,
                "api_key": DEEPSEEK_API_KEY,
                "base_url": DEEPSEEK_BASE_URL,
                "api_type": "openai"
            }
        ],
        "temperature": AI_CONFIG["temperature"],
        "timeout": AI_CONFIG["timeout"],
        "cache_seed": AI_CONFIG["cache_seed"]
    }

def validate_api_key() -> bool:
    """验证API密钥是否已设置"""
    return DEEPSEEK_API_KEY != "your-deepseek-api-key-here" and len(DEEPSEEK_API_KEY) > 10

def get_api_key_status() -> Dict[str, Any]:
    """获取API密钥状态"""
    is_valid = validate_api_key()
    return {
        "is_configured": is_valid,
        "api_key_preview": DEEPSEEK_API_KEY[:10] + "..." if is_valid else "未配置",
        "base_url": DEEPSEEK_BASE_URL,
        "model": DEEPSEEK_MODEL,
        "message": "API密钥已配置" if is_valid else "请配置您的DeepSeek API密钥"
    }

# 环境变量配置说明
CONFIG_HELP = """
🔑 DeepSeek API密钥配置方法：

方法1: 修改配置文件
编辑 backend/config.py 文件，将 DEEPSEEK_API_KEY 设置为您的真实API密钥

方法2: 使用环境变量
设置环境变量 DEEPSEEK_API_KEY=your-actual-api-key

方法3: 在启动时设置
Windows: set DEEPSEEK_API_KEY=your-actual-api-key && python main.py
Linux/Mac: DEEPSEEK_API_KEY=your-actual-api-key python main.py

📝 获取API密钥：
1. 访问 https://platform.deepseek.com/
2. 注册账号并登录
3. 在API密钥页面创建新的密钥
4. 复制密钥并配置到系统中

⚠️ 安全提醒：
- 不要将API密钥提交到版本控制系统
- 定期轮换API密钥
- 监控API使用量和费用
"""

if __name__ == "__main__":
    print("🔑 DeepSeek API配置状态:")
    status = get_api_key_status()
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    if not status["is_configured"]:
        print("\n" + CONFIG_HELP)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统核心配置模块
负责管理所有系统配置参数，包括数据库、AI、文件上传等配置
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    """系统配置类 - 统一管理所有配置参数"""
    
    # ==================== 应用基础配置 ====================
    APP_NAME: str = "数据中台智能测试系统"
    APP_VERSION: str = "2.0.0"
    APP_DESCRIPTION: str = "基于AutoGen + DeepSeek的智能需求分析平台"
    DEBUG: bool = True
    
    # ==================== 服务器配置 ====================
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    API_V1_PREFIX: str = "/api/v1"
    
    # ==================== 数据库配置 ====================
    # MySQL数据库连接配置
    MYSQL_HOST: str = Field(default="localhost", description="MySQL服务器地址")
    MYSQL_PORT: int = Field(default=3306, description="MySQL端口号")
    MYSQL_USER: str = Field(default="root", description="MySQL用户名")
    MYSQL_PASSWORD: str = Field(default="123456", description="MySQL密码")
    MYSQL_DATABASE: str = Field(default="data_platform_test", description="数据库名称")
    MYSQL_CHARSET: str = Field(default="utf8mb4", description="数据库字符集")
    
    # 数据库连接池配置
    DB_POOL_SIZE: int = Field(default=10, description="连接池大小")
    DB_MAX_OVERFLOW: int = Field(default=20, description="连接池最大溢出")
    DB_POOL_TIMEOUT: int = Field(default=30, description="连接池超时时间")
    DB_POOL_RECYCLE: int = Field(default=3600, description="连接回收时间")
    
    # ==================== AI配置 - DeepSeek ====================
    # DeepSeek API配置
    DEEPSEEK_API_KEY: str = Field(
        default="***********************************", 
        description="DeepSeek API密钥"
    )
    DEEPSEEK_BASE_URL: str = Field(
        default="https://api.deepseek.com", 
        description="DeepSeek API基础URL"
    )
    DEEPSEEK_MODEL: str = Field(
        default="deepseek-chat", 
        description="DeepSeek模型名称"
    )
    
    # AutoGen配置参数
    AUTOGEN_CACHE_SEED: int = Field(default=42, description="AutoGen缓存种子")
    AUTOGEN_MAX_ROUND: int = Field(default=10, description="AutoGen最大对话轮数")
    AUTOGEN_TIMEOUT: int = Field(default=300, description="AutoGen超时时间(秒)")
    AUTOGEN_TEMPERATURE: float = Field(default=0.1, description="AI模型温度参数")
    AUTOGEN_MAX_TOKENS: int = Field(default=2000, description="AI模型最大令牌数")
    
    # ==================== 文件上传配置 ====================
    # 文件上传路径和限制
    UPLOAD_DIR: str = Field(default="backend/uploads", description="文件上传目录")
    MAX_FILE_SIZE: int = Field(default=10 * 1024 * 1024, description="最大文件大小(10MB)")
    ALLOWED_EXTENSIONS: List[str] = Field(
        default=["pdf", "doc", "docx", "txt", "png", "jpg", "jpeg", "xlsx", "xls"],
        description="允许的文件扩展名"
    )
    
    # ==================== 日志配置 ====================
    # 日志相关配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_DIR: str = Field(default="backend/logs", description="日志文件目录")
    LOG_FILE_MAX_SIZE: int = Field(default=10 * 1024 * 1024, description="日志文件最大大小")
    LOG_FILE_BACKUP_COUNT: int = Field(default=5, description="日志文件备份数量")
    
    # ==================== 安全配置 ====================
    # CORS跨域配置
    CORS_ORIGINS: List[str] = Field(
        default=[
            "http://localhost:3000",
            "http://127.0.0.1:3000",
            "http://localhost:8080",
            "http://127.0.0.1:8080"
        ],
        description="允许的跨域源"
    )
    CORS_ALLOW_CREDENTIALS: bool = Field(default=True, description="允许跨域凭证")
    CORS_ALLOW_METHODS: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        description="允许的HTTP方法"
    )
    CORS_ALLOW_HEADERS: List[str] = Field(
        default=["*"],
        description="允许的HTTP头"
    )
    
    # JWT认证配置（预留）
    SECRET_KEY: str = Field(
        default="your-secret-key-here-change-in-production",
        description="JWT密钥"
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="访问令牌过期时间(分钟)")
    
    # ==================== 业务配置 ====================
    # 分页配置
    DEFAULT_PAGE_SIZE: int = Field(default=20, description="默认分页大小")
    MAX_PAGE_SIZE: int = Field(default=100, description="最大分页大小")
    
    # 需求分析配置
    ANALYSIS_BATCH_SIZE: int = Field(default=5, description="批量分析最大数量")
    ANALYSIS_RETRY_TIMES: int = Field(default=3, description="分析失败重试次数")
    
    # ==================== 计算属性 ====================
    @property
    def database_url(self) -> str:
        """获取同步数据库连接URL"""
        return (
            f"mysql+pymysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}"
            f"@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{self.MYSQL_DATABASE}"
            f"?charset={self.MYSQL_CHARSET}"
        )
    
    @property
    def async_database_url(self) -> str:
        """获取异步数据库连接URL"""
        return (
            f"mysql+aiomysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}"
            f"@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{self.MYSQL_DATABASE}"
            f"?charset={self.MYSQL_CHARSET}"
        )
    
    @property
    def deepseek_config(self) -> dict:
        """获取DeepSeek配置字典"""
        return {
            "model": self.DEEPSEEK_MODEL,
            "api_key": self.DEEPSEEK_API_KEY,
            "base_url": self.DEEPSEEK_BASE_URL,
            "api_type": "openai"
            # 移除api_version参数，新版本OpenAI客户端不支持
        }
    
    @property
    def autogen_llm_config(self) -> dict:
        """获取AutoGen LLM配置"""
        return {
            "config_list": [self.deepseek_config],
            "cache_seed": self.AUTOGEN_CACHE_SEED,
            "temperature": self.AUTOGEN_TEMPERATURE,
            "max_tokens": self.AUTOGEN_MAX_TOKENS,
            "timeout": self.AUTOGEN_TIMEOUT
        }
    
    # ==================== 配置验证 ====================
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        errors = []
        
        # 验证数据库配置
        if not self.MYSQL_HOST:
            errors.append("MySQL主机地址不能为空")
        
        if not self.MYSQL_USER:
            errors.append("MySQL用户名不能为空")
        
        if not self.MYSQL_DATABASE:
            errors.append("数据库名称不能为空")
        
        # 验证AI配置
        if not self.DEEPSEEK_API_KEY or self.DEEPSEEK_API_KEY == "your-api-key-here":
            errors.append("DeepSeek API密钥未配置")
        
        # 验证文件上传配置
        if self.MAX_FILE_SIZE <= 0:
            errors.append("最大文件大小必须大于0")
        
        if errors:
            print(" 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        return True
    
    class Config:
        """Pydantic配置类"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# ==================== 全局配置实例 ====================
# 创建全局配置实例
settings = Settings()

# ==================== 初始化函数 ====================
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.UPLOAD_DIR,
        settings.LOG_DIR,
        "backend/uploads",
        "backend/logs",
        "frontend/static/uploads"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def init_config():
    """初始化配置"""
    try:
        print("初始化系统配置...")

        # 创建必要目录
        ensure_directories()

        # 验证配置
        if settings.validate_config():
            print("配置验证通过")
        else:
            print("配置验证失败，请检查配置参数")
            return False

        print(f"系统配置:")
        print(f"  - 应用名称: {settings.APP_NAME}")
        print(f"  - 版本: {settings.APP_VERSION}")
        print(f"  - 调试模式: {settings.DEBUG}")
        print(f"  - 服务地址: {settings.HOST}:{settings.PORT}")
        print(f"  - 数据库: {settings.MYSQL_HOST}:{settings.MYSQL_PORT}/{settings.MYSQL_DATABASE}")
        print(f"  - AI模型: {settings.DEEPSEEK_MODEL}")

        return True
    except UnicodeEncodeError:
        # Windows编码问题的备用方案
        return True

# 自动初始化
if __name__ != "__main__":
    init_config()

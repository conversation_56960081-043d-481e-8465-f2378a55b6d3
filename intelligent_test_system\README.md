# 🧠 数据中台智能测试系统

基于 **AutoGen + DeepSeek** 的AI驱动测试用例生成平台

## 🎯 系统特性

### ✅ 完全满足您的需求
- **真正的AutoGen + DeepSeek实现** - 使用您的API密钥，非模拟模式
- **国企风格界面设计** - 专业、美观、易用的用户界面
- **多模态文件支持** - 支持思维导图、流程图、界面截图分析
- **AI专家团队协作** - 需求分析师 + 数据测试专家 + 多模态分析师
- **流式实时输出** - 实时显示AI生成过程和详细日志
- **专业Excel导出** - 包含完整测试用例信息的标准格式
- **Gemini风格设计** - 现代化、一致性的界面体验

### 🤖 AI专家团队
1. **需求分析专家** - 深度分析项目需求和技术上下文
2. **数据测试专家** - 生成专业的数据中台测试用例
3. **多模态分析师** - 分析上传的图片和文档内容

### 📋 测试用例字段
- 用例编号、测试标题、测试目标
- 前置条件、详细测试步骤、预期结果
- 优先级、测试类型、风险等级
- 业务影响、测试数据、自动化可行性
- 预估时间、创建时间

## 🚀 快速启动

### 方法1: 一键启动（推荐）
```bash
# Windows
双击 start.bat

# 或使用Python
python start.py
```

### 方法2: 手动启动
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动后端
cd backend
python main.py

# 3. 访问系统
# 浏览器打开: http://localhost:8000
```

## 🌐 访问地址
- **主系统**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/health

## 📁 系统架构

```
intelligent_test_system/
├── backend/
│   └── main.py              # FastAPI后端 + AutoGen + DeepSeek
├── frontend/
│   └── index.html           # Gemini风格前端界面
├── uploads/                 # 文件上传目录
├── exports/                 # Excel导出目录
├── requirements.txt         # 依赖包列表
├── start.py                # Python启动脚本
├── start.bat               # Windows启动脚本
└── README.md               # 系统文档
```

## 🎨 界面特性

### 国企风格设计
- 左侧导航栏，专业布局
- 蓝色主色调，金色点缀
- 清晰的层次结构和信息展示

### Gemini风格元素
- 现代化卡片设计
- 流畅的动画效果
- 一致的视觉语言
- 优雅的交互体验

## 🔧 技术栈

### 后端技术
- **FastAPI** - 高性能Web框架
- **AutoGen** - 多代理AI协作框架
- **DeepSeek** - 大语言模型API
- **Pandas + OpenPyXL** - Excel文件处理

### 前端技术
- **原生HTML/CSS/JavaScript** - 轻量级实现
- **Gemini设计语言** - Google风格界面
- **响应式设计** - 支持多设备访问

### AI能力
- **多模态分析** - 图片、文档内容理解
- **专家协作** - 多个AI代理协同工作
- **流式输出** - 实时生成过程展示
- **专业生成** - 数据中台专业测试用例

## 📊 功能模块

### 1. 系统首页
- 系统介绍和特性展示
- 快速入口和操作指南

### 2. AI智能生成
- 项目信息输入
- 多模态文件上传
- 实时生成进度
- AI协作日志

### 3. 测试用例库
- 用例列表展示
- 统计信息分析
- Excel导出功能

### 4. 系统状态
- 健康状态监控
- AI服务状态
- 系统信息展示

## 🔑 API配置

系统使用您提供的DeepSeek API密钥：
```
API Key: ***********************************
Base URL: https://api.deepseek.com/v1
Model: deepseek-chat
```

## 📝 使用说明

### 1. 项目信息填写
- **项目名称**: 输入数据中台项目名称
- **需求描述**: 详细描述测试需求和业务场景
- **技术上下文**: 描述技术架构和工具栈

### 2. 文件上传（可选）
- 支持思维导图、流程图、界面截图
- 支持PDF、Word、Excel等文档
- 文件大小限制：10MB

### 3. AI生成过程
- 实时显示生成进度
- 展示AI专家团队协作日志
- 自动跳转到结果页面

### 4. 结果查看和导出
- 查看生成的测试用例
- 统计信息分析
- 一键导出Excel文件

## 🎉 系统优势

1. **真正的AI模式** - 使用AutoGen + DeepSeek，非模拟
2. **专业测试用例** - 针对数据中台的专业内容
3. **多模态支持** - 图片、文档智能分析
4. **实时反馈** - 流式输出，实时查看进度
5. **标准化输出** - 专业Excel格式，便于使用
6. **用户友好** - 简洁界面，操作直观

## 🔧 故障排除

### 常见问题
1. **依赖安装失败** - 检查Python版本和网络连接
2. **AI生成失败** - 检查DeepSeek API密钥和网络
3. **文件上传失败** - 检查文件大小和格式
4. **Excel导出失败** - 检查磁盘空间和权限

### 技术支持
- 检查系统状态页面
- 查看AI生成日志
- 确认API连接正常

---

**🎊 享受AI驱动的智能测试用例生成体验！**

/**
 * 主应用程序 - 负责应用初始化和模块协调
 */

class App {
    constructor() {
        this.modules = {};
        this.config = {
            version: '2.0.0',
            name: '数据中台智能助手',
            apiBaseUrl: '/api'
        };
        this.init();
    }

    init() {
        this.setupGlobalErrorHandling();
        this.initializeModules();
        this.setupGlobalEventListeners();
        this.startApplication();
    }

    setupGlobalErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
            this.showNotification('系统错误: ' + event.error.message, 'error');
        });

        // Promise 错误处理
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise错误:', event.reason);
            this.showNotification('异步操作错误: ' + event.reason, 'error');
        });
    }

    initializeModules() {
        // 等待所有模块加载完成
        this.waitForModules().then(() => {
            this.modules = {
                navigation: window.navigationModule,
                requirement: window.requirementModule,
                assets: window.requirementAssetsModule,
                testCase: window.testCaseModule,
                api: window.apiInterfaceModule,
                system: window.systemModule
            };

            console.log('所有模块已加载:', Object.keys(this.modules));
            this.setupModuleInteractions();
        });
    }

    async waitForModules() {
        const maxWait = 5000; // 最大等待5秒
        const startTime = Date.now();

        while (Date.now() - startTime < maxWait) {
            if (window.navigationModule && 
                window.requirementModule && 
                window.requirementAssetsModule && 
                window.testCaseModule && 
                window.apiInterfaceModule && 
                window.systemModule) {
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.warn('部分模块加载超时');
    }

    setupModuleInteractions() {
        // 设置模块间的交互
        
        // 需求理解完成后自动保存到资产库
        if (this.modules.requirement && this.modules.assets) {
            const originalHandleComplete = this.modules.requirement.handleComplete;
            this.modules.requirement.handleComplete = (data) => {
                originalHandleComplete.call(this.modules.requirement, data);
                if (data.result) {
                    this.modules.assets.saveAsset(data.result);
                }
            };
        }

        // 导航模块与其他模块的集成
        if (this.modules.navigation) {
            this.modules.navigation.loadRequirementAssets = () => {
                if (this.modules.assets) {
                    this.modules.assets.loadAssets();
                }
            };

            this.modules.navigation.loadTestCasesLibrary = () => {
                if (this.modules.testCase) {
                    this.modules.testCase.loadTestCasesLibrary();
                }
            };

            this.modules.navigation.checkSystemHealth = () => {
                if (this.modules.system) {
                    this.modules.system.checkSystemHealth();
                }
            };

            this.modules.navigation.loadPrompts = () => {
                if (this.modules.system) {
                    this.modules.system.loadPrompts();
                }
            };
        }
    }

    setupGlobalEventListeners() {
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            this.onDOMReady();
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.onPageVisible();
            }
        });

        // 网络状态变化
        window.addEventListener('online', () => {
            this.showNotification('网络连接已恢复', 'success');
        });

        window.addEventListener('offline', () => {
            this.showNotification('网络连接已断开', 'warning');
        });
    }

    onDOMReady() {
        console.log('DOM已加载完成');
        this.addSystemLog('🎉 数据中台智能助手已就绪', 'success');
        this.addSystemLog('🤖 AutoGen + DeepSeek AI专家团队待命中', 'info');
        
        // 检查系统健康状态
        if (this.modules.system) {
            this.modules.system.checkSystemHealth();
        }
    }

    onPageVisible() {
        console.log('页面重新可见');
        // 可以在这里刷新数据或检查状态
    }

    startApplication() {
        console.log(`${this.config.name} v${this.config.version} 启动中...`);
        
        // 显示启动信息
        this.showNotification(`${this.config.name} 已启动`, 'success');
    }

    // 通用工具方法
    showNotification(message, type = 'info', duration = 3000) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-white);
            border: 1px solid var(--border-light);
            border-radius: 8px;
            padding: 12px 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 10000;
            max-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        const typeColors = {
            'success': 'var(--success-green)',
            'error': 'var(--error-red)',
            'warning': 'var(--warning-orange)',
            'info': 'var(--primary-blue)'
        };

        const typeIcons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': '💡'
        };

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="color: ${typeColors[type]};">${typeIcons[type]}</span>
                <span style="color: var(--text-primary);">${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    addSystemLog(message, type = 'info') {
        // 添加系统日志到AI日志区域
        const log = document.getElementById('aiLog');
        if (!log) return;

        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';

        const typeIcons = {
            'info': '💡',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️'
        };

        logEntry.innerHTML = `
            <span class="log-timestamp">[${timestamp}]</span> 
            <span class="log-agent">[系统]</span> ${typeIcons[type] || '💡'} ${message}
        `;

        log.appendChild(logEntry);
        log.scrollTop = log.scrollHeight;
    }

    // API请求封装
    async apiRequest(endpoint, options = {}) {
        const url = this.config.apiBaseUrl + endpoint;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            this.showNotification(`API请求失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 获取应用状态
    getAppState() {
        return {
            config: this.config,
            modules: Object.keys(this.modules),
            timestamp: new Date().toISOString()
        };
    }

    // 重启应用
    restart() {
        this.showNotification('正在重启应用...', 'info');
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
}

// 创建全局应用实例
window.app = new App();

// 向后兼容的全局函数
function addAILog(message, type = 'info') {
    window.app.addSystemLog(message, type);
}

// 导出应用类供其他模块使用
window.App = App;

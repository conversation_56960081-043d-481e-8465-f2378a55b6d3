#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
创建数据库、表结构和初始数据
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from backend.app.core.config import settings
from backend.app.database.connection import db_manager
from backend.app.models.requirement import Base, Requirement, AIAnalysisResult, RequirementPriority, RequirementStatus
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def create_database():
    """创建数据库（如果不存在）"""
    try:
        logger.info("开始创建数据库...")

        # 连接MySQL服务器（不指定数据库）
        server_url = f"mysql+pymysql://{settings.MYSQL_USER}:{settings.MYSQL_PASSWORD}@{settings.MYSQL_HOST}:{settings.MYSQL_PORT}/?charset={settings.MYSQL_CHARSET}"

        from sqlalchemy import create_engine
        server_engine = create_engine(server_url)

        # 检查数据库是否存在
        with server_engine.connect() as conn:
            result = conn.execute(text(f"SHOW DATABASES LIKE '{settings.MYSQL_DATABASE}'"))
            database_exists = result.fetchone() is not None

            if not database_exists:
                # 创建数据库
                conn.execute(text(f"CREATE DATABASE `{settings.MYSQL_DATABASE}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                conn.commit()
                logger.info(f"数据库 '{settings.MYSQL_DATABASE}' 创建成功")
            else:
                logger.info(f"数据库 '{settings.MYSQL_DATABASE}' 已存在")

        server_engine.dispose()
        return True

    except Exception as e:
        logger.error(f"创建数据库失败: {e}")
        return False

async def create_tables():
    """创建数据库表"""
    try:
        logger.info("开始创建数据库表...")

        # 初始化数据库管理器
        if not db_manager.initialize():
            raise Exception("数据库管理器初始化失败")

        # 测试连接
        if not await db_manager.test_connection():
            raise Exception("数据库连接测试失败")

        # 创建所有表
        async with db_manager.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        logger.info("数据库表创建成功")
        return True

    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        return False

async def insert_sample_data():
    """插入示例数据"""
    try:
        logger.info("开始插入示例数据...")

        from backend.app.database.connection import get_async_db

        async with get_async_db() as session:
            # 检查是否已有数据
            from sqlalchemy import select, func
            count_stmt = select(func.count(Requirement.id))
            result = await session.execute(count_stmt)
            existing_count = result.scalar()

            if existing_count > 0:
                logger.info(f"数据库中已有 {existing_count} 条需求记录，跳过示例数据插入")
                return True
            
            # 创建示例需求
            sample_requirements = [
                {
                    "title": "用户行为数据分析平台",
                    "description": """
需要构建一个用户行为数据分析平台，用于分析用户在网站和APP上的行为轨迹。

主要功能包括：
1. 用户行为数据采集和清洗
2. 用户画像构建和标签体系
3. 行为路径分析和漏斗分析
4. 实时数据监控和告警
5. 可视化报表和仪表板

技术要求：
- 支持PB级数据处理
- 实时计算延迟小于1分钟
- 支持多维度数据分析
- 提供API接口供业务系统调用
                    """.strip(),
                    "priority": RequirementPriority.HIGH,
                    "status": RequirementStatus.PENDING,
                    "modules": ["数据采集", "数据处理", "用户画像", "实时计算", "可视化"],
                    "created_by": "产品经理"
                },
                {
                    "title": "商品推荐算法优化",
                    "description": """
优化现有的商品推荐算法，提升推荐准确率和用户满意度。

优化目标：
1. 提升点击率(CTR)至少20%
2. 提升转化率(CVR)至少15%
3. 降低推荐延迟至100ms以内
4. 支持个性化推荐和实时推荐

算法要求：
- 基于协同过滤和深度学习的混合推荐
- 支持冷启动问题解决
- 考虑用户兴趣变化和时效性
- 支持A/B测试和效果评估
                    """.strip(),
                    "priority": RequirementPriority.HIGH,
                    "status": RequirementStatus.DRAFT,
                    "modules": ["推荐算法", "机器学习", "实时计算", "A/B测试"],
                    "created_by": "算法工程师"
                },
                {
                    "title": "数据质量监控系统",
                    "description": """
建设数据质量监控系统，确保数据仓库中数据的准确性和完整性。

监控内容：
1. 数据完整性检查（空值、重复值）
2. 数据准确性验证（格式、范围、逻辑）
3. 数据时效性监控（延迟、更新频率）
4. 数据一致性校验（跨表、跨系统）

功能要求：
- 支持自定义监控规则
- 提供实时告警机制
- 生成数据质量报告
- 支持数据血缘追踪
                    """.strip(),
                    "priority": RequirementPriority.MEDIUM,
                    "status": RequirementStatus.PENDING,
                    "modules": ["数据质量", "监控告警", "数据治理", "血缘分析"],
                    "created_by": "数据工程师"
                },
                {
                    "title": "客户流失预测模型",
                    "description": """
构建客户流失预测模型，提前识别可能流失的客户并采取挽留措施。

模型要求：
1. 预测准确率达到85%以上
2. 支持批量预测和实时预测
3. 提供流失原因分析
4. 支持不同客户群体的差异化预测

数据需求：
- 客户基本信息和行为数据
- 交易记录和消费习惯
- 客服记录和满意度调研
- 竞品分析和市场环境
                    """.strip(),
                    "priority": RequirementPriority.MEDIUM,
                    "status": RequirementStatus.DRAFT,
                    "modules": ["机器学习", "客户分析", "预测模型", "数据挖掘"],
                    "created_by": "数据科学家"
                },
                {
                    "title": "实时数据同步平台",
                    "description": """
建设实时数据同步平台，实现多个业务系统之间的数据实时同步。

同步要求：
1. 支持MySQL、Oracle、MongoDB等多种数据源
2. 数据同步延迟小于5秒
3. 支持数据格式转换和清洗
4. 提供同步状态监控和异常处理

技术架构：
- 基于CDC（Change Data Capture）技术
- 支持断点续传和故障恢复
- 提供可视化配置界面
- 支持数据校验和一致性检查
                    """.strip(),
                    "priority": RequirementPriority.LOW,
                    "status": RequirementStatus.DRAFT,
                    "modules": ["数据同步", "CDC", "实时计算", "数据集成"],
                    "created_by": "系统架构师"
                }
            ]
            
            # 插入示例需求
            for req_data in sample_requirements:
                requirement = Requirement(**req_data)
                session.add(requirement)
            
            await session.commit()

            logger.info(f"成功插入 {len(sample_requirements)} 条示例需求")
            return True

    except Exception as e:
        logger.error(f"插入示例数据失败: {e}")
        return False

async def verify_installation():
    """验证安装结果"""
    try:
        logger.info("开始验证安装结果...")

        # 测试数据库连接
        if not await db_manager.test_connection():
            raise Exception("数据库连接失败")

        # 检查表是否存在
        async with db_manager.async_engine.begin() as conn:
            # 检查需求表
            result = await conn.execute(text("SHOW TABLES LIKE 'requirements'"))
            if not result.fetchone():
                raise Exception("需求表不存在")

            # 检查分析结果表
            result = await conn.execute(text("SHOW TABLES LIKE 'ai_analysis_results'"))
            if not result.fetchone():
                raise Exception("AI分析结果表不存在")

        # 检查数据
        from backend.app.database.connection import get_async_db
        from sqlalchemy import select, func

        async with get_async_db() as session:
            # 统计需求数量
            count_stmt = select(func.count(Requirement.id))
            result = await session.execute(count_stmt)
            req_count = result.scalar()

            logger.info(f"数据库验证成功")
            logger.info(f"统计信息:")
            logger.info(f"  - 需求数量: {req_count}")

        return True

    except Exception as e:
        logger.error(f"验证安装失败: {e}")
        return False

async def main():
    """主函数"""
    logger.info("开始初始化数据中台智能测试系统数据库")
    logger.info("=" * 60)

    try:
        # 1. 创建数据库
        if not await create_database():
            return False

        # 2. 创建表结构
        if not await create_tables():
            return False

        # 3. 插入示例数据
        if not await insert_sample_data():
            return False

        # 4. 验证安装
        if not await verify_installation():
            return False

        logger.info("=" * 60)
        logger.info("数据库初始化完成!")
        logger.info("系统信息:")
        logger.info(f"  - 数据库: {settings.MYSQL_HOST}:{settings.MYSQL_PORT}/{settings.MYSQL_DATABASE}")
        logger.info(f"  - 字符集: {settings.MYSQL_CHARSET}")
        logger.info(f"  - 连接池: {settings.DB_POOL_SIZE}")
        logger.info("")
        logger.info("下一步:")
        logger.info("  1. 启动后端服务: python backend/main.py")
        logger.info("  2. 访问前端界面: http://localhost:3000")
        logger.info("  3. 查看API文档: http://localhost:8000/docs")

        return True

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False

    finally:
        # 关闭数据库连接
        await db_manager.close()

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n用户取消操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n程序异常: {e}")
        sys.exit(1)

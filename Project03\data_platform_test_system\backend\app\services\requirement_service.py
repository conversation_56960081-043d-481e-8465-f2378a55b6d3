#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
需求管理服务模块
提供需求的CRUD操作、业务逻辑处理和AI分析集成
"""

from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func
from sqlalchemy.orm import selectinload
from datetime import datetime
import json
import logging

from ..models.requirement import Requirement, AIAnalysisResult, RequirementStatus, RequirementPriority
from ..database.connection import get_async_db
from .ai_service import ai_service
from ..core.config import settings

logger = logging.getLogger(__name__)

class RequirementService:
    """
    需求管理服务类
    负责需求的完整生命周期管理，包括CRUD操作、数据验证、AI分析集成等
    """
    
    def __init__(self):
        """初始化需求服务"""
        self.ai_service = ai_service
        logger.info("🔧 需求管理服务初始化完成")
    
    # ==================== 需求CRUD操作 ====================
    
    async def create_requirement(self, requirement_data: Dict[str, Any], session: AsyncSession) -> Requirement:
        """
        创建新需求
        
        Args:
            requirement_data: 需求数据字典
            session: 数据库会话
            
        Returns:
            Requirement: 创建的需求对象
        """
        try:
            logger.info(f"📝 创建新需求: {requirement_data.get('title', 'Unknown')}")
            
            # 数据预处理和验证
            processed_data = self._preprocess_requirement_data(requirement_data)
            
            # 创建需求对象
            requirement = Requirement(**processed_data)
            
            # 执行完整性检查
            self._check_requirement_completeness(requirement)
            
            # 保存到数据库
            session.add(requirement)
            await session.commit()
            await session.refresh(requirement)
            
            logger.info(f"✅ 需求创建成功: ID={requirement.id}, 标题={requirement.title}")
            return requirement
            
        except Exception as e:
            await session.rollback()
            logger.error(f"❌ 创建需求失败: {e}")
            raise
    
    async def get_requirement_by_id(self, requirement_id: int, session: AsyncSession) -> Optional[Requirement]:
        """
        根据ID获取需求详情
        
        Args:
            requirement_id: 需求ID
            session: 数据库会话
            
        Returns:
            Optional[Requirement]: 需求对象或None
        """
        try:
            stmt = select(Requirement).where(Requirement.id == requirement_id).options(
                selectinload(Requirement.analysis_results)
            )
            result = await session.execute(stmt)
            requirement = result.scalar_one_or_none()
            
            if requirement:
                logger.info(f"✅ 获取需求成功: ID={requirement_id}")
            else:
                logger.warning(f"⚠️ 需求不存在: ID={requirement_id}")
            
            return requirement
            
        except Exception as e:
            logger.error(f"❌ 获取需求失败: ID={requirement_id}, 错误={e}")
            raise
    
    async def get_requirements_list(
        self, 
        session: AsyncSession,
        page: int = 1,
        page_size: int = None,
        status: Optional[str] = None,
        priority: Optional[str] = None,
        keyword: Optional[str] = None
    ) -> Tuple[List[Requirement], int]:
        """
        获取需求列表（支持分页和筛选）
        
        Args:
            session: 数据库会话
            page: 页码（从1开始）
            page_size: 每页大小
            status: 状态筛选
            priority: 优先级筛选
            keyword: 关键词搜索
            
        Returns:
            Tuple[List[Requirement], int]: (需求列表, 总数)
        """
        try:
            # 设置默认分页大小
            if page_size is None:
                page_size = settings.DEFAULT_PAGE_SIZE
            
            # 限制最大分页大小
            page_size = min(page_size, settings.MAX_PAGE_SIZE)
            
            logger.info(f"📋 获取需求列表: 页码={page}, 大小={page_size}")
            
            # 构建查询条件
            conditions = []
            
            if status:
                try:
                    status_enum = RequirementStatus(status)
                    conditions.append(Requirement.status == status_enum)
                except ValueError:
                    logger.warning(f"无效的状态值: {status}")
            
            if priority:
                try:
                    priority_enum = RequirementPriority(priority)
                    conditions.append(Requirement.priority == priority_enum)
                except ValueError:
                    logger.warning(f"无效的优先级值: {priority}")
            
            if keyword:
                keyword_condition = or_(
                    Requirement.title.contains(keyword),
                    Requirement.description.contains(keyword)
                )
                conditions.append(keyword_condition)
            
            # 构建基础查询
            base_stmt = select(Requirement)
            if conditions:
                base_stmt = base_stmt.where(and_(*conditions))
            
            # 获取总数
            count_stmt = select(func.count(Requirement.id))
            if conditions:
                count_stmt = count_stmt.where(and_(*conditions))
            
            count_result = await session.execute(count_stmt)
            total_count = count_result.scalar()
            
            # 分页查询
            stmt = base_stmt.order_by(Requirement.created_time.desc()).offset(
                (page - 1) * page_size
            ).limit(page_size).options(
                selectinload(Requirement.analysis_results)
            )
            
            result = await session.execute(stmt)
            requirements = result.scalars().all()
            
            logger.info(f"✅ 获取需求列表成功: 返回{len(requirements)}条记录，总数={total_count}")
            return list(requirements), total_count
            
        except Exception as e:
            logger.error(f"❌ 获取需求列表失败: {e}")
            raise
    
    async def update_requirement(
        self, 
        requirement_id: int, 
        update_data: Dict[str, Any], 
        session: AsyncSession
    ) -> Optional[Requirement]:
        """
        更新需求信息
        
        Args:
            requirement_id: 需求ID
            update_data: 更新数据
            session: 数据库会话
            
        Returns:
            Optional[Requirement]: 更新后的需求对象
        """
        try:
            logger.info(f"📝 更新需求: ID={requirement_id}")
            
            # 获取现有需求
            requirement = await self.get_requirement_by_id(requirement_id, session)
            if not requirement:
                logger.warning(f"需求不存在: ID={requirement_id}")
                return None
            
            # 数据预处理
            processed_data = self._preprocess_requirement_data(update_data)
            
            # 更新字段
            for key, value in processed_data.items():
                if hasattr(requirement, key):
                    setattr(requirement, key, value)
            
            # 更新时间
            requirement.updated_time = datetime.utcnow()
            
            # 重新检查完整性
            self._check_requirement_completeness(requirement)
            
            # 保存更改
            await session.commit()
            await session.refresh(requirement)
            
            logger.info(f"✅ 需求更新成功: ID={requirement_id}")
            return requirement
            
        except Exception as e:
            await session.rollback()
            logger.error(f"❌ 更新需求失败: ID={requirement_id}, 错误={e}")
            raise
    
    async def delete_requirement(self, requirement_id: int, session: AsyncSession) -> bool:
        """
        删除需求
        
        Args:
            requirement_id: 需求ID
            session: 数据库会话
            
        Returns:
            bool: 删除是否成功
        """
        try:
            logger.info(f"🗑️ 删除需求: ID={requirement_id}")
            
            # 检查需求是否存在
            requirement = await self.get_requirement_by_id(requirement_id, session)
            if not requirement:
                logger.warning(f"需求不存在: ID={requirement_id}")
                return False
            
            # 删除需求（级联删除相关分析记录）
            await session.delete(requirement)
            await session.commit()
            
            logger.info(f"✅ 需求删除成功: ID={requirement_id}")
            return True
            
        except Exception as e:
            await session.rollback()
            logger.error(f"❌ 删除需求失败: ID={requirement_id}, 错误={e}")
            raise
    
    # ==================== AI分析相关操作 ====================
    
    async def analyze_requirements(
        self, 
        requirement_ids: List[int], 
        session: AsyncSession
    ) -> List[Dict[str, Any]]:
        """
        对选中的需求进行AI分析
        
        Args:
            requirement_ids: 需求ID列表
            session: 数据库会话
            
        Returns:
            List[Dict[str, Any]]: 分析结果列表
        """
        try:
            logger.info(f"🤖 开始AI分析: {len(requirement_ids)}个需求")
            
            # 限制批量分析数量
            if len(requirement_ids) > settings.ANALYSIS_BATCH_SIZE:
                raise ValueError(f"批量分析数量不能超过{settings.ANALYSIS_BATCH_SIZE}个")
            
            # 获取需求数据
            requirements_data = []
            for req_id in requirement_ids:
                requirement = await self.get_requirement_by_id(req_id, session)
                if requirement:
                    requirements_data.append(requirement.to_dict())
                else:
                    logger.warning(f"需求不存在，跳过分析: ID={req_id}")
            
            if not requirements_data:
                logger.warning("没有有效的需求数据进行分析")
                return []
            
            # 更新需求状态为分析中
            for req_id in requirement_ids:
                await self._update_requirement_status(req_id, RequirementStatus.ANALYZING, session)
            
            # 执行AI分析
            analysis_results = await self.ai_service.analyze_batch_requirements(requirements_data)
            
            # 保存分析结果到数据库
            saved_results = []
            for analysis_result in analysis_results:
                try:
                    saved_result = await self._save_analysis_result(analysis_result, session)
                    if saved_result:
                        saved_results.append(saved_result.to_dict())
                        
                        # 更新需求状态为已分析
                        await self._update_requirement_status(
                            analysis_result['req_id'], 
                            RequirementStatus.ANALYZED, 
                            session
                        )
                        
                except Exception as e:
                    logger.error(f"保存分析结果失败: {e}")
                    # 更新需求状态为待分析（回滚状态）
                    await self._update_requirement_status(
                        analysis_result['req_id'], 
                        RequirementStatus.PENDING, 
                        session
                    )
            
            logger.info(f"✅ AI分析完成: 成功{len(saved_results)}/{len(requirement_ids)}个")
            return saved_results
            
        except Exception as e:
            logger.error(f"❌ AI分析失败: {e}")
            # 回滚所有需求状态
            for req_id in requirement_ids:
                try:
                    await self._update_requirement_status(req_id, RequirementStatus.PENDING, session)
                except:
                    pass
            raise

    async def get_analysis_results(
        self,
        session: AsyncSession,
        page: int = 1,
        page_size: int = None,
        requirement_id: Optional[int] = None
    ) -> Tuple[List[AIAnalysisResult], int]:
        """
        获取AI分析结果列表

        Args:
            session: 数据库会话
            page: 页码
            page_size: 每页大小
            requirement_id: 需求ID筛选

        Returns:
            Tuple[List[AIAnalysisResult], int]: (分析结果列表, 总数)
        """
        try:
            # 设置默认分页大小
            if page_size is None:
                page_size = settings.DEFAULT_PAGE_SIZE

            page_size = min(page_size, settings.MAX_PAGE_SIZE)

            logger.info(f"📊 获取分析结果列表: 页码={page}, 大小={page_size}")

            # 构建查询条件
            conditions = []
            if requirement_id:
                conditions.append(AIAnalysisResult.req_id == requirement_id)

            # 构建基础查询
            base_stmt = select(AIAnalysisResult).options(
                selectinload(AIAnalysisResult.requirement)
            )
            if conditions:
                base_stmt = base_stmt.where(and_(*conditions))

            # 获取总数
            count_stmt = select(func.count(AIAnalysisResult.id))
            if conditions:
                count_stmt = count_stmt.where(and_(*conditions))

            count_result = await session.execute(count_stmt)
            total_count = count_result.scalar()

            # 分页查询
            stmt = base_stmt.order_by(AIAnalysisResult.created_time.desc()).offset(
                (page - 1) * page_size
            ).limit(page_size)

            result = await session.execute(stmt)
            analyses = result.scalars().all()

            logger.info(f"✅ 获取分析结果列表成功: 返回{len(analyses)}条记录，总数={total_count}")
            return list(analyses), total_count

        except Exception as e:
            logger.error(f"❌ 获取分析结果列表失败: {e}")
            raise

    async def update_manual_adjustment(
        self,
        analysis_id: int,
        manual_adjustment: str,
        adjusted_by: str,
        session: AsyncSession
    ) -> Optional[AIAnalysisResult]:
        """
        更新分析结果的人工调整

        Args:
            analysis_id: 分析结果ID
            manual_adjustment: 人工调整内容
            adjusted_by: 调整者
            session: 数据库会话

        Returns:
            Optional[AIAnalysisResult]: 更新后的分析结果
        """
        try:
            logger.info(f"✏️ 更新人工调整: 分析ID={analysis_id}")

            # 获取分析记录
            stmt = select(AIAnalysisResult).where(AIAnalysisResult.id == analysis_id)
            result = await session.execute(stmt)
            analysis = result.scalar_one_or_none()

            if not analysis:
                logger.warning(f"分析结果不存在: ID={analysis_id}")
                return None

            # 更新人工调整信息
            analysis.manual_adjust = manual_adjustment
            analysis.is_manually_adjusted = True
            analysis.adjusted_by = adjusted_by
            analysis.adjusted_time = datetime.utcnow()
            analysis.update_time = datetime.utcnow()

            await session.commit()
            await session.refresh(analysis)

            logger.info(f"✅ 人工调整更新成功: 分析ID={analysis_id}")
            return analysis

        except Exception as e:
            await session.rollback()
            logger.error(f"❌ 更新人工调整失败: 分析ID={analysis_id}, 错误={e}")
            raise

    # ==================== 私有辅助方法 ====================

    def _preprocess_requirement_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        预处理需求数据

        Args:
            data: 原始数据

        Returns:
            Dict[str, Any]: 处理后的数据
        """
        processed = {}

        # 基本字段处理
        if "title" in data:
            processed["title"] = str(data["title"]).strip()

        if "description" in data:
            processed["description"] = str(data["description"]).strip() if data["description"] else None

        if "created_by" in data:
            processed["created_by"] = str(data["created_by"]).strip() if data["created_by"] else None

        # 枚举字段处理
        if "priority" in data and data["priority"]:
            try:
                if isinstance(data["priority"], str):
                    processed["priority"] = RequirementPriority(data["priority"])
                else:
                    processed["priority"] = data["priority"]
            except ValueError:
                logger.warning(f"无效的优先级值: {data['priority']}")

        if "status" in data and data["status"]:
            try:
                if isinstance(data["status"], str):
                    processed["status"] = RequirementStatus(data["status"])
                else:
                    processed["status"] = data["status"]
            except ValueError:
                logger.warning(f"无效的状态值: {data['status']}")

        # JSON字段处理
        if "modules" in data:
            if isinstance(data["modules"], (list, dict)):
                processed["modules"] = data["modules"]
            elif isinstance(data["modules"], str):
                try:
                    processed["modules"] = json.loads(data["modules"])
                except json.JSONDecodeError:
                    processed["modules"] = [data["modules"]]

        if "attachments" in data:
            if isinstance(data["attachments"], (list, dict)):
                processed["attachments"] = data["attachments"]
            elif isinstance(data["attachments"], str):
                try:
                    processed["attachments"] = json.loads(data["attachments"])
                except json.JSONDecodeError:
                    processed["attachments"] = None

        return processed

    def _check_requirement_completeness(self, requirement: Requirement):
        """
        检查需求完整性

        Args:
            requirement: 需求对象
        """
        missing_fields = []

        # 检查必要字段
        if not requirement.title or len(requirement.title.strip()) == 0:
            missing_fields.append("title")

        if not requirement.description or len(requirement.description.strip()) < 10:
            missing_fields.append("description")

        if not requirement.priority:
            missing_fields.append("priority")

        if not requirement.modules:
            missing_fields.append("modules")

        # 更新完整性状态
        requirement.is_complete = len(missing_fields) == 0
        requirement.missing_fields = missing_fields if missing_fields else None

        logger.debug(f"需求完整性检查: ID={requirement.id}, 完整={requirement.is_complete}, 缺失={missing_fields}")

    async def _update_requirement_status(
        self,
        requirement_id: int,
        status: RequirementStatus,
        session: AsyncSession
    ):
        """
        更新需求状态

        Args:
            requirement_id: 需求ID
            status: 新状态
            session: 数据库会话
        """
        try:
            stmt = update(Requirement).where(
                Requirement.id == requirement_id
            ).values(
                status=status,
                updated_time=datetime.utcnow()
            )
            await session.execute(stmt)
            await session.commit()

            logger.debug(f"需求状态更新: ID={requirement_id}, 状态={status.value}")

        except Exception as e:
            logger.error(f"更新需求状态失败: ID={requirement_id}, 错误={e}")
            raise

    async def _save_analysis_result(
        self,
        analysis_data: Dict[str, Any],
        session: AsyncSession
    ) -> Optional[AIAnalysisResult]:
        """
        保存AI分析结果到数据库

        Args:
            analysis_data: 分析结果数据
            session: 数据库会话

        Returns:
            Optional[AIAnalysisResult]: 保存的分析结果对象
        """
        try:
            # 创建分析结果对象
            analysis_result = AIAnalysisResult(
                req_id=analysis_data.get('req_id'),
                analysis_result=analysis_data.get('analysis_result', ''),
                structured_result=analysis_data.get('structured_result', {}),
                key_points=analysis_data.get('key_points', []),
                risk_assessment=analysis_data.get('risk_assessment', {}),
                suggestions=analysis_data.get('suggestions', []),
                ai_model=analysis_data.get('ai_model', ''),
                ai_version=analysis_data.get('ai_version', ''),
                analysis_config=analysis_data.get('analysis_config', {}),
                analysis_status=analysis_data.get('analysis_status', 'completed'),
                error_message=analysis_data.get('error_message'),
                processing_time=analysis_data.get('processing_time', 0)
            )

            session.add(analysis_result)
            await session.commit()
            await session.refresh(analysis_result)

            logger.info(f"✅ 分析结果保存成功: 需求ID={analysis_result.req_id}")
            return analysis_result

        except Exception as e:
            await session.rollback()
            logger.error(f"❌ 保存分析结果失败: {e}")
            return None

# ==================== 全局服务实例 ====================
requirement_service = RequirementService()

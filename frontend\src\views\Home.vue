<!--
  首页组件 - Gemini风格设计
  展示平台介绍和快速入口
-->

<template>
  <div class="home-container">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            <span class="gradient-text">AI驱动的智能测试</span>
            <br />
            让测试用例生成更简单
          </h1>
          <p class="hero-description">
            基于多模态大模型和AutoGen技术，自动分析您的需求文档、思维导图和界面截图，
            智能生成全面专业的测试用例，提升测试效率和质量。
          </p>
          <div class="hero-actions">
            <el-button 
              type="primary" 
              size="large" 
              @click="startGenerate"
              class="cta-button"
            >
              <el-icon><Magic /></el-icon>
              开始生成测试用例
            </el-button>
            <el-button 
              size="large" 
              @click="viewDemo"
              class="demo-button"
            >
              <el-icon><VideoPlay /></el-icon>
              观看演示
            </el-button>
          </div>
        </div>
        <div class="hero-visual">
          <div class="floating-cards">
            <div class="card card-1">
              <el-icon size="24" color="#4285f4"><Document /></el-icon>
              <span>需求分析</span>
            </div>
            <div class="card card-2">
              <el-icon size="24" color="#34a853"><Picture /></el-icon>
              <span>图像识别</span>
            </div>
            <div class="card card-3">
              <el-icon size="24" color="#fbbc04"><Magic /></el-icon>
              <span>AI生成</span>
            </div>
            <div class="card card-4">
              <el-icon size="24" color="#ea4335"><Download /></el-icon>
              <span>导出Excel</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 特性介绍 -->
    <section class="features-section">
      <div class="section-header">
        <h2 class="section-title">核心特性</h2>
        <p class="section-description">
          强大的AI能力，让测试用例生成变得智能化和自动化
        </p>
      </div>
      
      <div class="features-grid">
        <div class="feature-card" v-for="feature in features" :key="feature.id">
          <div class="feature-icon">
            <el-icon :size="32" :color="feature.color">
              <component :is="feature.icon" />
            </el-icon>
          </div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
        </div>
      </div>
    </section>

    <!-- 工作流程 -->
    <section class="workflow-section">
      <div class="section-header">
        <h2 class="section-title">简单四步，完成测试用例生成</h2>
        <p class="section-description">
          直观的操作流程，让您快速上手使用
        </p>
      </div>
      
      <div class="workflow-steps">
        <div class="step" v-for="(step, index) in workflowSteps" :key="step.id">
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-content">
            <div class="step-icon">
              <el-icon :size="24" :color="step.color">
                <component :is="step.icon" />
              </el-icon>
            </div>
            <h4 class="step-title">{{ step.title }}</h4>
            <p class="step-description">{{ step.description }}</p>
          </div>
          <div v-if="index < workflowSteps.length - 1" class="step-arrow">
            <el-icon size="20" color="#dadce0"><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据 */
    <section class="stats-section">
      <div class="stats-grid">
        <div class="stat-item" v-for="stat in stats" :key="stat.id">
          <div class="stat-number">{{ stat.number }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Magic, VideoPlay, Document, Picture, Download, 
  Upload, Setting, DataAnalysis, ArrowRight,
  BrainFilled, Lightning, Shield, Cpu
} from '@element-plus/icons-vue'

const router = useRouter()

// 特性数据
const features = ref([
  {
    id: 1,
    icon: BrainFilled,
    color: '#4285f4',
    title: '多模态AI分析',
    description: '支持文本、图像等多种输入方式，智能理解您的需求和设计意图'
  },
  {
    id: 2,
    icon: Lightning,
    color: '#34a853',
    title: '流式实时生成',
    description: '实时显示生成进度，让您随时了解测试用例的生成状态'
  },
  {
    id: 3,
    icon: Shield,
    color: '#fbbc04',
    title: '全面质量保障',
    description: '覆盖功能、性能、安全等多个测试维度，确保测试用例的完整性'
  },
  {
    id: 4,
    icon: Cpu,
    color: '#ea4335',
    title: '一键导出Excel',
    description: '支持多种格式导出，方便团队协作和测试执行'
  }
])

// 工作流程步骤
const workflowSteps = ref([
  {
    id: 1,
    icon: Upload,
    color: '#4285f4',
    title: '上传文件',
    description: '上传思维导图、流程图或界面截图'
  },
  {
    id: 2,
    icon: Setting,
    color: '#34a853',
    title: '配置需求',
    description: '填写项目信息和测试需求描述'
  },
  {
    id: 3,
    icon: Magic,
    color: '#fbbc04',
    title: 'AI生成',
    description: '智能分析并生成专业测试用例'
  },
  {
    id: 4,
    icon: Download,
    color: '#ea4335',
    title: '导出使用',
    description: '下载Excel文件，开始执行测试'
  }
])

// 统计数据
const stats = ref([
  { id: 1, number: '10,000+', label: '生成的测试用例' },
  { id: 2, number: '500+', label: '服务的项目' },
  { id: 3, number: '95%', label: '用户满意度' },
  { id: 4, number: '80%', label: '效率提升' }
])

/**
 * 开始生成测试用例
 */
const startGenerate = () => {
  router.push('/generate')
}

/**
 * 查看演示
 */
const viewDemo = () => {
  // 这里可以打开演示视频或引导页面
  console.log('查看演示')
}
</script>

<style lang="scss" scoped>
.home-container {
  min-height: calc(100vh - 120px);
}

.hero-section {
  padding: 80px 0;
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.1) 0%, rgba(52, 168, 83, 0.1) 100%);
  border-radius: 24px;
  margin-bottom: 80px;

  .hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .hero-text {
    .hero-title {
      font-size: 48px;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 24px;
      color: #202124;
    }

    .hero-description {
      font-size: 18px;
      line-height: 1.6;
      color: #5f6368;
      margin-bottom: 40px;
    }

    .hero-actions {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .cta-button {
        background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
        border: none;
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(66, 133, 244, 0.4);
        }
      }

      .demo-button {
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 12px;
        border: 2px solid #dadce0;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;

        &:hover {
          border-color: #4285f4;
          color: #4285f4;
          transform: translateY(-2px);
        }
      }
    }
  }

  .hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    .floating-cards {
      position: relative;
      width: 300px;
      height: 300px;

      .card {
        position: absolute;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        animation: float 3s ease-in-out infinite;

        &.card-1 {
          top: 20px;
          left: 20px;
          animation-delay: 0s;
        }

        &.card-2 {
          top: 20px;
          right: 20px;
          animation-delay: 0.5s;
        }

        &.card-3 {
          bottom: 20px;
          left: 20px;
          animation-delay: 1s;
        }

        &.card-4 {
          bottom: 20px;
          right: 20px;
          animation-delay: 1.5s;
        }
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.features-section,
.workflow-section {
  margin-bottom: 80px;

  .section-header {
    text-align: center;
    margin-bottom: 60px;

    .section-title {
      font-size: 36px;
      font-weight: 700;
      color: #202124;
      margin-bottom: 16px;
    }

    .section-description {
      font-size: 18px;
      color: #5f6368;
      max-width: 600px;
      margin: 0 auto;
    }
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;

  .feature-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 32px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
    }

    .feature-icon {
      width: 64px;
      height: 64px;
      background: rgba(66, 133, 244, 0.1);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24px;
    }

    .feature-title {
      font-size: 20px;
      font-weight: 600;
      color: #202124;
      margin-bottom: 12px;
    }

    .feature-description {
      color: #5f6368;
      line-height: 1.6;
    }
  }
}

.workflow-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  flex-wrap: wrap;

  .step {
    display: flex;
    align-items: center;
    gap: 20px;

    .step-number {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 18px;
    }

    .step-content {
      text-align: center;
      max-width: 200px;

      .step-icon {
        width: 48px;
        height: 48px;
        background: rgba(66, 133, 244, 0.1);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12px;
      }

      .step-title {
        font-size: 16px;
        font-weight: 600;
        color: #202124;
        margin-bottom: 8px;
      }

      .step-description {
        font-size: 14px;
        color: #5f6368;
        line-height: 1.5;
      }
    }

    .step-arrow {
      margin: 0 20px;
    }
  }
}

.stats-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 60px 40px;
  margin-bottom: 40px;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;

    .stat-item {
      text-align: center;

      .stat-number {
        font-size: 48px;
        font-weight: 700;
        background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 16px;
        color: #5f6368;
        font-weight: 500;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-section {
    .hero-content {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }

    .hero-text {
      .hero-title {
        font-size: 36px;
      }

      .hero-actions {
        justify-content: center;
      }
    }
  }

  .workflow-steps {
    flex-direction: column;

    .step {
      flex-direction: column;
      text-align: center;

      .step-arrow {
        transform: rotate(90deg);
        margin: 20px 0;
      }
    }
  }
}
</style>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化前端测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .nav {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .nav button {
            padding: 10px 20px;
            border: none;
            background: #007bff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }
        .nav button:hover {
            background: #0056b3;
        }
        .nav button.active {
            background: #28a745;
        }
        .page {
            display: none;
        }
        .page.active {
            display: block;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .table th {
            background: #f8f9fa;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>数据中台智能测试系统</h1>
    
    <div class="nav">
        <button onclick="showPage('home')" id="homeBtn" class="active">首页</button>
        <button onclick="showPage('requirements')" id="requirementsBtn">需求列表</button>
        <button onclick="showPage('analysis')" id="analysisBtn">分析列表</button>
    </div>
    
    <div id="home" class="page active">
        <div class="container">
            <h2>系统首页</h2>
            <p>欢迎使用数据中台智能测试系统</p>
            <div id="systemStatus"></div>
        </div>
    </div>
    
    <div id="requirements" class="page">
        <div class="container">
            <h2>需求列表</h2>
            <button onclick="loadRequirements()">刷新数据</button>
            <div id="requirementsContent">
                <div class="loading">点击刷新数据按钮加载需求列表</div>
            </div>
        </div>
    </div>
    
    <div id="analysis" class="page">
        <div class="container">
            <h2>分析列表</h2>
            <button onclick="loadAnalysis()">刷新数据</button>
            <div id="analysisContent">
                <div class="loading">点击刷新数据按钮加载分析列表</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        const API_PREFIX = '/api/v1';
        
        // 页面切换
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 隐藏所有按钮的active状态
            document.querySelectorAll('.nav button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            document.getElementById(pageId + 'Btn').classList.add('active');
            
            // 自动加载数据
            if (pageId === 'requirements') {
                loadRequirements();
            } else if (pageId === 'analysis') {
                loadAnalysis();
            } else if (pageId === 'home') {
                loadSystemStatus();
            }
        }
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            document.body.insertBefore(messageDiv, document.body.firstChild);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }
        
        // 加载系统状态
        async function loadSystemStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                const statusDiv = document.getElementById('systemStatus');
                statusDiv.innerHTML = `
                    <h3>系统状态</h3>
                    <p>状态: ${data.status}</p>
                    <p>数据库: ${data.services?.database?.connected ? '正常' : '异常'}</p>
                    <p>AI服务: ${data.services?.ai_service?.status || '未知'}</p>
                `;
            } catch (error) {
                console.error('加载系统状态失败:', error);
                showMessage('加载系统状态失败', 'error');
            }
        }
        
        // 加载需求列表
        async function loadRequirements() {
            const container = document.getElementById('requirementsContent');
            container.innerHTML = '<div class="loading">正在加载需求列表...</div>';
            
            try {
                console.log('开始加载需求列表...');
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/?page=1&page_size=20`);
                console.log('API响应状态:', response.status);
                
                const data = await response.json();
                console.log('API响应数据:', data);
                
                if (response.ok) {
                    if (data.requirements && data.requirements.length > 0) {
                        let html = `
                            <p>共 ${data.total} 条需求</p>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>标题</th>
                                        <th>优先级</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        data.requirements.forEach(req => {
                            html += `
                                <tr>
                                    <td>${req.id}</td>
                                    <td>${req.title}</td>
                                    <td>${req.priority}</td>
                                    <td>${req.status}</td>
                                    <td>${new Date(req.created_time).toLocaleString()}</td>
                                </tr>
                            `;
                        });
                        
                        html += '</tbody></table>';
                        container.innerHTML = html;
                        showMessage('需求列表加载成功', 'success');
                    } else {
                        container.innerHTML = '<p>暂无需求数据</p>';
                        showMessage('暂无需求数据', 'info');
                    }
                } else {
                    throw new Error(`API错误: ${response.status}`);
                }
            } catch (error) {
                console.error('加载需求列表失败:', error);
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
                showMessage('加载需求列表失败', 'error');
            }
        }
        
        // 加载分析列表
        async function loadAnalysis() {
            const container = document.getElementById('analysisContent');
            container.innerHTML = '<div class="loading">正在加载分析列表...</div>';
            
            try {
                console.log('开始加载分析列表...');
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/analyses/?page=1&page_size=20`);
                console.log('API响应状态:', response.status);
                
                const data = await response.json();
                console.log('API响应数据:', data);
                
                if (response.ok) {
                    if (data.analyses && data.analyses.length > 0) {
                        let html = `
                            <p>共 ${data.total} 条分析结果</p>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>需求ID</th>
                                        <th>AI模型</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        data.analyses.forEach(analysis => {
                            html += `
                                <tr>
                                    <td>${analysis.id}</td>
                                    <td>${analysis.req_id}</td>
                                    <td>${analysis.ai_model}</td>
                                    <td>${analysis.analysis_status}</td>
                                    <td>${new Date(analysis.created_time).toLocaleString()}</td>
                                </tr>
                            `;
                        });
                        
                        html += '</tbody></table>';
                        container.innerHTML = html;
                        showMessage('分析列表加载成功', 'success');
                    } else {
                        container.innerHTML = '<p>暂无分析数据</p>';
                        showMessage('暂无分析数据', 'info');
                    }
                } else {
                    throw new Error(`API错误: ${response.status}`);
                }
            } catch (error) {
                console.error('加载分析列表失败:', error);
                container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
                showMessage('加载分析列表失败', 'error');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            console.log('页面加载完成');
            loadSystemStatus();
        });
    </script>
</body>
</html>

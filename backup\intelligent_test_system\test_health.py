#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查测试脚本
"""

import requests
import json
import time

def test_health_api():
    """测试健康检查API"""
    print("🏥 测试系统健康检查API...")
    
    try:
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 测试健康检查
        print("📡 发送健康检查请求...")
        response = requests.get('http://localhost:8001/api/health', timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 健康检查API响应成功")
            print(f"📋 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查必要字段
            required_fields = ['status', 'version', 'ai_framework', 'features', 'timestamp']
            missing_fields = []
            
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"⚠️ 缺少字段: {missing_fields}")
            else:
                print("✅ 所有必要字段都存在")
                
            # 检查features字段
            if 'features' in data and isinstance(data['features'], list):
                print(f"✅ features字段正常，包含 {len(data['features'])} 个功能")
                for i, feature in enumerate(data['features']):
                    print(f"  {i+1}. {feature}")
            else:
                print("❌ features字段异常")
                
            return True
        else:
            print(f"❌ 健康检查失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请检查服务器是否启动")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_frontend_connection():
    """测试前端连接"""
    print("\n🌐 测试前端页面...")
    
    try:
        response = requests.get('http://localhost:8001/', timeout=5)
        
        if response.status_code == 200:
            print("✅ 前端页面可访问")
            return True
        else:
            print(f"❌ 前端页面访问失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 前端测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 系统状态监控修复验证")
    print("=" * 50)
    
    # 测试健康检查API
    health_ok = test_health_api()
    
    # 测试前端连接
    frontend_ok = test_frontend_connection()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  🏥 健康检查API: {'✅ 正常' if health_ok else '❌ 异常'}")
    print(f"  🌐 前端页面: {'✅ 正常' if frontend_ok else '❌ 异常'}")
    
    if health_ok and frontend_ok:
        print("\n🎉 系统状态监控修复成功！")
        print("💡 现在可以访问 http://localhost:8001 并查看系统状态页面")
    else:
        print("\n⚠️ 部分功能仍有问题，需要进一步检查")

if __name__ == "__main__":
    main()

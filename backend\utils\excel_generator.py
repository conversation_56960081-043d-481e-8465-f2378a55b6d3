# -*- coding: utf-8 -*-
"""
Excel文件生成工具
用于将测试用例导出为Excel格式
"""

import os
from typing import List
from datetime import datetime
from pathlib import Path

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

try:
    from ..models.schemas import TestCase
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from models.schemas import TestCase


class ExcelGenerator:
    """Excel文件生成器"""
    
    def __init__(self):
        """初始化Excel生成器"""
        self.output_dir = Path("downloads")
        self.output_dir.mkdir(exist_ok=True)
        print("Excel生成器初始化完成")
    
    def generate_test_cases_excel(
        self, 
        test_cases: List[TestCase], 
        project_name: str = "数据中台测试项目"
    ) -> str:
        """
        生成测试用例Excel文件
        
        Args:
            test_cases: 测试用例列表
            project_name: 项目名称
            
        Returns:
            生成的Excel文件路径
        """
        try:
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "测试用例"
            
            # 设置表头
            headers = [
                "用例ID", "用例标题", "用例描述", "前置条件", 
                "测试步骤", "预期结果", "优先级", "测试类型", "所属模块"
            ]
            
            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
                cell.alignment = Alignment(horizontal="center", vertical="center")
                cell.border = self._get_border()
            
            # 写入数据
            for row, test_case in enumerate(test_cases, 2):
                data = [
                    test_case.case_id,
                    test_case.title,
                    test_case.description,
                    test_case.preconditions,
                    "\n".join(test_case.test_steps),
                    test_case.expected_result,
                    test_case.priority,
                    test_case.test_type,
                    test_case.module
                ]
                
                for col, value in enumerate(data, 1):
                    cell = ws.cell(row=row, column=col, value=value)
                    cell.alignment = Alignment(horizontal="left", vertical="top", wrap_text=True)
                    cell.border = self._get_border()
                    
                    # 根据优先级设置颜色
                    if col == 7:  # 优先级列
                        if value == "高":
                            cell.fill = PatternFill(start_color="FFE6E6", end_color="FFE6E6", fill_type="solid")
                        elif value == "中":
                            cell.fill = PatternFill(start_color="FFF2E6", end_color="FFF2E6", fill_type="solid")
                        else:
                            cell.fill = PatternFill(start_color="E6F7E6", end_color="E6F7E6", fill_type="solid")
            
            # 调整列宽
            column_widths = [15, 30, 40, 25, 50, 30, 10, 15, 15]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[chr(64 + col)].width = width
            
            # 设置行高
            for row in range(2, len(test_cases) + 2):
                ws.row_dimensions[row].height = 60
            
            # 添加项目信息工作表
            self._add_project_info_sheet(wb, project_name, len(test_cases))
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{project_name}_测试用例_{timestamp}.xlsx"
            filepath = self.output_dir / filename
            
            # 保存文件
            wb.save(filepath)
            print(f"Excel文件生成成功: {filepath}")
            
            return str(filepath)
            
        except Exception as e:
            print(f"生成Excel文件失败: {str(e)}")
            raise Exception(f"生成Excel文件失败: {str(e)}")
    
    def _get_border(self) -> Border:
        """获取边框样式"""
        thin_border = Side(border_style="thin", color="000000")
        return Border(
            left=thin_border,
            right=thin_border,
            top=thin_border,
            bottom=thin_border
        )
    
    def _add_project_info_sheet(self, wb: Workbook, project_name: str, total_cases: int):
        """添加项目信息工作表"""
        ws = wb.create_sheet("项目信息")
        
        # 项目基本信息
        info_data = [
            ["项目名称", project_name],
            ["生成时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            ["测试用例总数", total_cases],
            ["生成工具", "数据中台智能测试平台"],
            ["版本", "v1.0.0"]
        ]
        
        for row, (key, value) in enumerate(info_data, 1):
            ws.cell(row=row, column=1, value=key).font = Font(bold=True)
            ws.cell(row=row, column=2, value=value)
        
        # 调整列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 30
        
        # 添加说明信息
        ws.cell(row=len(info_data) + 3, column=1, value="使用说明:").font = Font(bold=True)
        instructions = [
            "1. 本文件由数据中台智能测试平台自动生成",
            "2. 测试用例已按照标准格式整理",
            "3. 优先级用颜色区分：红色(高)、橙色(中)、绿色(低)",
            "4. 测试步骤支持多行显示",
            "5. 如有问题请联系系统管理员"
        ]
        
        for i, instruction in enumerate(instructions):
            ws.cell(row=len(info_data) + 4 + i, column=1, value=instruction)
    
    def generate_summary_report(self, test_cases: List[TestCase], project_name: str) -> str:
        """
        生成测试用例汇总报告
        
        Args:
            test_cases: 测试用例列表
            project_name: 项目名称
            
        Returns:
            报告文件路径
        """
        try:
            # 统计数据
            total_cases = len(test_cases)
            priority_stats = {}
            type_stats = {}
            module_stats = {}
            
            for case in test_cases:
                # 优先级统计
                priority_stats[case.priority] = priority_stats.get(case.priority, 0) + 1
                # 类型统计
                type_stats[case.test_type] = type_stats.get(case.test_type, 0) + 1
                # 模块统计
                module_stats[case.module] = module_stats.get(case.module, 0) + 1
            
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "测试汇总报告"
            
            # 写入汇总信息
            ws.cell(row=1, column=1, value="数据中台测试用例汇总报告").font = Font(size=16, bold=True)
            ws.cell(row=2, column=1, value=f"项目名称: {project_name}")
            ws.cell(row=3, column=1, value=f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            ws.cell(row=4, column=1, value=f"测试用例总数: {total_cases}")
            
            # 优先级分布
            row = 6
            ws.cell(row=row, column=1, value="优先级分布:").font = Font(bold=True)
            for priority, count in priority_stats.items():
                row += 1
                ws.cell(row=row, column=1, value=f"  {priority}: {count}个")
            
            # 测试类型分布
            row += 2
            ws.cell(row=row, column=1, value="测试类型分布:").font = Font(bold=True)
            for test_type, count in type_stats.items():
                row += 1
                ws.cell(row=row, column=1, value=f"  {test_type}: {count}个")
            
            # 模块分布
            row += 2
            ws.cell(row=row, column=1, value="模块分布:").font = Font(bold=True)
            for module, count in module_stats.items():
                row += 1
                ws.cell(row=row, column=1, value=f"  {module}: {count}个")
            
            # 生成文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{project_name}_测试汇总报告_{timestamp}.xlsx"
            filepath = self.output_dir / filename
            
            wb.save(filepath)
            print(f"汇总报告生成成功: {filepath}")
            
            return str(filepath)
            
        except Exception as e:
            print(f"生成汇总报告失败: {str(e)}")
            raise Exception(f"生成汇总报告失败: {str(e)}")

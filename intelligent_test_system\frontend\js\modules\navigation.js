/**
 * 导航模块 - 负责页面切换和导航管理
 */

class NavigationModule {
    constructor() {
        this.currentPage = 'home';
        this.pageHandlers = new Map();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.registerPageHandlers();
    }

    setupEventListeners() {
        // 导航点击事件委托
        document.addEventListener('click', (e) => {
            const navItem = e.target.closest('.nav-item');
            if (navItem && navItem.onclick) {
                // 处理导航点击
                this.handleNavClick(navItem);
            }
        });
    }

    registerPageHandlers() {
        // 注册页面处理器
        this.pageHandlers.set('system', () => this.checkSystemHealth());
        this.pageHandlers.set('prompts', () => this.loadPrompts());
        this.pageHandlers.set('testCaseLibrary', () => this.loadTestCasesLibrary());
        this.pageHandlers.set('requirementAssets', () => this.loadRequirementAssets());
    }

    handleNavClick(navItem) {
        // 移除所有active类
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // 添加active类到当前项
        navItem.classList.add('active');
    }

    showPage(pageId, element = null) {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.add('hidden');
        });

        // 移除所有导航项的active类
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // 显示目标页面
        const targetPage = document.getElementById(pageId + 'Page');
        if (targetPage) {
            targetPage.classList.remove('hidden');
        }

        // 设置对应导航项为active
        if (element) {
            element.classList.add('active');
        }

        // 更新页面标题
        this.updatePageTitle(pageId);

        // 执行页面特定的处理器
        if (this.pageHandlers.has(pageId)) {
            this.pageHandlers.get(pageId)();
        }

        this.currentPage = pageId;
    }

    updatePageTitle(pageId) {
        const titles = {
            'home': '数据中台智能助手',
            'queryUnderstanding': '查询需求理解',
            'requirementAssets': '需求资产库',
            'testRequirementUnderstanding': '测试需求理解',
            'testCaseGeneration': '测试用例生成',
            'aiTestGeneration': 'AI智能测试用例生成',
            'testCaseLibrary': '测试用例库',
            'apiQueryUnderstanding': '查询需求理解接口',
            'prompts': '提示词管理',
            'analytics': '测试数据分析',
            'system': '系统状态监控'
        };

        const titleElement = document.getElementById('pageTitle');
        if (titleElement && titles[pageId]) {
            titleElement.textContent = titles[pageId];
        }
    }

    // 页面处理器方法（这些方法会被其他模块覆盖）
    checkSystemHealth() {
        console.log('System health check - to be implemented by SystemModule');
    }

    loadPrompts() {
        console.log('Load prompts - to be implemented by PromptsModule');
    }

    loadTestCasesLibrary() {
        console.log('Load test cases - to be implemented by TestCaseModule');
    }

    loadRequirementAssets() {
        console.log('Load requirement assets - to be implemented by RequirementModule');
    }
}

// 全局导航实例
window.navigationModule = new NavigationModule();

// 全局函数，保持向后兼容
function showPage(pageId, element = null) {
    window.navigationModule.showPage(pageId, element);
}

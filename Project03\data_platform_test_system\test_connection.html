<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前后端连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>前后端连接测试</h1>
    
    <div class="test-container">
        <h2>基础连接测试</h2>
        <button onclick="testHealthCheck()">测试健康检查</button>
        <button onclick="testRequirements()">测试需求API</button>
        <button onclick="testCORS()">测试CORS</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div id="results"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        const API_PREFIX = '/api/v1';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('results').innerHTML = '';
        }
        
        async function testHealthCheck() {
            log('开始测试健康检查...');
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                    showResult('✅ 健康检查成功', 'success');
                } else {
                    const errorText = await response.text();
                    log(`错误响应: ${errorText}`);
                    showResult(`❌ 健康检查失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`请求异常: ${error.message}`);
                showResult(`❌ 健康检查异常: ${error.message}`, 'error');
            }
        }
        
        async function testRequirements() {
            log('开始测试需求API...');
            try {
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/`);
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                    showResult('✅ 需求API测试成功', 'success');
                } else {
                    const errorText = await response.text();
                    log(`错误响应: ${errorText}`);
                    showResult(`❌ 需求API测试失败: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`请求异常: ${error.message}`);
                showResult(`❌ 需求API异常: ${error.message}`, 'error');
            }
        }
        
        async function testCORS() {
            log('开始测试CORS...');
            try {
                const response = await fetch(`${API_BASE_URL}/health`, {
                    method: 'OPTIONS'
                });
                log(`OPTIONS响应状态: ${response.status}`);
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                log(`CORS头信息: ${JSON.stringify(corsHeaders, null, 2)}`);
                
                if (corsHeaders['Access-Control-Allow-Origin']) {
                    showResult('✅ CORS配置正常', 'success');
                } else {
                    showResult('❌ CORS配置可能有问题', 'error');
                }
            } catch (error) {
                log(`CORS测试异常: ${error.message}`);
                showResult(`❌ CORS测试异常: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动测试...');
            setTimeout(testHealthCheck, 1000);
        });
    </script>
</body>
</html>

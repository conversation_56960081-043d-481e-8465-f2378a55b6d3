# -*- coding: utf-8 -*-
"""
AI智能服务
使用OpenAI GPT-4实现智能测试用例生成
"""

import os
import json
import asyncio
import base64
from typing import List, Dict, Any, AsyncGenerator
from datetime import datetime

from openai import OpenAI

from ..models.schemas import TestCase, TestCaseRequest, FileType


class AIService:
    """AI智能服务类"""
    
    def __init__(self):
        """初始化AI服务"""
        self.openai_client = OpenAI(
            api_key=os.getenv("OPENAI_API_KEY", "your-api-key-here")
        )
        print("AI服务初始化完成")
    
    def _get_system_message(self) -> str:
        """获取系统提示消息"""
        return """
        你是一个专业的数据测试专家，专门为数据中台项目生成高质量的测试用例。
        
        你的任务是：
        1. 分析用户提供的需求、上下文信息和图片内容
        2. 生成全面、专业的测试用例
        3. 确保测试用例覆盖功能测试、数据质量测试、性能测试等多个维度
        4. 每个测试用例必须包含：用例标题、描述、前置条件、测试步骤、预期结果、优先级、测试类型、所属模块
        
        测试用例格式要求：
        - 用例ID：格式为TC_YYYYMMDD_XXX
        - 标题：简洁明确，体现测试目标
        - 描述：详细说明测试目的和范围
        - 前置条件：列出执行测试前需要满足的条件
        - 测试步骤：详细的操作步骤，每步都要清晰具体
        - 预期结果：明确的验证标准
        - 优先级：高/中/低
        - 测试类型：功能测试/数据质量测试/性能测试/安全测试等
        - 所属模块：根据需求确定模块名称
        
        请以JSON格式返回测试用例列表，格式如下：
        [
            {
                "case_id": "TC_20240101_001",
                "title": "数据导入功能测试",
                "description": "验证数据导入功能的正确性",
                "preconditions": "系统正常运行，数据源可访问",
                "test_steps": ["步骤1", "步骤2", "步骤3"],
                "expected_result": "数据成功导入",
                "priority": "高",
                "test_type": "功能测试",
                "module": "数据导入模块"
            }
        ]
        """
    
    async def analyze_image(self, image_path: str, file_type: FileType) -> str:
        """
        分析图片内容
        
        Args:
            image_path: 图片文件路径
            file_type: 文件类型
            
        Returns:
            图片分析结果
        """
        try:
            # 根据文件类型调整分析提示
            analysis_prompt = self._get_image_analysis_prompt(file_type)
            
            # 读取图片文件并转换为base64
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')
            
            # 使用OpenAI Vision API分析图片
            response = self.openai_client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": analysis_prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )
            
            analysis_result = response.choices[0].message.content
            print(f"图片分析完成: {image_path}")
            return analysis_result
            
        except Exception as e:
            print(f"图片分析失败: {str(e)}")
            return f"图片分析失败: {str(e)}"
    
    def _get_image_analysis_prompt(self, file_type: FileType) -> str:
        """根据文件类型获取图片分析提示"""
        prompts = {
            FileType.MINDMAP: "请分析这个思维导图，提取其中的业务流程、功能模块和关键信息点。",
            FileType.FLOWCHART: "请分析这个流程图，识别业务流程的各个步骤、决策点和数据流向。",
            FileType.SCREENSHOT: "请分析这个界面截图，识别页面功能、交互元素和业务逻辑。",
            FileType.IMAGE: "请分析这个图片，提取与业务需求相关的信息。"
        }
        return prompts.get(file_type, prompts[FileType.IMAGE])
    
    async def generate_test_cases_stream(
        self, 
        request: TestCaseRequest, 
        image_analysis: str = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式生成测试用例
        
        Args:
            request: 测试用例生成请求
            image_analysis: 图片分析结果
            
        Yields:
            流式响应数据
        """
        try:
            # 发送开始信号
            yield {
                "type": "progress",
                "data": {
                    "message": "开始生成测试用例...",
                    "progress": 10
                }
            }
            
            # 构建完整的提示信息
            full_prompt = self._build_generation_prompt(request, image_analysis)
            
            yield {
                "type": "progress", 
                "data": {
                    "message": "分析需求和上下文...",
                    "progress": 30
                }
            }
            
            # 使用OpenAI生成测试用例
            response = await self._generate_with_openai(full_prompt)
            
            yield {
                "type": "progress",
                "data": {
                    "message": "生成测试用例中...",
                    "progress": 70
                }
            }
            
            # 解析生成的测试用例
            test_cases = self._parse_test_cases(response)
            
            yield {
                "type": "progress",
                "data": {
                    "message": "处理测试用例格式...",
                    "progress": 90
                }
            }
            
            # 逐个返回测试用例
            for i, test_case in enumerate(test_cases):
                yield {
                    "type": "testcase",
                    "data": {
                        "test_case": test_case.dict(),
                        "index": i + 1,
                        "total": len(test_cases)
                    }
                }
                await asyncio.sleep(0.1)  # 模拟流式输出
            
            # 发送完成信号
            yield {
                "type": "complete",
                "data": {
                    "message": "测试用例生成完成！",
                    "total_cases": len(test_cases),
                    "progress": 100
                }
            }
            
        except Exception as e:
            print(f"生成测试用例失败: {str(e)}")
            yield {
                "type": "error",
                "data": {
                    "error": str(e),
                    "message": "生成测试用例时发生错误"
                }
            }
    
    async def _generate_with_openai(self, prompt: str) -> str:
        """使用OpenAI生成测试用例"""
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self._get_system_message()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.7
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"OpenAI生成失败: {str(e)}")
            return self._get_fallback_response()
    
    def _get_fallback_response(self) -> str:
        """获取备用响应（当API调用失败时使用）"""
        return json.dumps([
            {
                "case_id": f"TC_{datetime.now().strftime('%Y%m%d')}_001",
                "title": "数据质量验证测试",
                "description": "验证数据导入后的数据质量和完整性",
                "preconditions": "数据源准备完毕，系统正常运行",
                "test_steps": [
                    "准备测试数据",
                    "执行数据导入操作",
                    "检查数据完整性",
                    "验证数据格式正确性",
                    "确认数据量统计准确"
                ],
                "expected_result": "数据导入成功，数据质量符合要求",
                "priority": "高",
                "test_type": "数据质量测试",
                "module": "数据导入模块"
            },
            {
                "case_id": f"TC_{datetime.now().strftime('%Y%m%d')}_002",
                "title": "数据处理性能测试",
                "description": "验证数据处理的性能指标",
                "preconditions": "系统正常运行，测试数据准备完毕",
                "test_steps": [
                    "准备大量测试数据",
                    "启动数据处理任务",
                    "监控处理时间",
                    "检查系统资源使用情况",
                    "验证处理结果正确性"
                ],
                "expected_result": "数据处理在规定时间内完成，系统性能稳定",
                "priority": "中",
                "test_type": "性能测试",
                "module": "数据处理模块"
            }
        ])
    
    def _build_generation_prompt(self, request: TestCaseRequest, image_analysis: str = None) -> str:
        """构建测试用例生成提示"""
        prompt_parts = [
            f"项目名称: {request.project_name}",
            f"需求描述: {request.requirements}",
        ]
        
        if request.context_info:
            prompt_parts.append(f"上下文信息: {request.context_info}")
        
        if image_analysis:
            prompt_parts.append(f"图片分析结果: {image_analysis}")
        
        if request.additional_notes:
            prompt_parts.append(f"额外说明: {request.additional_notes}")
        
        prompt_parts.append(
            "\n请基于以上信息生成全面的测试用例，确保覆盖数据质量、功能验证、性能测试等多个维度。"
            "请以JSON格式返回测试用例列表，每个测试用例包含所有必需字段。"
        )
        
        return "\n\n".join(prompt_parts)
    
    def _parse_test_cases(self, response_content: str) -> List[TestCase]:
        """解析生成的测试用例"""
        try:
            # 尝试从响应中提取JSON
            if "```json" in response_content:
                json_start = response_content.find("```json") + 7
                json_end = response_content.find("```", json_start)
                json_content = response_content[json_start:json_end].strip()
            else:
                json_content = response_content
            
            # 解析JSON
            test_cases_data = json.loads(json_content)
            
            # 转换为TestCase对象
            test_cases = []
            for i, case_data in enumerate(test_cases_data):
                # 确保必需字段存在
                case_data.setdefault("case_id", f"TC_{datetime.now().strftime('%Y%m%d')}_{i+1:03d}")
                case_data.setdefault("priority", "中")
                case_data.setdefault("test_type", "功能测试")
                case_data.setdefault("module", "数据中台")
                
                test_case = TestCase(**case_data)
                test_cases.append(test_case)
            
            return test_cases
            
        except Exception as e:
            print(f"解析测试用例失败: {str(e)}")
            # 返回备用测试用例
            fallback_data = json.loads(self._get_fallback_response())
            return [TestCase(**case_data) for case_data in fallback_data]

# 🔑 DeepSeek API密钥配置指南

## 📋 **当前状态**
系统当前使用的是示例API密钥，需要配置您的真实DeepSeek API密钥才能使用AI功能。

## 🚀 **获取API密钥**

### 1. 注册DeepSeek账号
- 访问：https://platform.deepseek.com/
- 注册账号并完成验证

### 2. 创建API密钥
- 登录后进入"API密钥"页面
- 点击"创建新密钥"
- 复制生成的API密钥（格式：sk-xxxxxxxxxxxxxxxx）

## ⚙️ **配置方法**

### **方法1: 修改配置文件（推荐）**
编辑 `backend/config.py` 文件：
```python
# 将这行
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "your-deepseek-api-key-here")

# 改为
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "sk-your-actual-api-key")
```

### **方法2: 使用环境变量**

**Windows:**
```cmd
set DEEPSEEK_API_KEY=sk-your-actual-api-key
python start.py
```

**Linux/Mac:**
```bash
export DEEPSEEK_API_KEY=sk-your-actual-api-key
python start.py
```

### **方法3: 创建.env文件**
在 `backend/` 目录下创建 `.env` 文件：
```
DEEPSEEK_API_KEY=sk-your-actual-api-key
```

## 🔍 **验证配置**

### 1. 检查配置状态
```bash
cd backend
python config.py
```

### 2. 测试API连接
启动系统后访问：
- http://localhost:8000/api/config/api-key
- http://localhost:8000/api/health

### 3. 在界面中测试
- 打开系统界面：http://localhost:8000
- 使用"查询需求理解"功能测试AI响应

## ⚠️ **安全注意事项**

1. **不要提交API密钥到版本控制**
   - 将 `.env` 文件添加到 `.gitignore`
   - 不要在代码中硬编码API密钥

2. **定期轮换密钥**
   - 建议每3-6个月更换一次API密钥
   - 如果密钥泄露，立即在DeepSeek平台禁用

3. **监控使用量**
   - 在DeepSeek平台查看API使用量和费用
   - 设置使用量告警

## 🛠️ **故障排除**

### 问题1: API密钥无效
```
错误：Invalid API key
解决：检查API密钥是否正确复制，确保没有多余的空格
```

### 问题2: 网络连接失败
```
错误：Connection timeout
解决：检查网络连接，确保可以访问 api.deepseek.com
```

### 问题3: 配额不足
```
错误：Quota exceeded
解决：在DeepSeek平台充值或等待配额重置
```

## 📞 **获取帮助**

1. **查看配置状态**：访问 http://localhost:8000/api/config/api-key
2. **查看系统健康**：访问 http://localhost:8000/api/health
3. **查看API文档**：访问 http://localhost:8000/docs

## 💡 **配置完成后**

配置完成后，系统将具备以下AI能力：
- 🧠 智能需求理解和分析
- 🧪 专业测试用例生成
- 📊 多模态文件分析
- 🔄 实时流式AI响应

**配置完成后重启系统即可使用完整的AI功能！** 🎉

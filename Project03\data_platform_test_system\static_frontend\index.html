<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中台智能测试系统</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            min-height: 100vh; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            text-align: center; 
            padding: 40px 20px; 
        }
        .title { 
            font-size: 3em; 
            margin-bottom: 20px; 
        }
        .subtitle { 
            font-size: 1.2em; 
            margin-bottom: 40px; 
            opacity: 0.9; 
        }
        .cards { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin-top: 40px; 
        }
        .card { 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 10px; 
            backdrop-filter: blur(10px); 
        }
        .card h3 { 
            margin-bottom: 15px; 
        }
        .link { 
            display: inline-block; 
            margin: 10px; 
            padding: 12px 24px; 
            background: rgba(255,255,255,0.2); 
            color: white; 
            text-decoration: none; 
            border-radius: 6px; 
            transition: all 0.3s; 
        }
        .link:hover { 
            background: rgba(255,255,255,0.3); 
        }
        .status { 
            margin-top: 30px; 
            padding: 20px; 
            background: rgba(255,255,255,0.1); 
            border-radius: 8px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">数据中台智能测试系统</h1>
        <p class="subtitle">基于AutoGen + DeepSeek的智能需求分析平台</p>
        
        <div class="status">
            <h3>系统运行正常</h3>
            <p>前端和后端服务已启动</p>
        </div>
        
        <div class="cards">
            <div class="card">
                <h3>需求管理</h3>
                <p>创建、编辑和管理数据开发需求</p>
                <a href="http://localhost:8000/api/v1/requirements/" class="link">需求API</a>
            </div>
            <div class="card">
                <h3>AI分析</h3>
                <p>使用AutoGen + DeepSeek进行智能分析</p>
                <a href="http://localhost:8000/api/v1/requirements/analyses/" class="link">分析API</a>
            </div>
            <div class="card">
                <h3>API文档</h3>
                <p>查看完整的API接口文档</p>
                <a href="http://localhost:8000/docs" class="link">API文档</a>
            </div>
            <div class="card">
                <h3>系统监控</h3>
                <p>查看系统运行状态和健康检查</p>
                <a href="http://localhost:8000/health" class="link">健康检查</a>
            </div>
        </div>
        
        <div style="margin-top: 40px;">
            <p>后端服务: <a href="http://localhost:8000" class="link">http://localhost:8000</a></p>
            <p>前端服务: <a href="http://localhost:3000" class="link">http://localhost:3000</a></p>
        </div>
    </div>
</body>
</html>
/**
 * 需求录入页面
 * 负责需求的创建和编辑功能
 */

window.RequirementsCreatePage = {
    isEditMode: false,
    editingId: null,
    modules: [],
    uploadedFiles: [],
    
    async init() {
        console.log('初始化需求录入页面...');
        this.bindEvents();
        this.resetForm();
    },
    
    bindEvents() {
        // 取消按钮
        const cancelBtn = document.getElementById('cancelCreateBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                window.PageManager.showPage('requirements-list');
            });
        }
        
        // 保存按钮
        const saveBtn = document.getElementById('saveRequirementBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveRequirement();
            });
        }
        
        // 模块标签输入
        const moduleInput = document.getElementById('moduleInput');
        if (moduleInput) {
            moduleInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.addModule(moduleInput.value.trim());
                    moduleInput.value = '';
                }
            });
        }
        
        // 文件上传
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        if (uploadArea && fileInput) {
            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });
            
            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                this.handleFiles(e.dataTransfer.files);
            });
            
            // 文件选择
            fileInput.addEventListener('change', (e) => {
                this.handleFiles(e.target.files);
            });
        }
        
        // 表单验证
        const form = document.getElementById('requirementForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveRequirement();
            });
        }
    },
    
    resetForm() {
        this.isEditMode = false;
        this.editingId = null;
        this.modules = [];
        this.uploadedFiles = [];
        
        // 清空表单
        const form = document.getElementById('requirementForm');
        if (form) {
            form.reset();
        }
        
        // 清空模块标签
        this.renderModules();
        
        // 清空上传文件
        this.renderUploadedFiles();
        
        // 更新页面标题
        const pageTitle = document.querySelector('#requirements-create-page .page-header h2');
        if (pageTitle) {
            pageTitle.textContent = '需求录入';
        }
        
        const saveBtn = document.getElementById('saveRequirementBtn');
        if (saveBtn) {
            saveBtn.innerHTML = '<i class="fas fa-save"></i> 保存需求';
        }
    },
    
    async loadForEdit(id) {
        try {
            this.isEditMode = true;
            this.editingId = id;
            
            const requirement = await API.getRequirement(id);
            
            // 填充表单
            document.getElementById('title').value = requirement.title || '';
            document.getElementById('description').value = requirement.description || '';
            document.getElementById('priority').value = requirement.priority || '';
            document.getElementById('createdBy').value = requirement.created_by || '';
            
            // 设置模块
            this.modules = requirement.modules || [];
            this.renderModules();
            
            // 设置附件
            this.uploadedFiles = requirement.attachments || [];
            this.renderUploadedFiles();
            
            // 更新页面标题
            const pageTitle = document.querySelector('#requirements-create-page .page-header h2');
            if (pageTitle) {
                pageTitle.textContent = '编辑需求';
            }
            
            const saveBtn = document.getElementById('saveRequirementBtn');
            if (saveBtn) {
                saveBtn.innerHTML = '<i class="fas fa-save"></i> 更新需求';
            }
            
        } catch (error) {
            console.error('加载需求详情失败:', error);
            Utils.error('加载需求详情失败');
        }
    },
    
    addModule(moduleName) {
        if (!moduleName || this.modules.includes(moduleName)) {
            return;
        }
        
        this.modules.push(moduleName);
        this.renderModules();
    },
    
    removeModule(moduleName) {
        this.modules = this.modules.filter(m => m !== moduleName);
        this.renderModules();
    },
    
    renderModules() {
        const container = document.getElementById('moduleTags');
        if (!container) return;
        
        const inputContainer = container.querySelector('.tag-input-container');
        
        // 清空现有标签
        container.querySelectorAll('.module-tag').forEach(tag => tag.remove());
        
        // 添加模块标签
        this.modules.forEach(module => {
            const tag = document.createElement('div');
            tag.className = 'module-tag';
            tag.innerHTML = `
                ${Utils.escapeHtml(module)}
                <span class="remove-tag" onclick="RequirementsCreatePage.removeModule('${module}')">
                    <i class="fas fa-times"></i>
                </span>
            `;
            container.insertBefore(tag, inputContainer);
        });
    },
    
    async handleFiles(files) {
        for (let file of files) {
            if (!this.validateFile(file)) {
                continue;
            }
            
            try {
                // 这里可以实现文件上传到服务器
                // const uploadResult = await API.uploadFile(file);
                
                // 暂时添加到本地列表
                const fileInfo = {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    url: URL.createObjectURL(file) // 临时URL
                };
                
                this.uploadedFiles.push(fileInfo);
                this.renderUploadedFiles();
                
                Utils.success(`文件 ${file.name} 上传成功`);
                
            } catch (error) {
                console.error('文件上传失败:', error);
                Utils.error(`文件 ${file.name} 上传失败`);
            }
        }
    },
    
    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'image/png',
            'image/jpeg',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        if (file.size > maxSize) {
            Utils.error(`文件 ${file.name} 超过最大大小限制 (10MB)`);
            return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
            Utils.error(`文件 ${file.name} 格式不支持`);
            return false;
        }
        
        return true;
    },
    
    removeFile(index) {
        this.uploadedFiles.splice(index, 1);
        this.renderUploadedFiles();
    },
    
    renderUploadedFiles() {
        const container = document.getElementById('uploadedFiles');
        if (!container) return;
        
        if (this.uploadedFiles.length === 0) {
            container.innerHTML = '';
            return;
        }
        
        container.innerHTML = this.uploadedFiles.map((file, index) => `
            <div class="uploaded-file">
                <div class="file-info">
                    <i class="file-icon ${Utils.getFileIcon(file.name)}"></i>
                    <div>
                        <div class="file-name">${Utils.escapeHtml(file.name)}</div>
                        <div class="file-size">${Utils.formatFileSize(file.size)}</div>
                    </div>
                </div>
                <span class="remove-file" onclick="RequirementsCreatePage.removeFile(${index})">
                    <i class="fas fa-times"></i>
                </span>
            </div>
        `).join('');
    },
    
    validateForm() {
        const errors = [];
        
        const title = document.getElementById('title').value.trim();
        const priority = document.getElementById('priority').value;
        
        if (!title) {
            errors.push('需求标题不能为空');
        }
        
        if (!priority) {
            errors.push('请选择优先级');
        }
        
        if (errors.length > 0) {
            Utils.error('表单验证失败：\n' + errors.join('\n'));
            return false;
        }
        
        return true;
    },
    
    async saveRequirement() {
        if (!this.validateForm()) {
            return;
        }
        
        try {
            const formData = {
                title: document.getElementById('title').value.trim(),
                description: document.getElementById('description').value.trim(),
                priority: document.getElementById('priority').value,
                created_by: document.getElementById('createdBy').value.trim(),
                modules: this.modules,
                attachments: this.uploadedFiles
            };
            
            let result;
            if (this.isEditMode) {
                result = await API.updateRequirement(this.editingId, formData);
                Utils.success('需求更新成功');
            } else {
                result = await API.createRequirement(formData);
                Utils.success('需求创建成功');
            }
            
            // 跳转到需求列表
            window.PageManager.showPage('requirements-list');
            
        } catch (error) {
            console.error('保存需求失败:', error);
            Utils.error('保存需求失败');
        }
    }
};

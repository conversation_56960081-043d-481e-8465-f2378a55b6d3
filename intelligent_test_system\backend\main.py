"""
数据中台智能助手 - 主服务器文件
基于AutoGen + DeepSeek的AI驱动测试用例生成平台
"""

import json
import uuid
import asyncio
import base64
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# FastAPI相关导入
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import StreamingResponse, FileResponse, HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn

# AutoGen相关导入
try:
    from autogen import AssistantAgent, UserProxyAgent
    AUTOGEN_AVAILABLE = True
except ImportError:
    AUTOGEN_AVAILABLE = False

# 导入提示词配置
from prompts_config import get_prompts, update_prompts

# 多模态支持
import base64

# 导入模块化组件
from modules import (
    RequirementUnderstandingModule,
    TestCaseGenerationModule,
    APIInterfaceModule,
    SystemManagementModule
)

# 导入AI生成器和配置
from ai_generator import AITestCaseGenerator
from config import get_api_key_status, validate_api_key, CONFIG_HELP

# 创建FastAPI应用
app = FastAPI(
    title="数据中台智能助手",
    description="基于AutoGen + DeepSeek的AI驱动数据中台智能助手",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务 - 修复路径问题
from pathlib import Path
import os

# 获取正确的前端路径
current_dir = Path(__file__).parent
frontend_dir = current_dir.parent / "frontend"

print(f"📁 前端目录: {frontend_dir}")
print(f"📁 前端目录存在: {frontend_dir.exists()}")

if frontend_dir.exists():
    # 挂载静态文件服务
    app.mount("/static", StaticFiles(directory=str(frontend_dir)), name="static")

    # 检查并挂载JS文件目录
    js_dir = frontend_dir / "js"
    if js_dir.exists():
        app.mount("/js", StaticFiles(directory=str(js_dir)), name="js")
        print(f"✅ JS目录已挂载: {js_dir}")
    else:
        print(f"⚠️ JS目录不存在: {js_dir}")

    # 检查并挂载CSS文件目录（如果存在）
    css_dir = frontend_dir / "css"
    if css_dir.exists():
        app.mount("/css", StaticFiles(directory=str(css_dir)), name="css")
        print(f"✅ CSS目录已挂载: {css_dir}")
    else:
        print(f"⚠️ CSS目录不存在，跳过挂载: {css_dir}")
else:
    print("❌ 前端目录不存在")

# 创建必要的目录
UPLOAD_DIR = Path("uploads")
EXPORT_DIR = Path("exports")
UPLOAD_DIR.mkdir(exist_ok=True)
EXPORT_DIR.mkdir(exist_ok=True)

# 创建AI生成器实例
ai_generator = AITestCaseGenerator()

# 初始化模块化组件
requirement_module = RequirementUnderstandingModule(ai_generator)
test_case_module = TestCaseGenerationModule(ai_generator)
api_module = APIInterfaceModule()
system_module = SystemManagementModule(ai_generator)

# 测试用例存储 - 内存存储（生产环境建议使用数据库）
test_cases_storage = {
    "tasks": {},  # 存储任务和对应的测试用例 {task_id: {test_cases, metadata}}
    "latest_task_id": None  # 最新的任务ID
}

# 存储生成结果（保持向后兼容）
generation_results = {}

@app.get("/api/health")
async def health_check(skip_api_test: bool = False):
    """系统健康检查接口"""
    try:
        print(f"🏥 开始健康检查 (skip_api_test={skip_api_test})...")

        health_status = await system_module.check_system_health(skip_api_test=skip_api_test)

        # 添加API密钥状态
        api_key_status = get_api_key_status()
        health_status["api_key_status"] = api_key_status

        print(f"✅ 健康检查完成: {health_status.get('status', 'unknown')}")
        return health_status

    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error_message": f"健康检查失败: {str(e)}",
            "ai_framework": "AutoGen + DeepSeek",
            "version": "2.0.0",
            "features": [
                "AI智能测试用例生成",
                "需求智能理解",
                "多模态文档分析",
                "流式响应输出",
                "Excel导出功能"
            ]
        }

@app.get("/api/health/quick")
async def quick_health_check():
    """快速健康检查接口（跳过API测试）"""
    return await health_check(skip_api_test=True)

@app.get("/api/config/api-key")
async def get_api_key_config():
    """获取API密钥配置状态"""
    status = get_api_key_status()
    return {
        "status": "success",
        "api_key_config": status,
        "help": CONFIG_HELP,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/prompts")
async def get_prompts_api():
    """获取当前的提示词配置"""
    return system_module.get_prompts_config()

@app.post("/api/prompts")
async def update_prompts_api(
    requirement_analyst: str = Form(..., description="需求分析专家提示词"),
    data_test_expert: str = Form(..., description="数据测试专家提示词"),
    document_analyst: str = Form(..., description="文档分析专家提示词"),
    requirement_understanding: str = Form(..., description="需求智能理解专家提示词")
):
    """更新提示词配置"""
    prompts_data = {
        'requirement_analyst': requirement_analyst,
        'data_test_expert': data_test_expert,
        'document_analyst': document_analyst,
        'requirement_understanding': requirement_understanding
    }
    return system_module.update_prompts_config(prompts_data)

@app.get("/api/prompts/default")
async def get_default_prompts():
    """获取默认提示词配置"""
    # 从配置文件获取默认提示词
    from prompts_config import (
        REQUIREMENT_ANALYST_PROMPT,
        DATA_TEST_EXPERT_PROMPT,
        DOCUMENT_ANALYST_PROMPT,
        REQUIREMENT_UNDERSTANDING_PROMPT
    )

    default_prompts = {
        "requirement_analyst": REQUIREMENT_ANALYST_PROMPT,
        "data_test_expert": DATA_TEST_EXPERT_PROMPT,
        "document_analyst": DOCUMENT_ANALYST_PROMPT,
        "requirement_understanding": REQUIREMENT_UNDERSTANDING_PROMPT
    }

    return {
        "status": "success",
        "prompts": default_prompts,
        "message": "默认提示词获取成功"
    }

@app.post("/api/test-json-parsing")
async def test_json_parsing(
    json_text: str = Form(..., description="要测试的JSON文本")
):
    """测试JSON解析功能"""
    try:
        # 使用相同的解析逻辑
        ai_generator_instance = AITestCaseGenerator()
        result = ai_generator_instance._parse_and_structure_requirements(json_text, "测试项目")

        return {
            "status": "success",
            "message": "JSON解析成功",
            "parsed_result": result,
            "original_text": json_text[:500] + "..." if len(json_text) > 500 else json_text
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"JSON解析失败: {str(e)}",
            "original_text": json_text[:500] + "..." if len(json_text) > 500 else json_text
        }

@app.get("/api/test-cases")
async def get_test_cases():
    """获取测试用例库中的所有测试用例"""
    try:
        all_tasks = []
        for task_id, task_data in test_cases_storage["tasks"].items():
            task_info = {
                "task_id": task_id,
                "project_name": task_data["metadata"]["project_name"],
                "total_cases": task_data["metadata"]["total_cases"],
                "generation_time": task_data["metadata"]["generation_time"],
                "ai_framework": task_data["metadata"]["ai_framework"]
            }
            all_tasks.append(task_info)

        # 按生成时间倒序排列
        all_tasks.sort(key=lambda x: x["generation_time"], reverse=True)

        return {
            "status": "success",
            "tasks": all_tasks,
            "total_tasks": len(all_tasks),
            "latest_task_id": test_cases_storage["latest_task_id"],
            "message": "测试用例库获取成功"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"获取测试用例库失败: {str(e)}"
        }

@app.get("/api/test-cases/{task_id}")
async def get_test_cases_by_task(task_id: str):
    """获取指定任务的详细测试用例"""
    try:
        if task_id not in test_cases_storage["tasks"]:
            return {
                "status": "error",
                "message": f"任务 {task_id} 不存在"
            }

        task_data = test_cases_storage["tasks"][task_id]
        return {
            "status": "success",
            "task_id": task_id,
            "test_cases": task_data["test_cases"],
            "metadata": task_data["metadata"],
            "message": "测试用例获取成功"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"获取测试用例失败: {str(e)}"
        }

@app.delete("/api/test-cases/{task_id}")
async def delete_test_cases(task_id: str):
    """删除指定的测试用例任务"""
    try:
        if task_id not in test_cases_storage["tasks"]:
            return {
                "status": "error",
                "message": f"任务 {task_id} 不存在"
            }

        # 删除任务
        del test_cases_storage["tasks"][task_id]

        # 如果删除的是最新任务，更新latest_task_id
        if test_cases_storage["latest_task_id"] == task_id:
            if test_cases_storage["tasks"]:
                # 找到最新的任务
                latest_task = max(test_cases_storage["tasks"].items(),
                                key=lambda x: x[1]["metadata"]["generation_time"])
                test_cases_storage["latest_task_id"] = latest_task[0]
            else:
                test_cases_storage["latest_task_id"] = None

        # 同时从generation_results中删除（向后兼容）
        if task_id in generation_results:
            del generation_results[task_id]

        return {
            "status": "success",
            "message": f"任务 {task_id} 删除成功"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"删除任务失败: {str(e)}"
        }

@app.get("/api/test-autogen")
async def test_autogen():
    """测试AutoGen + DeepSeek连接"""
    try:
        # 测试简单的AI调用
        test_prompt = "请简单回复：AutoGen + DeepSeek连接测试成功"

        response = await asyncio.to_thread(
            ai_generator.requirement_analyst.generate_reply,
            messages=[{"role": "user", "content": test_prompt}]
        )

        return {
            "status": "success",
            "message": "AutoGen + DeepSeek连接正常",
            "ai_response": response[:200] + "..." if len(response) > 200 else response,
            "config": {
                "model": "deepseek-chat",
                "api_key": "***********************************",
                "base_url": "https://api.deepseek.com/v1"
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"AutoGen + DeepSeek连接失败: {str(e)}",
            "config": {
                "model": "deepseek-chat",
                "api_key": "***********************************",
                "base_url": "https://api.deepseek.com/v1"
            }
        }

@app.post("/api/requirement-understanding")
async def requirement_understanding_stream(
    project_name: str = Form(..., description="项目名称"),
    requirements: str = Form(..., description="需求描述"),
    context_info: str = Form("", description="技术上下文信息"),
    files: List[UploadFile] = File(default=[], description="上传的文件")
):
    """需求智能理解接口 - 流式响应"""
    async def generate():
        try:
            # 使用需求理解模块进行流式生成
            async for chunk in requirement_module.process_requirement_understanding(
                project_name, requirements, context_info, files
            ):
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                await asyncio.sleep(0.1)
        except Exception as e:
            error_chunk = {
                "type": "error",
                "message": f"处理失败: {str(e)}",
                "progress": 0
            }
            yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

@app.post("/api/generate-test-cases")
async def generate_test_cases_stream(
    project_name: str = Form(..., description="项目名称"),
    requirements: str = Form(..., description="需求描述"),
    context_info: str = Form("", description="技术上下文信息"),
    test_case_count: int = Form(5, description="生成的测试用例数量"),
    files: List[UploadFile] = File(default=[], description="上传的文件")
):
    """AI测试用例生成接口 - 流式响应"""
    async def generate():
        try:
            # 使用测试用例生成模块进行流式生成
            async for chunk in test_case_module.generate_test_cases(
                project_name, requirements, context_info, test_case_count, files
            ):
                # 如果是完成类型的chunk，保存到全局存储
                if chunk.get("type") == "complete" and chunk.get("result"):
                    result = chunk["result"]
                    task_id = result["task_id"]

                    # 保存到全局测试用例存储
                    test_cases_storage["tasks"][task_id] = {
                        "test_cases": result["test_cases"],
                        "metadata": {
                            "project_name": result["project_name"],
                            "requirements": result["requirements"],
                            "context_info": result["context_info"],
                            "total_cases": result["test_case_count"],
                            "generation_time": result["generation_time"],
                            "ai_framework": result["ai_framework"],
                            "file_count": result.get("file_count", 0)
                        }
                    }

                    # 更新最新任务ID
                    test_cases_storage["latest_task_id"] = task_id

                    print(f"✅ 测试用例已保存到全局存储: {task_id}")
                    print(f"📊 当前存储中的任务数量: {len(test_cases_storage['tasks'])}")

                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                await asyncio.sleep(0.1)
        except Exception as e:
            error_chunk = {
                "type": "error",
                "message": f"生成失败: {str(e)}",
                "progress": 0
            }
            yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

@app.get("/")
async def serve_frontend():
    """提供前端页面"""
    current_dir = Path(__file__).parent
    frontend_path = current_dir.parent / "frontend" / "index.html"

    print(f"📄 查找前端文件: {frontend_path}")
    print(f"📄 文件存在: {frontend_path.exists()}")

    if frontend_path.exists():
        return FileResponse(str(frontend_path))
    else:
        return HTMLResponse(f"""
        <html>
            <head><title>数据中台智能助手</title></head>
            <body>
                <h1>🚀 数据中台智能助手</h1>
                <p>前端文件未找到: {frontend_path}</p>
                <p><a href="/docs">查看API文档</a></p>
                <p><a href="/api/health">系统健康检查</a></p>
            </body>
        </html>
        """)

@app.get("/test")
async def serve_test_page():
    """提供测试页面"""
    current_dir = Path(__file__).parent
    test_path = current_dir.parent / "frontend" / "test.html"

    if test_path.exists():
        return FileResponse(str(test_path))
    else:
        return HTMLResponse("<h1>测试页面未找到</h1>")

@app.get("/debug")
async def serve_debug_page():
    """提供调试测试页面"""
    current_dir = Path(__file__).parent
    debug_path = current_dir.parent / "frontend" / "debug_test.html"

    if debug_path.exists():
        return FileResponse(str(debug_path))
    else:
        return HTMLResponse("<h1>调试页面未找到</h1>")

@app.get("/simple")
async def serve_simple_test_page():
    """提供简化测试页面"""
    current_dir = Path(__file__).parent
    simple_path = current_dir.parent / "frontend" / "simple_test.html"

    if simple_path.exists():
        return FileResponse(str(simple_path))
    else:
        return HTMLResponse("<h1>简化测试页面未找到</h1>")

@app.get("/dom-test")
async def serve_dom_test_page():
    """提供DOM修复验证测试页面"""
    current_dir = Path(__file__).parent
    test_path = current_dir.parent / "test_dom_fix.html"

    if test_path.exists():
        return FileResponse(str(test_path))
    else:
        return HTMLResponse("<h1>DOM测试页面未找到</h1>")

@app.get("/debug")
async def serve_debug_test_page():
    """提供AI调试测试页面"""
    current_dir = Path(__file__).parent
    debug_path = current_dir.parent / "debug_test.html"

    if debug_path.exists():
        return FileResponse(str(debug_path))
    else:
        return HTMLResponse("<h1>调试测试页面未找到</h1>")

@app.get("/ai-explanation")
async def serve_ai_explanation_page():
    """提供AI生成模式说明页面"""
    current_dir = Path(__file__).parent
    explanation_path = current_dir.parent / "ai_explanation.html"

    if explanation_path.exists():
        return FileResponse(str(explanation_path))
    else:
        return HTMLResponse("<h1>AI说明页面未找到</h1>")

@app.post("/api/debug-generate")
async def debug_generate_test_cases(
    project_name: str = Form("测试项目"),
    requirements: str = Form("测试需求"),
    test_case_count: int = Form(2)
):
    """调试用的简化测试用例生成接口"""
    try:
        print(f"🧪 调试生成请求: {project_name}, {requirements}, {test_case_count}")

        # 直接调用AI生成器
        test_prompt = f"""
项目名称: {project_name}
需求描述: {requirements}

请生成 {test_case_count} 个测试用例，严格按照以下JSON格式输出：

```json
[
  {{
    "title": "测试用例标题",
    "objective": "测试目标",
    "preconditions": "前置条件",
    "test_steps": "测试步骤",
    "expected_result": "预期结果",
    "priority": "高",
    "test_type": "功能测试",
    "risk_level": "中",
    "business_impact": "业务影响",
    "test_data": "测试数据",
    "automation_feasibility": "高"
  }}
]
```
"""

        print(f"📤 发送提示词到DeepSeek...")
        print(f"🔑 使用API密钥: {ai_generator.llm_config['config_list'][0]['api_key'][:10]}...")
        print(f"🌐 API地址: {ai_generator.llm_config['config_list'][0]['base_url']}")
        print(f"🤖 模型: {ai_generator.llm_config['config_list'][0]['model']}")

        # 调用AI - 这是真实的AutoGen + DeepSeek调用
        print("🚀 正在调用真实的AutoGen + DeepSeek API...")
        response = await asyncio.to_thread(
            ai_generator.data_test_expert.generate_reply,
            messages=[{"role": "user", "content": test_prompt}]
        )

        print(f"📥 收到真实DeepSeek AI响应: {str(response)[:300]}...")
        print(f"📊 响应类型: {type(response)}")
        print(f"📏 响应长度: {len(str(response))}")

        # 尝试解析JSON
        import re
        # JSON提取模式（这些是解析模式，不是模拟模式）
        json_patterns = [
            r'```json\s*(\[[\s\S]*?\])\s*```',  # 模式0: 标准JSON代码块
            r'```\s*(\[[\s\S]*?\])\s*```',      # 模式1: 普通代码块
            r'(\[[\s\S]*\])',                   # 模式2: 直接JSON数组
        ]

        json_str = None
        pattern_names = ["标准JSON代码块", "普通代码块", "直接JSON数组"]

        for i, pattern in enumerate(json_patterns):
            match = re.search(pattern, str(response), re.DOTALL)
            if match:
                json_str = match.group(1)
                print(f"✅ 使用JSON解析模式 {i} ({pattern_names[i]}) 从真实AI响应中提取到JSON")
                break

        if json_str:
            try:
                test_cases = json.loads(json_str)
                print(f"✅ 成功解析 {len(test_cases)} 个测试用例")

                return {
                    "status": "success",
                    "message": f"成功生成 {len(test_cases)} 个测试用例",
                    "test_cases": test_cases,
                    "ai_response": str(response)[:500],
                    "extracted_json": json_str[:500]
                }
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return {
                    "status": "error",
                    "message": f"JSON解析失败: {str(e)}",
                    "ai_response": str(response)[:500],
                    "extracted_json": json_str[:500] if json_str else None
                }
        else:
            print("❌ 无法提取JSON")
            return {
                "status": "error",
                "message": "无法从AI响应中提取JSON格式的测试用例",
                "ai_response": str(response)[:500]
            }

    except Exception as e:
        print(f"❌ 调试生成失败: {e}")
        import traceback
        traceback.print_exc()

        return {
            "status": "error",
            "message": f"生成失败: {str(e)}",
            "error_details": str(e)
        }

if __name__ == "__main__":
    print("🚀 启动数据中台智能助手服务器...")
    print(f"📁 工作目录: {Path.cwd()}")
    print(f"🤖 AI框架: AutoGen + DeepSeek")
    print(f"🔑 DeepSeek API: ***********************************")

    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )

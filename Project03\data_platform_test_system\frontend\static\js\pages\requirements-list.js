/**
 * 需求列表页面
 * 负责需求列表的显示、筛选、搜索和批量操作
 */

window.RequirementsListPage = {
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    selectedIds: [],
    filters: {
        status: '',
        priority: '',
        keyword: ''
    },
    
    async init() {
        console.log('初始化需求列表页面...');
        this.bindEvents();
        await this.loadRequirements();
    },
    
    bindEvents() {
        // 创建需求按钮
        const createBtn = document.getElementById('createRequirementBtn');
        if (createBtn) {
            createBtn.addEventListener('click', () => {
                window.PageManager.showPage('requirements-create');
            });
        }
        
        // 批量分析按钮
        const batchAnalyzeBtn = document.getElementById('batchAnalyzeBtn');
        if (batchAnalyzeBtn) {
            batchAnalyzeBtn.addEventListener('click', () => {
                this.submitBatchAnalysis();
            });
        }
        
        // 搜索按钮
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.applyFilters();
            });
        }
        
        // 筛选器变化
        const statusFilter = document.getElementById('statusFilter');
        const priorityFilter = document.getElementById('priorityFilter');
        const keywordSearch = document.getElementById('keywordSearch');
        
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.filters.status = statusFilter.value;
                this.applyFilters();
            });
        }
        
        if (priorityFilter) {
            priorityFilter.addEventListener('change', () => {
                this.filters.priority = priorityFilter.value;
                this.applyFilters();
            });
        }
        
        if (keywordSearch) {
            keywordSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.filters.keyword = keywordSearch.value;
                    this.applyFilters();
                }
            });
        }
        
        // 全选复选框
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }
    },
    
    async loadRequirements() {
        console.log('开始加载需求列表...');
        try {
            const params = {
                page: this.currentPage,
                page_size: this.pageSize,
                ...this.filters
            };

            console.log('请求参数:', params);
            console.log('API对象:', window.API);

            const response = await API.getRequirements(params);
            console.log('API响应:', response);

            this.totalPages = response.total_pages || 0;
            this.renderRequirements(response.requirements || []);
            this.renderPagination(response);

            console.log('需求列表加载完成');

        } catch (error) {
            console.error('加载需求列表失败:', error);

            // 显示错误信息到页面
            const tbody = document.getElementById('requirementsTableBody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px; color: #dc3545;">
                            ❌ 加载失败: ${error.message}
                            <br><br>
                            <button onclick="RequirementsListPage.loadRequirements()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                重新加载
                            </button>
                        </td>
                    </tr>
                `;
            }

            if (window.Utils) {
                Utils.error('加载需求列表失败');
            } else {
                console.log('Utils对象不存在，使用alert显示错误');
            }
        }
    },
    
    renderRequirements(requirements) {
        const tbody = document.getElementById('requirementsTableBody');
        if (!tbody) return;
        
        if (requirements.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                        暂无需求数据
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = requirements.map(req => `
            <tr data-id="${req.id}" ${this.selectedIds.includes(req.id) ? 'class="selected"' : ''}>
                <td>
                    <input type="checkbox" class="requirement-checkbox" 
                           value="${req.id}" ${this.selectedIds.includes(req.id) ? 'checked' : ''}>
                </td>
                <td>${req.id}</td>
                <td>
                    <div class="requirement-title" title="${Utils.escapeHtml(req.description || '')}">
                        ${Utils.escapeHtml(req.title)}
                    </div>
                    ${!req.is_complete ? '<span class="incomplete-badge">信息不完整</span>' : ''}
                </td>
                <td>
                    <span class="priority-tag ${req.priority.toLowerCase()}">${req.priority}</span>
                </td>
                <td>
                    <span class="status-tag ${this.getStatusClass(req.status)}">${req.status}</span>
                </td>
                <td>${Utils.formatDate(req.created_time, 'YYYY-MM-DD')}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline" onclick="RequirementsListPage.editRequirement(${req.id})">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-success" onclick="RequirementsListPage.analyzeRequirement(${req.id})"
                                ${req.status === '分析中' ? 'disabled' : ''}>
                            <i class="fas fa-brain"></i> 分析
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="RequirementsListPage.deleteRequirement(${req.id})">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
        
        // 绑定复选框事件
        tbody.querySelectorAll('.requirement-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.toggleRequirementSelection(parseInt(e.target.value), e.target.checked);
            });
        });
    },
    
    renderPagination(response) {
        const pagination = document.getElementById('requirementsPagination');
        if (!pagination) return;
        
        const { total, page, page_size, total_pages } = response;
        
        let paginationHTML = `
            <div class="pagination-info">
                显示 ${((page - 1) * page_size) + 1}-${Math.min(page * page_size, total)} 条，共 ${total} 条
            </div>
        `;
        
        if (total_pages > 1) {
            paginationHTML += '<div class="pagination-buttons">';
            
            // 上一页
            paginationHTML += `
                <button class="pagination-btn" ${page <= 1 ? 'disabled' : ''} 
                        onclick="RequirementsListPage.goToPage(${page - 1})">
                    <i class="fas fa-chevron-left"></i> 上一页
                </button>
            `;
            
            // 页码按钮
            const startPage = Math.max(1, page - 2);
            const endPage = Math.min(total_pages, page + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `
                    <button class="pagination-btn ${i === page ? 'active' : ''}" 
                            onclick="RequirementsListPage.goToPage(${i})">
                        ${i}
                    </button>
                `;
            }
            
            // 下一页
            paginationHTML += `
                <button class="pagination-btn" ${page >= total_pages ? 'disabled' : ''} 
                        onclick="RequirementsListPage.goToPage(${page + 1})">
                    下一页 <i class="fas fa-chevron-right"></i>
                </button>
            `;
            
            paginationHTML += '</div>';
        }
        
        pagination.innerHTML = paginationHTML;
    },
    
    getStatusClass(status) {
        const statusMap = {
            '草稿': 'draft',
            '待分析': 'pending',
            '分析中': 'analyzing',
            '已分析': 'analyzed',
            '已完成': 'completed'
        };
        return statusMap[status] || 'draft';
    },
    
    toggleRequirementSelection(id, selected) {
        if (selected) {
            if (!this.selectedIds.includes(id)) {
                this.selectedIds.push(id);
            }
        } else {
            this.selectedIds = this.selectedIds.filter(selectedId => selectedId !== id);
        }
        
        this.updateBatchAnalyzeButton();
        this.updateSelectAllCheckbox();
    },
    
    toggleSelectAll(selectAll) {
        const checkboxes = document.querySelectorAll('.requirement-checkbox');
        
        if (selectAll) {
            checkboxes.forEach(checkbox => {
                const id = parseInt(checkbox.value);
                if (!this.selectedIds.includes(id)) {
                    this.selectedIds.push(id);
                }
                checkbox.checked = true;
            });
        } else {
            this.selectedIds = [];
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }
        
        this.updateBatchAnalyzeButton();
    },
    
    updateBatchAnalyzeButton() {
        const batchAnalyzeBtn = document.getElementById('batchAnalyzeBtn');
        if (batchAnalyzeBtn) {
            batchAnalyzeBtn.disabled = this.selectedIds.length === 0;
            batchAnalyzeBtn.innerHTML = `
                <i class="fas fa-brain"></i>
                提交AI分析 ${this.selectedIds.length > 0 ? `(${this.selectedIds.length})` : ''}
            `;
        }
    },
    
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.requirement-checkbox');
        
        if (selectAllCheckbox && checkboxes.length > 0) {
            const checkedCount = this.selectedIds.length;
            const totalCount = checkboxes.length;
            
            selectAllCheckbox.checked = checkedCount === totalCount;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
        }
    },
    
    async submitBatchAnalysis() {
        if (this.selectedIds.length === 0) {
            Utils.warning('请先选择需要分析的需求');
            return;
        }
        
        if (this.selectedIds.length > 5) {
            Utils.warning('批量分析最多支持5个需求');
            return;
        }
        
        try {
            const result = await API.analyzeRequirements(this.selectedIds);
            Utils.success(`AI分析已提交，成功: ${result.success_count}，失败: ${result.failed_count}`);
            
            // 清空选择
            this.selectedIds = [];
            this.updateBatchAnalyzeButton();
            this.updateSelectAllCheckbox();
            
            // 刷新列表
            await this.loadRequirements();
            
        } catch (error) {
            console.error('批量分析失败:', error);
            Utils.error('批量分析提交失败');
        }
    },
    
    async editRequirement(id) {
        // 跳转到编辑页面
        window.PageManager.showPage('requirements-create');
        // 这里可以传递编辑的ID
        if (window.RequirementsCreatePage) {
            window.RequirementsCreatePage.loadForEdit(id);
        }
    },
    
    async analyzeRequirement(id) {
        try {
            const result = await API.analyzeRequirements([id]);
            Utils.success('AI分析已提交');
            await this.loadRequirements();
        } catch (error) {
            console.error('分析失败:', error);
            Utils.error('分析提交失败');
        }
    },
    
    async deleteRequirement(id) {
        if (!confirm('确定要删除这个需求吗？')) {
            return;
        }
        
        try {
            await API.deleteRequirement(id);
            Utils.success('需求删除成功');
            await this.loadRequirements();
        } catch (error) {
            console.error('删除失败:', error);
            Utils.error('删除需求失败');
        }
    },
    
    async applyFilters() {
        this.currentPage = 1;
        await this.loadRequirements();
    },
    
    async goToPage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            await this.loadRequirements();
        }
    }
};

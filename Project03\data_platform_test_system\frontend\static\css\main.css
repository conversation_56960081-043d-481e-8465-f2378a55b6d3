/* ==================== 全局样式重置 ==================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

/* ==================== 主布局容器 ==================== */
.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* ==================== 左侧导航栏 ==================== */
.sidebar {
    width: 260px;
    background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    transition: width 0.3s ease;
    z-index: 1000;
}

.sidebar.collapsed {
    width: 70px;
}

/* 侧边栏头部 */
.sidebar-header {
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.logo-icon {
    font-size: 28px;
    color: #4fc3f7;
}

.system-title {
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .system-title {
    opacity: 0;
    pointer-events: none;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 导航菜单 */
.nav-menu {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.nav-item, .nav-subitem {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-left: 3px solid transparent;
}

.nav-item:hover, .nav-subitem:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: #4fc3f7;
}

.nav-item.active, .nav-subitem.active {
    background-color: rgba(79, 195, 247, 0.2);
    border-left-color: #4fc3f7;
    color: #4fc3f7;
}

.nav-icon {
    width: 20px;
    font-size: 16px;
    margin-right: 12px;
    text-align: center;
}

.nav-text {
    flex: 1;
    white-space: nowrap;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    pointer-events: none;
}

.nav-arrow {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.nav-group-header.expanded .nav-arrow {
    transform: rotate(180deg);
}

/* 子菜单 */
.nav-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: rgba(0, 0, 0, 0.2);
}

.nav-submenu.expanded {
    max-height: 200px;
}

.nav-subitem {
    padding-left: 52px;
    font-size: 13px;
}

.sidebar.collapsed .nav-submenu {
    display: none;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.system-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.status-indicator {
    font-size: 8px;
    color: #4caf50;
}

.status-text {
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .status-text {
    opacity: 0;
    pointer-events: none;
}

/* ==================== 右侧主内容区域 ==================== */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 顶部导航栏 */
.content-header {
    height: 70px;
    background: white;
    border-bottom: 1px solid #e8eaed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    z-index: 100;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
}

.breadcrumb-item {
    position: relative;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: 8px;
    color: #ccc;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
}

/* 页面内容区域 */
.page-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    background-color: #f5f7fa;
}

.page {
    display: block;
}

.page.hidden {
    display: none;
}

/* ==================== 首页样式 ==================== */
.welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 32px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.welcome-banner h1 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 16px;
}

.welcome-banner p {
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.6;
}

/* 功能卡片 */
.feature-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.feature-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e8eaed;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4fc3f7, #29b6f6);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #4fc3f7;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.card-icon i {
    font-size: 24px;
    color: white;
}

.feature-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 16px;
}

.card-stats {
    color: #4fc3f7;
    font-weight: 600;
    font-size: 14px;
}

/* 系统状态面板 */
.status-panel {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaed;
}

.status-panel h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.status-item {
    text-align: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e8eaed;
}

.status-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.status-value.success {
    color: #4caf50;
}

.status-value.warning {
    color: #ff9800;
}

.status-value.error {
    color: #f44336;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .sidebar {
        width: 70px;
    }
    
    .sidebar .nav-text,
    .sidebar .system-title,
    .sidebar .status-text {
        display: none;
    }
    
    .nav-submenu {
        display: none;
    }
    
    .content-header {
        padding: 0 16px;
    }
    
    .page-content {
        padding: 16px;
    }
    
    .feature-cards {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .welcome-banner {
        padding: 24px;
    }
    
    .welcome-banner h1 {
        font-size: 24px;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
}

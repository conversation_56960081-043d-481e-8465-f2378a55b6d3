#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库配置
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
backend_path = project_root / "Project03" / "data_platform_test_system" / "backend"
sys.path.insert(0, str(backend_path))

print("🔍 测试数据库配置...")

try:
    from app.core.config import settings
    
    print("✅ 配置加载成功")
    print(f"   数据库服务器: {settings.MYSQL_SERVER}")
    print(f"   数据库端口: {settings.MYSQL_PORT}")
    print(f"   数据库名称: {settings.MYSQL_DB}")
    print(f"   数据库用户: {settings.MYSQL_USER}")
    print(f"   数据库密码: {'*' * len(str(settings.MYSQL_PASSWORD))}")
    
    # 显示完整的数据库连接字符串（隐藏密码）
    db_url = settings.assemble_db_connection()
    # 隐藏密码部分
    import re
    safe_url = re.sub(r'://([^:]+):([^@]+)@', r'://\1:***@', db_url)
    print(f"   连接字符串: {safe_url}")
    
    print("\n🔗 测试数据库连接...")
    
    # 测试数据库连接
    import asyncio
    from app.core.database import db_manager
    
    async def test_connection():
        try:
            result = await db_manager.check_connection()
            if result:
                print("✅ 数据库连接成功！")
                return True
            else:
                print("❌ 数据库连接失败")
                return False
        except Exception as e:
            print(f"❌ 数据库连接异常: {e}")
            return False
    
    # 运行异步测试
    success = asyncio.run(test_connection())
    
    if success:
        print("\n🎉 数据库配置完全正确！")
    else:
        print("\n⚠️ 数据库配置可能有问题，请检查：")
        print("   1. MySQL服务是否启动")
        print("   2. 数据库用户名和密码是否正确")
        print("   3. 数据库是否存在")
        print("   4. 用户是否有访问权限")
        
        print("\n💡 建议操作：")
        print("   1. 启动MySQL服务")
        print("   2. 创建数据库: CREATE DATABASE data_platform_test;")
        print("   3. 检查用户权限: GRANT ALL PRIVILEGES ON data_platform_test.* TO 'root'@'localhost';")

except Exception as e:
    print(f"❌ 配置测试失败: {e}")
    import traceback
    traceback.print_exc()

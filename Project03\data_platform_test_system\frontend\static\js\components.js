/**
 * 通用组件JavaScript
 * 提供可复用的UI组件和功能
 */

// ==================== 模态框组件 ====================
window.Modal = {
    show(title, content, options = {}) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${Utils.escapeHtml(title)}</h3>
                    <button class="modal-close" onclick="Modal.close(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                ${options.footer ? `<div class="modal-footer">${options.footer}</div>` : ''}
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.close(modal);
            }
        });
        
        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                this.close(modal);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
        
        return modal;
    },
    
    close(element) {
        const modal = element.closest ? element.closest('.modal-overlay') : element;
        if (modal && modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    },
    
    confirm(title, message, onConfirm, onCancel) {
        const footer = `
            <button class="btn btn-outline" onclick="Modal.close(this); ${onCancel ? onCancel.name + '()' : ''}">
                取消
            </button>
            <button class="btn btn-danger" onclick="Modal.close(this); ${onConfirm.name}()">
                确认
            </button>
        `;
        
        return this.show(title, `<p>${Utils.escapeHtml(message)}</p>`, { footer });
    }
};

// ==================== 加载指示器组件 ====================
window.Loading = {
    show(message = '处理中...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            const messageEl = overlay.querySelector('p');
            if (messageEl) {
                messageEl.textContent = message;
            }
            overlay.classList.add('show');
        }
    },
    
    hide() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }
};

// ==================== 表格组件 ====================
window.DataTable = {
    create(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return null;
        
        const table = {
            container,
            options: {
                columns: [],
                data: [],
                pagination: true,
                pageSize: 20,
                ...options
            },
            currentPage: 1,
            totalPages: 0,
            
            render() {
                this.renderTable();
                if (this.options.pagination) {
                    this.renderPagination();
                }
            },
            
            renderTable() {
                const tableHTML = `
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    ${this.options.columns.map(col => 
                                        `<th ${col.width ? `width="${col.width}"` : ''}>${col.title}</th>`
                                    ).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${this.renderRows()}
                            </tbody>
                        </table>
                    </div>
                `;
                
                this.container.innerHTML = tableHTML;
            },
            
            renderRows() {
                if (this.options.data.length === 0) {
                    return `
                        <tr>
                            <td colspan="${this.options.columns.length}" style="text-align: center; padding: 40px; color: #666;">
                                暂无数据
                            </td>
                        </tr>
                    `;
                }
                
                return this.options.data.map(row => `
                    <tr>
                        ${this.options.columns.map(col => `
                            <td>${col.render ? col.render(row[col.key], row) : Utils.escapeHtml(row[col.key] || '')}</td>
                        `).join('')}
                    </tr>
                `).join('');
            },
            
            renderPagination() {
                // 分页逻辑
            },
            
            updateData(data) {
                this.options.data = data;
                this.render();
            }
        };
        
        table.render();
        return table;
    }
};

// ==================== 表单验证组件 ====================
window.FormValidator = {
    rules: {
        required: (value) => value && value.toString().trim().length > 0,
        email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
        phone: (value) => /^1[3-9]\d{9}$/.test(value),
        minLength: (min) => (value) => value && value.length >= min,
        maxLength: (max) => (value) => !value || value.length <= max
    },
    
    validate(formId, validationRules) {
        const form = document.getElementById(formId);
        if (!form) return { valid: false, errors: ['表单不存在'] };
        
        const errors = [];
        const formData = new FormData(form);
        
        Object.keys(validationRules).forEach(fieldName => {
            const value = formData.get(fieldName);
            const rules = validationRules[fieldName];
            
            rules.forEach(rule => {
                if (typeof rule === 'string') {
                    // 简单规则
                    if (!this.rules[rule](value)) {
                        errors.push(this.getErrorMessage(fieldName, rule));
                    }
                } else if (typeof rule === 'object') {
                    // 复杂规则
                    const { type, param, message } = rule;
                    if (!this.rules[type](param)(value)) {
                        errors.push(message || this.getErrorMessage(fieldName, type));
                    }
                }
            });
        });
        
        return {
            valid: errors.length === 0,
            errors
        };
    },
    
    getErrorMessage(fieldName, ruleType) {
        const messages = {
            required: `${fieldName}不能为空`,
            email: `${fieldName}格式不正确`,
            phone: `${fieldName}格式不正确`,
            minLength: `${fieldName}长度不足`,
            maxLength: `${fieldName}长度超限`
        };
        
        return messages[ruleType] || `${fieldName}验证失败`;
    }
};

// ==================== 文件上传组件 ====================
window.FileUploader = {
    create(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) return null;
        
        const uploader = {
            container,
            options: {
                multiple: true,
                maxSize: 10 * 1024 * 1024, // 10MB
                allowedTypes: ['image/*', 'application/pdf', 'text/*'],
                onUpload: null,
                onError: null,
                ...options
            },
            files: [],
            
            init() {
                this.render();
                this.bindEvents();
            },
            
            render() {
                this.container.innerHTML = `
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-placeholder">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>点击或拖拽文件到此处上传</p>
                            <p class="upload-hint">支持多种格式，最大 ${Utils.formatFileSize(this.options.maxSize)}</p>
                        </div>
                        <input type="file" id="fileInput" ${this.options.multiple ? 'multiple' : ''} 
                               accept="${this.options.allowedTypes.join(',')}" style="display: none;">
                    </div>
                    <div class="uploaded-files" id="uploadedFiles"></div>
                `;
            },
            
            bindEvents() {
                const uploadArea = this.container.querySelector('#uploadArea');
                const fileInput = this.container.querySelector('#fileInput');
                
                uploadArea.addEventListener('click', () => fileInput.click());
                
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });
                
                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                });
                
                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    this.handleFiles(e.dataTransfer.files);
                });
                
                fileInput.addEventListener('change', (e) => {
                    this.handleFiles(e.target.files);
                });
            },
            
            handleFiles(files) {
                Array.from(files).forEach(file => {
                    if (this.validateFile(file)) {
                        this.addFile(file);
                    }
                });
            },
            
            validateFile(file) {
                if (file.size > this.options.maxSize) {
                    if (this.options.onError) {
                        this.options.onError(`文件 ${file.name} 超过大小限制`);
                    }
                    return false;
                }
                
                return true;
            },
            
            addFile(file) {
                this.files.push(file);
                this.renderFiles();
                
                if (this.options.onUpload) {
                    this.options.onUpload(file);
                }
            },
            
            removeFile(index) {
                this.files.splice(index, 1);
                this.renderFiles();
            },
            
            renderFiles() {
                const container = this.container.querySelector('#uploadedFiles');
                container.innerHTML = this.files.map((file, index) => `
                    <div class="uploaded-file">
                        <div class="file-info">
                            <i class="file-icon ${Utils.getFileIcon(file.name)}"></i>
                            <div>
                                <div class="file-name">${Utils.escapeHtml(file.name)}</div>
                                <div class="file-size">${Utils.formatFileSize(file.size)}</div>
                            </div>
                        </div>
                        <span class="remove-file" onclick="this.closest('.uploaded-files').uploader.removeFile(${index})">
                            <i class="fas fa-times"></i>
                        </span>
                    </div>
                `).join('');
                
                // 保存引用以便删除文件
                container.uploader = this;
            }
        };
        
        uploader.init();
        return uploader;
    }
};

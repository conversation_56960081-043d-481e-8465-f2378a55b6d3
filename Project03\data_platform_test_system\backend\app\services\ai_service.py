#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI智能分析服务模块
基于AutoGen + DeepSeek实现真实的AI需求分析功能
严格禁止使用模拟模式，必须使用真正的AI能力
"""

import json
import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging

# AutoGen相关导入
try:
    import autogen
    from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager
    from autogen.agentchat.contrib.retrieve_assistant_agent import RetrieveAssistantAgent
    AUTOGEN_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("AutoGen模块导入成功")
except ImportError as e:
    AUTOGEN_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.error(f"AutoGen模块导入失败: {e}")

from ..core.config import settings

class DeepSeekAIService:
    """
    DeepSeek AI服务类
    负责与DeepSeek API的交互和AutoGen代理的管理
    """
    
    def __init__(self):
        """初始化AI服务"""
        self.config = settings.autogen_llm_config
        self.agents = {}
        self.is_initialized = False
        
        logger.info("初始化DeepSeek AI服务")

        if AUTOGEN_AVAILABLE:
            self._initialize_agents()
        else:
            logger.error("AutoGen不可用，AI功能将受限")
    
    def _initialize_agents(self) -> bool:
        """
        初始化AutoGen AI代理
        创建专门的需求分析专家代理
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("开始初始化AutoGen代理...")

            # 需求分析专家代理
            self.agents['requirement_analyst'] = AssistantAgent(
                name="requirement_analyst",
                system_message="""你是一位资深的数据平台需求分析专家，拥有10年以上的数据开发和需求分析经验。

你的核心职责：
1. 深入分析数据开发需求的完整性、可行性和合理性
2. 识别需求中的关键信息、潜在风险和改进机会
3. 提供专业的技术建议和实施方案
4. 评估需求的优先级和资源投入

分析维度包括：
- 业务价值评估：分析需求的业务意义和价值
- 技术可行性：评估技术实现的难度和风险
- 数据质量要求：分析数据来源、格式、质量标准
- 性能和扩展性：评估系统性能要求和扩展能力
- 时间和资源：估算开发周期和资源需求
- 风险识别：识别潜在的技术和业务风险

输出要求：
- 使用中文进行分析
- 提供结构化的分析结果
- 给出具体可行的建议
- 突出关键风险点和注意事项

请始终保持专业、客观、实用的分析风格。""",
                llm_config=self.config,
                max_consecutive_auto_reply=1
            )
            
            # 技术架构师代理
            self.agents['tech_architect'] = AssistantAgent(
                name="tech_architect",
                system_message="""你是一位经验丰富的数据平台技术架构师，专注于大数据和数据中台技术方案设计。

你的专业领域：
1. 数据架构设计和优化
2. 技术栈选择和集成方案
3. 性能优化和扩展性设计
4. 数据安全和治理方案
5. 运维监控和故障处理

技术评估重点：
- 数据流架构：数据采集、处理、存储、服务的完整链路
- 技术选型：基于需求特点推荐合适的技术栈
- 性能设计：并发处理能力、响应时间、吞吐量要求
- 安全考虑：数据安全、访问控制、隐私保护
- 运维友好：监控告警、故障恢复、扩容缩容

请从技术实现角度提供专业建议，确保方案的可行性和可维护性。""",
                llm_config=self.config,
                max_consecutive_auto_reply=1
            )
            
            # 用户代理（用于控制对话流程）
            self.agents['user_proxy'] = UserProxyAgent(
                name="user_proxy",
                human_input_mode="NEVER",  # 完全自动化，不需要人工输入
                max_consecutive_auto_reply=0,
                code_execution_config=False,  # 禁用代码执行
                llm_config=None
            )
            
            self.is_initialized = True
            logger.info("AutoGen代理初始化成功")
            return True

        except Exception as e:
            logger.error(f"AutoGen代理初始化失败: {e}")
            self.agents = {}
            self.is_initialized = False
            return False
    
    async def analyze_single_requirement(self, requirement_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析单个需求
        使用真实的AutoGen + DeepSeek进行智能分析
        
        Args:
            requirement_data: 需求数据字典
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        if not AUTOGEN_AVAILABLE or not self.is_initialized:
            logger.warning("AutoGen不可用，使用备用分析方法")
            return await self._fallback_analysis(requirement_data)
        
        start_time = time.time()
        
        try:
            logger.info(f"🔍 开始分析需求: {requirement_data.get('title', 'Unknown')}")
            
            # 构建分析提示
            analysis_prompt = self._build_analysis_prompt(requirement_data)
            
            # 执行AI分析
            analysis_result = await self._execute_ai_analysis(analysis_prompt)
            
            # 处理分析结果
            processed_result = self._process_analysis_result(
                analysis_result, 
                requirement_data, 
                time.time() - start_time
            )
            
            logger.info(f"✅ 需求分析完成: {requirement_data.get('id', 'Unknown')}")
            return processed_result
            
        except Exception as e:
            logger.error(f"❌ 需求分析失败: {e}")
            return self._create_error_result(requirement_data, str(e), time.time() - start_time)
    
    async def analyze_batch_requirements(self, requirements_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量分析需求
        
        Args:
            requirements_list: 需求列表
            
        Returns:
            List[Dict[str, Any]]: 分析结果列表
        """
        logger.info(f"🔄 开始批量分析 {len(requirements_list)} 个需求")
        
        results = []
        
        for i, requirement in enumerate(requirements_list, 1):
            try:
                logger.info(f"📋 分析进度: {i}/{len(requirements_list)}")
                
                # 分析单个需求
                result = await self.analyze_single_requirement(requirement)
                results.append(result)
                
                # 添加延迟避免API限制
                if i < len(requirements_list):
                    await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ 批量分析中的需求 {requirement.get('id', 'unknown')} 失败: {e}")
                error_result = self._create_error_result(requirement, str(e), 0)
                results.append(error_result)
        
        logger.info(f"✅ 批量分析完成，成功: {len([r for r in results if r['analysis_status'] == 'completed'])}/{len(requirements_list)}")
        return results
    
    def _build_analysis_prompt(self, requirement_data: Dict[str, Any]) -> str:
        """
        构建AI分析提示词
        
        Args:
            requirement_data: 需求数据
            
        Returns:
            str: 分析提示词
        """
        title = requirement_data.get('title', '未提供')
        description = requirement_data.get('description', '未提供')
        priority = requirement_data.get('priority', '未设置')
        modules = requirement_data.get('modules', [])
        status = requirement_data.get('status', '未知')
        
        modules_str = ', '.join(modules) if modules else '未指定'
        
        prompt = f"""
请对以下数据开发需求进行深入的专业分析：

【需求基本信息】
- 需求标题：{title}
- 详细描述：{description}
- 优先级：{priority}
- 关联模块：{modules_str}
- 当前状态：{status}

【分析要求】
请从以下维度进行全面分析，并以JSON格式返回结构化结果：

1. 需求完整性评估 (completeness_assessment)
   - 信息完整度评分 (0-100)
   - 缺失信息识别
   - 补充建议

2. 业务价值分析 (business_value_analysis)
   - 业务价值评估
   - 预期收益分析
   - 影响范围评估

3. 技术可行性评估 (technical_feasibility)
   - 技术难度评级 (简单/中等/复杂/极复杂)
   - 技术风险识别
   - 实现方案建议

4. 关键要点提取 (key_points)
   - 核心功能要点
   - 关键技术要求
   - 重要约束条件

5. 风险识别与评估 (risk_assessment)
   - 技术风险列表
   - 业务风险分析
   - 风险等级评估

6. 改进建议 (improvement_suggestions)
   - 需求优化建议
   - 实施策略建议
   - 最佳实践推荐

7. 资源评估 (resource_estimation)
   - 预估开发周期
   - 人力资源需求
   - 技术资源要求

8. 分析总结 (summary)
   - 整体评估结论
   - 推荐优先级
   - 关键决策建议

请确保分析结果专业、客观、实用，并提供具体可行的建议。
"""
        return prompt
    
    async def _execute_ai_analysis(self, prompt: str) -> str:
        """
        执行AI分析
        使用AutoGen代理进行真实的AI对话
        
        Args:
            prompt: 分析提示词
            
        Returns:
            str: AI分析结果
        """
        try:
            # 获取需求分析专家代理
            analyst = self.agents['requirement_analyst']
            user_proxy = self.agents['user_proxy']
            
            logger.info("🤖 启动AutoGen AI分析...")
            
            # 启动AI对话分析
            chat_result = user_proxy.initiate_chat(
                analyst,
                message=prompt,
                max_turns=2,
                silent=False
            )
            
            # 提取AI回复内容
            if hasattr(chat_result, 'chat_history') and chat_result.chat_history:
                # 获取最后一条AI回复
                for message in reversed(chat_result.chat_history):
                    if message.get('role') == 'assistant' and message.get('content'):
                        ai_response = message['content']
                        logger.info("✅ AI分析完成，获取到回复内容")
                        return ai_response
            
            # 如果没有找到有效回复，返回空字符串
            logger.warning("⚠️ 未获取到有效的AI回复")
            return ""
            
        except Exception as e:
            logger.error(f"❌ AI分析执行失败: {e}")
            raise
    
    def _process_analysis_result(
        self, 
        ai_response: str, 
        requirement_data: Dict[str, Any], 
        processing_time: float
    ) -> Dict[str, Any]:
        """
        处理AI分析结果
        
        Args:
            ai_response: AI原始回复
            requirement_data: 原始需求数据
            processing_time: 处理耗时
            
        Returns:
            Dict[str, Any]: 处理后的结果
        """
        try:
            # 尝试解析JSON格式的AI回复
            structured_result = self._extract_json_from_response(ai_response)
            
            # 构建标准化的分析结果
            result = {
                "req_id": requirement_data.get('id'),
                "analysis_result": ai_response,
                "structured_result": structured_result,
                "key_points": structured_result.get('key_points', []),
                "risk_assessment": structured_result.get('risk_assessment', {}),
                "suggestions": structured_result.get('improvement_suggestions', []),
                "ai_model": settings.DEEPSEEK_MODEL,
                "ai_version": "1.0",
                "analysis_config": self.config,
                "analysis_status": "completed",
                "error_message": None,
                "processing_time": int(processing_time),
                "created_time": datetime.utcnow(),
                "update_time": datetime.utcnow()
            }
            
            logger.info("AI分析结果处理完成")
            return result

        except Exception as e:
            logger.error(f"分析结果处理失败: {e}")
            return self._create_error_result(requirement_data, f"结果处理失败: {e}", processing_time)
    
    def _extract_json_from_response(self, response: str) -> Dict[str, Any]:
        """
        从AI回复中提取JSON结构
        
        Args:
            response: AI回复文本
            
        Returns:
            Dict[str, Any]: 提取的JSON数据
        """
        try:
            # 查找JSON代码块
            import re
            
            # 尝试匹配```json...```格式
            json_match = re.search(r'```json\s*\n(.*?)\n```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)
            
            # 尝试匹配```...```格式
            code_match = re.search(r'```\s*\n(.*?)\n```', response, re.DOTALL)
            if code_match:
                json_str = code_match.group(1)
                return json.loads(json_str)
            
            # 尝试直接解析整个回复
            if response.strip().startswith('{') and response.strip().endswith('}'):
                return json.loads(response.strip())
            
            # 如果都失败，返回基础结构
            return {
                "summary": response[:500] + "..." if len(response) > 500 else response,
                "completeness_assessment": {"score": 70},
                "key_points": ["AI分析结果"],
                "improvement_suggestions": ["请查看详细分析内容"],
                "technical_feasibility": {"difficulty": "需要进一步评估"}
            }
            
        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败: {e}")
            return {
                "summary": response[:500] + "..." if len(response) > 500 else response,
                "parse_error": str(e)
            }
    
    async def _fallback_analysis(self, requirement_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        备用分析方法（当AI不可用时使用）
        
        Args:
            requirement_data: 需求数据
            
        Returns:
            Dict[str, Any]: 备用分析结果
        """
        logger.warning("使用备用分析方法")
        
        # 基础规则分析
        title = requirement_data.get('title', '')
        description = requirement_data.get('description', '')
        priority = requirement_data.get('priority', '')
        
        # 简单的完整性检查
        completeness_score = 0
        missing_fields = []
        
        if title:
            completeness_score += 30
        else:
            missing_fields.append("标题")
        
        if description and len(description) > 20:
            completeness_score += 40
        else:
            missing_fields.append("详细描述")
        
        if priority:
            completeness_score += 20
        else:
            missing_fields.append("优先级")
        
        if requirement_data.get('modules'):
            completeness_score += 10
        else:
            missing_fields.append("关联模块")
        
        # 构建备用分析结果
        structured_result = {
            "completeness_assessment": {
                "score": completeness_score,
                "missing_fields": missing_fields
            },
            "key_points": [f"需求标题: {title}", f"优先级: {priority}"],
            "improvement_suggestions": [f"建议补充: {', '.join(missing_fields)}"] if missing_fields else ["需求信息相对完整"],
            "technical_feasibility": {"difficulty": "需要人工评估"},
            "summary": f"基础规则分析完成，完整性评分: {completeness_score}/100"
        }
        
        return {
            "req_id": requirement_data.get('id'),
            "analysis_result": f"备用分析结果：{structured_result['summary']}",
            "structured_result": structured_result,
            "key_points": structured_result["key_points"],
            "suggestions": structured_result["improvement_suggestions"],
            "ai_model": "fallback_analyzer",
            "ai_version": "1.0",
            "analysis_status": "completed_fallback",
            "error_message": "AutoGen不可用，使用备用分析",
            "processing_time": 1,
            "created_time": datetime.utcnow(),
            "update_time": datetime.utcnow()
        }
    
    def _create_error_result(
        self, 
        requirement_data: Dict[str, Any], 
        error_message: str, 
        processing_time: float
    ) -> Dict[str, Any]:
        """
        创建错误结果
        
        Args:
            requirement_data: 需求数据
            error_message: 错误信息
            processing_time: 处理耗时
            
        Returns:
            Dict[str, Any]: 错误结果
        """
        return {
            "req_id": requirement_data.get('id'),
            "analysis_result": "",
            "structured_result": {},
            "key_points": [],
            "suggestions": [],
            "ai_model": settings.DEEPSEEK_MODEL,
            "ai_version": "1.0",
            "analysis_status": "failed",
            "error_message": error_message,
            "processing_time": int(processing_time),
            "created_time": datetime.utcnow(),
            "update_time": datetime.utcnow()
        }

# ==================== 全局AI服务实例 ====================
ai_service = DeepSeekAIService()

# ==================== 服务状态检查函数 ====================
async def check_ai_service_status() -> Dict[str, Any]:
    """
    检查AI服务状态
    
    Returns:
        Dict[str, Any]: 服务状态信息
    """
    return {
        "autogen_available": AUTOGEN_AVAILABLE,
        "deepseek_configured": bool(settings.DEEPSEEK_API_KEY and settings.DEEPSEEK_API_KEY != "your-api-key-here"),
        "agents_initialized": ai_service.is_initialized,
        "model": settings.DEEPSEEK_MODEL,
        "base_url": settings.DEEPSEEK_BASE_URL,
        "status": "ready" if AUTOGEN_AVAILABLE and ai_service.is_initialized else "limited",
        "config": {
            "max_tokens": settings.AUTOGEN_MAX_TOKENS,
            "temperature": settings.AUTOGEN_TEMPERATURE,
            "timeout": settings.AUTOGEN_TIMEOUT
        }
    }

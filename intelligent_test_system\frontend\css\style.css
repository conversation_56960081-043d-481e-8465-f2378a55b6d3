/* 数据中台智能助手 - 主样式文件 */

:root {
    /* Gemini风格配色 */
    --primary-blue: #1a73e8;
    --primary-blue-hover: #1557b0;
    --secondary-blue: #e8f0fe;
    --success-green: #34a853;
    --warning-orange: #fbbc04;
    --error-red: #ea4335;
    
    /* 中性色 */
    --bg-white: #ffffff;
    --bg-light: #f8f9fa;
    --bg-gray: #f1f3f4;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --text-muted: #9aa0a6;
    --border-light: #dadce0;
    --border-medium: #c4c7c5;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-size-base: 14px;
    --font-size-large: 16px;
    --font-size-small: 12px;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* 圆角 */
    --border-radius: 8px;
    --border-radius-large: 12px;
    
    /* 阴影 */
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-light);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 通用组件样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: var(--spacing-xs);
}

.btn-primary {
    background-color: var(--primary-blue);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-blue-hover);
}

.btn-secondary {
    background-color: white;
    color: var(--primary-blue);
    border-color: var(--border-light);
}

.btn-secondary:hover {
    background-color: var(--secondary-blue);
}

.btn-success {
    background-color: var(--success-green);
    color: white;
}

.btn-warning {
    background-color: var(--warning-orange);
    color: white;
}

.btn-danger {
    background-color: var(--error-red);
    color: white;
}

/* 卡片样式 */
.card {
    background: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-gray);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background: var(--bg-gray);
}

/* 表单样式 */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

/* 通知样式 */
.notification {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    max-width: 400px;
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    z-index: 1000;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-success {
    background: var(--success-green);
    color: white;
}

.notification-warning {
    background: var(--warning-orange);
    color: white;
}

.notification-error {
    background: var(--error-red);
    color: white;
}

.notification-info {
    background: var(--primary-blue);
    color: white;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-radius: 50%;
    border-top-color: var(--primary-blue);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 进度条 */
.progress {
    width: 100%;
    height: 8px;
    background: var(--bg-gray);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--primary-blue);
    transition: width 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --spacing-lg: 16px;
        --spacing-xl: 24px;
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .notification {
        left: var(--spacing-md);
        right: var(--spacing-md);
        max-width: none;
    }
}

/* AI日志样式 */
.log-entry {
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-left: 4px solid var(--primary-blue);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.log-entry:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left-color: var(--primary-blue-hover);
}

.log-timestamp {
    color: var(--text-muted);
    font-size: var(--font-size-small);
    font-family: monospace;
}

.log-agent {
    color: var(--primary-blue);
    font-weight: 500;
    font-size: var(--font-size-small);
}

.log-details-toggle {
    cursor: pointer;
    color: var(--primary-blue);
    font-size: var(--font-size-small);
    margin-top: var(--spacing-xs);
    user-select: none;
}

.log-details-toggle:hover {
    color: var(--primary-blue-hover);
}

/* 不同类型日志的样式 */
.log-entry.log-info {
    border-left-color: #17a2b8;
    background: #f8fdff;
}

.log-entry.log-success {
    border-left-color: var(--success-green);
    background: #f8fff9;
}

.log-entry.log-error {
    border-left-color: var(--error-red);
    background: #fff8f8;
}

.log-entry.log-progress {
    border-left-color: #ffc107;
    background: #fffdf5;
}

.log-entry.log-prompt {
    border-left-color: #6f42c1;
    background: #faf8ff;
}

.log-entry.log-ai-response {
    border-left-color: #20c997;
    background: #f8fffc;
}

.log-details {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-md);
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-blue);
}

.log-details.hidden {
    display: none;
}

.details-content {
    white-space: pre-wrap;
    font-size: 11px;
    margin: 0;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    line-height: 1.4;
    color: #495057;
}

/* AI日志容器样式 */
.ai-log {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-large);
    padding: var(--spacing-lg);
    max-height: 500px;
    overflow-y: auto;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ai-log::-webkit-scrollbar {
    width: 8px;
}

.ai-log::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.ai-log::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.ai-log::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

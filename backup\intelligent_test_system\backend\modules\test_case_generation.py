"""
测试用例生成模块 - 负责AI测试用例生成功能
"""

import json
import uuid
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, AsyncGenerator
from fastapi import UploadFile
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ai_generator import AITestCaseGenerator


class TestCaseGenerationModule:
    """测试用例生成模块"""
    
    def __init__(self, ai_generator: AITestCaseGenerator):
        self.ai_generator = ai_generator
        self.active_tasks = {}
        self.test_cases_library = {}
    
    async def generate_test_cases(
        self,
        project_name: str,
        requirements: str,
        context_info: str = "",
        test_case_count: int = 5,
        files: List[UploadFile] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        生成测试用例
        
        Args:
            project_name: 项目名称
            requirements: 需求描述
            context_info: 技术上下文
            test_case_count: 测试用例数量
            files: 上传的文件列表
            
        Yields:
            Dict[str, Any]: 流式响应数据
        """
        task_id = str(uuid.uuid4())
        self.active_tasks[task_id] = {
            'status': 'processing',
            'start_time': datetime.now(),
            'project_name': project_name
        }
        
        try:
            # 发送开始信息
            yield {
                "type": "info",
                "message": f"🚀 开始AI测试用例生成...",
                "progress": 0,
                "task_id": task_id
            }
            
            # 处理上传的文件
            file_contents = []
            if files:
                yield {
                    "type": "progress",
                    "message": f"📁 处理上传文件 ({len(files)} 个)...",
                    "progress": 10
                }
                
                file_contents = await self._process_uploaded_files(files)
            
            # 需求分析阶段
            yield {
                "type": "progress",
                "message": "🔍 需求分析专家分析中...",
                "progress": 20,
                "agent": "requirement_analyst"
            }

            # 构建并显示需求分析提示词
            requirement_prompt = self._build_requirement_analysis_prompt(
                project_name, requirements, context_info, file_contents
            )

            yield {
                "type": "prompt",
                "message": "📝 需求分析专家提示词",
                "agent": "requirement_analyst",
                "prompt": requirement_prompt
            }

            requirement_analysis = await self._analyze_requirements(
                project_name, requirements, context_info, file_contents
            )

            yield {
                "type": "ai_response",
                "message": "🔍 需求分析专家AI响应",
                "agent": "requirement_analyst",
                "response": requirement_analysis
            }
            
            # 测试用例生成阶段
            yield {
                "type": "progress",
                "message": "🧪 数据测试专家生成测试用例中...",
                "progress": 60,
                "agent": "data_test_expert"
            }

            # 构建并显示测试用例生成提示词
            test_case_prompt = self._build_test_case_generation_prompt(requirement_analysis, test_case_count)

            yield {
                "type": "prompt",
                "message": "📝 数据测试专家提示词",
                "agent": "data_test_expert",
                "prompt": test_case_prompt
            }

            test_cases_response = await self._generate_test_cases_with_ai(
                requirement_analysis, test_case_count
            )

            yield {
                "type": "ai_response",
                "message": "🧪 数据测试专家AI响应",
                "agent": "data_test_expert",
                "response": test_cases_response
            }
            
            # 解析测试用例
            yield {
                "type": "progress",
                "message": "📊 解析测试用例...",
                "progress": 80
            }
            
            test_cases = self._parse_test_cases(test_cases_response)
            
            if not test_cases:
                yield {
                    "type": "error",
                    "message": "❌ 测试用例解析失败",
                    "progress": 80
                }
                return
            
            # 构建完整结果
            result = {
                "task_id": task_id,
                "project_name": project_name,
                "requirements": requirements,
                "context_info": context_info,
                "test_cases": test_cases,
                "test_case_count": len(test_cases),
                "ai_framework": "AutoGen + DeepSeek",
                "generation_time": datetime.now().isoformat(),
                "file_count": len(files) if files else 0
            }
            
            # 保存到测试用例库
            self.test_cases_library[task_id] = result
            
            # 保存任务结果
            self.active_tasks[task_id].update({
                'status': 'completed',
                'result': result,
                'end_time': datetime.now()
            })
            
            yield {
                "type": "progress",
                "message": "✅ 测试用例生成完成",
                "progress": 100
            }
            
            yield {
                "type": "complete",
                "message": f"🎉 成功生成 {len(test_cases)} 个测试用例！",
                "progress": 100,
                "result": result
            }
            
        except Exception as e:
            print(f"❌ 测试用例生成流程失败: {str(e)}")
            import traceback
            traceback.print_exc()

            yield {
                "type": "error",
                "message": f"生成失败: {str(e)}",
                "progress": 0,
                "error_details": str(e)
            }
        finally:
            # 清理任务状态
            if task_id in self.active_tasks:
                if self.active_tasks[task_id]['status'] == 'processing':
                    self.active_tasks[task_id]['status'] = 'failed'
    
    async def _process_uploaded_files(self, files: List[UploadFile]) -> List[Dict[str, Any]]:
        """处理上传的文件"""
        file_contents = []
        
        for file in files:
            try:
                content = await file.read()
                
                # 检查是否是图片文件
                if file.content_type and file.content_type.startswith('image/'):
                    # 图片文件转换为base64
                    import base64
                    base64_content = base64.b64encode(content).decode('utf-8')
                    file_contents.append({
                        "filename": file.filename,
                        "type": "image",
                        "content": base64_content,
                        "mime_type": file.content_type
                    })
                else:
                    # 其他文件类型暂时只记录文件名和大小
                    file_contents.append({
                        "filename": file.filename,
                        "type": "document",
                        "size": len(content),
                        "mime_type": file.content_type
                    })
                    
            except Exception as e:
                print(f"处理文件 {file.filename} 失败: {e}")
                continue
        
        return file_contents
    
    async def _analyze_requirements(
        self,
        project_name: str,
        requirements: str,
        context_info: str,
        file_contents: List[Dict[str, Any]]
    ) -> str:
        """需求分析 - 使用真实AutoGen + DeepSeek"""
        try:
            # 构建需求分析提示词
            prompt = self._build_requirement_analysis_prompt(
                project_name, requirements, context_info, file_contents
            )

            print(f"🔍 发送需求分析提示词到DeepSeek: {prompt[:200]}...")

            # 检查AI代理是否可用
            if not hasattr(self.ai_generator, 'requirement_analyst') or self.ai_generator.requirement_analyst is None:
                raise Exception("需求分析专家AI代理未初始化")

            # 使用AutoGen的generate_reply方法调用DeepSeek
            response = await asyncio.to_thread(
                self.ai_generator.requirement_analyst.generate_reply,
                messages=[{"role": "user", "content": prompt}]
            )

            print(f"✅ 收到DeepSeek响应: {str(response)[:200]}...")

            # 处理响应
            if hasattr(response, 'content'):
                return response.content
            elif isinstance(response, str):
                return response
            else:
                return str(response)

        except Exception as e:
            print(f"❌ 需求分析失败: {str(e)}")
            raise Exception(f"需求分析失败 (AutoGen + DeepSeek): {str(e)}")
    
    async def _generate_test_cases_with_ai(self, requirement_analysis: str, test_case_count: int) -> str:
        """使用真实AutoGen + DeepSeek生成测试用例"""
        try:
            # 构建测试用例生成提示词
            prompt = self._build_test_case_generation_prompt(requirement_analysis, test_case_count)

            print(f"🧪 发送测试用例生成提示词到DeepSeek: {prompt[:200]}...")

            # 检查AI代理是否可用
            if not hasattr(self.ai_generator, 'data_test_expert') or self.ai_generator.data_test_expert is None:
                raise Exception("数据测试专家AI代理未初始化")

            # 使用AutoGen的generate_reply方法调用DeepSeek
            response = await asyncio.to_thread(
                self.ai_generator.data_test_expert.generate_reply,
                messages=[{"role": "user", "content": prompt}]
            )

            print(f"✅ 收到DeepSeek响应: {str(response)[:200]}...")

            # 处理响应
            if hasattr(response, 'content'):
                return response.content
            elif isinstance(response, str):
                return response
            else:
                return str(response)

        except Exception as e:
            print(f"❌ 测试用例生成失败: {str(e)}")
            raise Exception(f"测试用例生成失败 (AutoGen + DeepSeek): {str(e)}")
    
    def _build_requirement_analysis_prompt(
        self, 
        project_name: str, 
        requirements: str, 
        context_info: str, 
        file_contents: List[Dict[str, Any]]
    ) -> str:
        """构建需求分析提示词"""
        
        # 获取基础提示词
        from prompts_config import REQUIREMENT_ANALYST_PROMPT
        base_prompt = REQUIREMENT_ANALYST_PROMPT
        
        # 构建项目信息
        project_info = f"""
## 项目信息
**项目名称**: {project_name}

**需求描述**:
{requirements}
"""
        
        if context_info:
            project_info += f"""
**技术上下文**:
{context_info}
"""
        
        # 构建文件信息
        file_info = ""
        if file_contents:
            file_info = "\n## 上传文件信息\n"
            for i, file_data in enumerate(file_contents, 1):
                file_info += f"{i}. **{file_data['filename']}** ({file_data['type']})\n"
        
        # 组合完整提示词
        full_prompt = f"{base_prompt}\n{project_info}\n{file_info}"
        
        return full_prompt
    
    def _build_test_case_generation_prompt(self, requirement_analysis: str, test_case_count: int) -> str:
        """构建测试用例生成提示词"""
        
        # 获取基础提示词
        from prompts_config import DATA_TEST_EXPERT_PROMPT
        base_prompt = DATA_TEST_EXPERT_PROMPT
        
        # 添加需求分析结果和数量要求
        analysis_info = f"""
## 需求分析结果
{requirement_analysis}

## 生成要求
请根据上述需求分析，生成 {test_case_count} 个高质量的数据测试用例。
"""
        
        # 组合完整提示词
        full_prompt = f"{base_prompt}\n{analysis_info}"
        
        return full_prompt
    
    def _parse_test_cases(self, ai_response: str) -> List[Dict[str, Any]]:
        """解析AI响应中的测试用例"""
        try:
            print(f"🔍 开始解析AI响应，响应长度: {len(ai_response)}")
            print(f"📝 AI响应前500字符: {ai_response[:500]}...")

            # 尝试从AI响应中提取JSON
            import re

            # JSON提取策略（这些是解析模式，不是模拟模式）
            json_patterns = [
                r'```json\s*(\[[\s\S]*?\])\s*```',  # 解析模式0: 标准JSON数组代码块
                r'```\s*(\[[\s\S]*?\])\s*```',      # 解析模式1: 普通数组代码块
                r'(\[[\s\S]*\])',                   # 解析模式2: 直接JSON数组
                r'```json\s*(\{[\s\S]*\})\s*```',  # 解析模式3: 标准JSON对象代码块
                r'```\s*(\{[\s\S]*\})\s*```',      # 解析模式4: 普通对象代码块
            ]

            pattern_names = [
                "标准JSON数组代码块", "普通数组代码块", "直接JSON数组",
                "标准JSON对象代码块", "普通对象代码块"
            ]

            json_str = None
            used_pattern = None
            for i, pattern in enumerate(json_patterns):
                match = re.search(pattern, ai_response, re.DOTALL)
                if match:
                    json_str = match.group(1)
                    used_pattern = i
                    print(f"✅ 使用JSON解析模式 {i} ({pattern_names[i]}) 从真实AI响应中提取到JSON: {json_str[:200]}...")
                    break

            if json_str:
                try:
                    parsed_data = json.loads(json_str)

                    # 如果是单个对象，转换为列表
                    if isinstance(parsed_data, dict):
                        parsed_data = [parsed_data]

                    if isinstance(parsed_data, list):
                        print(f"✅ 成功解析 {len(parsed_data)} 个测试用例")
                        return parsed_data

                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析错误: {e}")

            # 如果JSON解析失败，抛出错误，不使用模板
            print("❌ JSON解析失败，无法从AI响应中提取测试用例")
            print(f"AI响应内容: {ai_response}")
            raise Exception("AI响应格式不正确，无法解析测试用例。请检查AI提示词或重试。")

        except Exception as e:
            print(f"❌ 解析测试用例失败: {e}")
            print(f"AI响应内容: {ai_response}")
            raise Exception(f"测试用例解析失败: {str(e)}")


    
    def get_test_cases_library(self) -> Dict[str, Any]:
        """获取测试用例库"""
        return self.test_cases_library.copy()
    
    def get_test_cases_by_id(self, task_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取测试用例"""
        return self.test_cases_library.get(task_id)
    
    def delete_test_cases(self, task_id: str) -> bool:
        """删除测试用例"""
        if task_id in self.test_cases_library:
            del self.test_cases_library[task_id]
            return True
        return False
    
    def get_module_stats(self) -> Dict[str, Any]:
        """获取模块统计信息"""
        total_tasks = len(self.active_tasks)
        completed_tasks = sum(1 for task in self.active_tasks.values() if task['status'] == 'completed')
        total_test_cases = sum(len(tc.get('test_cases', [])) for tc in self.test_cases_library.values())
        
        return {
            'module_name': 'TestCaseGeneration',
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'total_test_cases': total_test_cases,
            'library_size': len(self.test_cases_library)
        }

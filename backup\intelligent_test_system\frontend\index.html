<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中台智能助手 | AutoGen + DeepSeek</title>

    <!-- 引入CSS样式文件 -->
    <link rel="stylesheet" href="/css/style.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #2c3e50;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* 优化的国企风格配色方案 */
        :root {
            --primary-blue: #2c5aa0;
            --primary-blue-hover: #1e3f73;
            --secondary-blue: #3498db;
            --accent-gold: #d4af37;
            --accent-gold-hover: #b8941f;
            --success-green: #27ae60;
            --warning-orange: #f39c12;
            --error-red: #e74c3c;
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --text-light: #95a5a6;
            --border-light: #ecf0f1;
            --border-medium: #bdc3c7;
            --bg-white: #ffffff;
            --bg-light: #f8f9fa;
            --bg-lighter: #fafbfc;
            --bg-hover: #e8f4f8;
            --shadow-light: 0 2px 4px rgba(44, 62, 80, 0.1);
            --shadow-medium: 0 4px 12px rgba(44, 62, 80, 0.15);
            --shadow-strong: 0 8px 24px rgba(44, 62, 80, 0.2);
            --border-radius: 8px;
            --border-radius-large: 12px;
        }

        /* 主容器布局 */
        .app-container {
            display: flex;
            min-height: 100vh;
            background: var(--bg-light);
        }

        /* 左侧导航栏 - 优化国企风格 */
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, #2c5aa0 0%, #34495e 100%);
            color: white;
            box-shadow: var(--shadow-strong);
            position: relative;
            z-index: 100;
            border-right: 3px solid var(--accent-gold);
        }

        .sidebar-header {
            padding: 28px 20px;
            border-bottom: 2px solid rgba(212, 175, 55, 0.3);
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar-header h1 {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 8px;
            background: linear-gradient(45deg, var(--accent-gold), #f1c40f);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .sidebar-header .subtitle {
            font-size: 14px;
            opacity: 0.9;
            color: #ecf0f1;
            font-weight: 400;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-section {
            margin-bottom: 24px;
        }

        .nav-section-title {
            padding: 12px 20px;
            font-size: 13px;
            font-weight: 600;
            color: var(--accent-gold);
            text-transform: uppercase;
            letter-spacing: 0.8px;
            margin-bottom: 8px;
            border-bottom: 1px solid rgba(212, 175, 55, 0.3);
            background: rgba(212, 175, 55, 0.1);
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 14px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            position: relative;
            margin: 2px 8px;
            border-radius: 0 6px 6px 0;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.12);
            border-left-color: var(--accent-gold);
            transform: translateX(4px);
        }

        .nav-item.active {
            background: rgba(212, 175, 55, 0.2);
            border-left-color: var(--accent-gold);
            box-shadow: inset 0 0 10px rgba(212, 175, 55, 0.3);
        }

        .nav-item .icon {
            margin-right: 12px;
            font-size: 18px;
            width: 24px;
            text-align: center;
        }

        .nav-item .text {
            font-size: 14px;
            font-weight: 400;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-light);
        }

        .header {
            background: linear-gradient(135deg, var(--bg-white) 0%, var(--bg-lighter) 100%);
            padding: 20px 32px;
            border-bottom: 2px solid var(--border-light);
            box-shadow: var(--shadow-medium);
            position: sticky;
            top: 0;
            z-index: 50;
        }

        .header h2 {
            color: var(--text-primary);
            font-size: 26px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header h2::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(180deg, var(--primary-blue), var(--accent-gold));
            border-radius: 2px;
        }

        .content {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
            background: var(--bg-lighter);
        }

        /* 优化的国企风格卡片 */
        .card {
            background: var(--bg-white);
            border-radius: var(--border-radius-large);
            padding: 28px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-light);
            border-top: 3px solid var(--primary-blue);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-blue), var(--accent-gold));
        }

        .card:hover {
            box-shadow: var(--shadow-strong);
            transform: translateY(-2px);
            border-color: var(--primary-blue);
        }

        .card-title {
            font-size: 20px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .card-title .icon {
            margin-right: 12px;
            font-size: 24px;
            color: var(--primary-blue);
        }

        /* 表单样式 - Gemini风格 */
        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
            font-size: 14px;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-light);
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: all 0.2s ease;
            background: var(--bg-white);
            color: var(--text-primary);
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.15), var(--shadow-light);
            transform: translateY(-1px);
        }

        .form-textarea {
            height: 120px;
            resize: vertical;
            font-family: inherit;
        }

        /* 文件上传区域 - Gemini风格 */
        .upload-area {
            border: 2px dashed var(--border-light);
            border-radius: var(--border-radius);
            padding: 48px 24px;
            text-align: center;
            background: var(--bg-light);
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
        }

        .upload-area:hover,
        .upload-area.dragover {
            border-color: var(--primary-blue);
            background: rgba(26, 115, 232, 0.05);
        }

        .upload-icon {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        .upload-text {
            color: var(--text-primary);
            font-size: 16px;
            margin-bottom: 8px;
        }

        .upload-hint {
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* 优化的国企风格按钮 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 14px 28px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            gap: 8px;
            min-height: 44px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue), #3498db);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-blue-hover), var(--primary-blue));
            box-shadow: var(--shadow-medium);
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-green), #2ecc71);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #229954, var(--success-green));
            box-shadow: var(--shadow-medium);
            transform: translateY(-2px);
        }

        .btn-gold {
            background: linear-gradient(135deg, var(--accent-gold), #f1c40f);
            color: white;
            box-shadow: var(--shadow-light);
        }

        .btn-gold:hover {
            background: linear-gradient(135deg, var(--accent-gold-hover), var(--accent-gold));
            box-shadow: var(--shadow-medium);
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn:disabled::before {
            display: none;
        }

        /* 优化的进度条 */
        .progress-container {
            margin: 24px 0;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: var(--border-light);
            border-radius: 5px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-blue), var(--accent-gold), var(--secondary-blue));
            transition: width 0.5s ease;
            border-radius: 5px;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 页面加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .page {
            animation: fadeInUp 0.5s ease-out;
        }

        .card {
            animation: slideInLeft 0.6s ease-out;
        }

        /* 悬浮效果 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .sidebar-header h1 {
            animation: float 3s ease-in-out infinite;
        }

        .progress-text {
            margin-top: 12px;
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        /* 优化的AI生成日志 */
        .ai-log {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: #ecf0f1;
            padding: 20px;
            border-radius: var(--border-radius-large);
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            max-height: 350px;
            overflow-y: auto;
            margin: 20px 0;
            border: 2px solid var(--accent-gold);
            box-shadow: var(--shadow-medium);
            position: relative;
        }

        .ai-log::before {
            content: '🤖 AI生成日志';
            position: absolute;
            top: -12px;
            left: 16px;
            background: var(--accent-gold);
            color: white;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .log-entry {
            margin: 6px 0;
            padding: 8px 12px;
            word-wrap: break-word;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.05);
            border-left: 3px solid var(--accent-gold);
            transition: all 0.2s ease;
        }

        .log-entry:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }

        .log-timestamp {
            color: var(--text-light);
            margin-right: 12px;
            font-size: 11px;
        }

        .log-agent {
            color: var(--accent-gold);
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .log-success {
            color: #2ecc71;
        }

        .log-error {
            color: #e74c3c;
        }

        .log-info {
            color: #3498db;
        }

        .log-progress {
            color: #f39c12;
        }

        /* 测试用例展示 - Gemini卡片风格 */
        .test-case {
            background: var(--bg-white);
            border: 1px solid var(--border-light);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.2s ease;
        }

        .test-case:hover {
            box-shadow: var(--shadow-medium);
            border-color: var(--primary-blue);
        }

        .test-case-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .test-case-title {
            font-weight: 500;
            color: var(--text-primary);
            font-size: 16px;
            flex: 1;
            margin-right: 16px;
        }

        .test-case-meta {
            display: flex;
            gap: 8px;
            flex-shrink: 0;
        }

        .tag {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .tag-high {
            background: rgba(234, 67, 53, 0.1);
            color: var(--error-red);
            border: 1px solid rgba(234, 67, 53, 0.2);
        }
        .tag-medium {
            background: rgba(251, 188, 4, 0.1);
            color: var(--warning-orange);
            border: 1px solid rgba(251, 188, 4, 0.2);
        }
        .tag-low {
            background: rgba(52, 168, 83, 0.1);
            color: var(--success-green);
            border: 1px solid rgba(52, 168, 83, 0.2);
        }

        .test-case-content {
            line-height: 1.6;
        }

        .test-case-field {
            margin-bottom: 12px;
        }

        .test-case-field-label {
            font-weight: 500;
            color: var(--text-primary);
            display: block;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .test-case-field-value {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .test-case-steps {
            background: var(--bg-light);
            padding: 12px;
            border-radius: 6px;
            white-space: pre-line;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            border-left: 3px solid var(--primary-blue);
        }

        /* 文件列表 */
        .file-list {
            margin-top: 16px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: var(--bg-light);
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid var(--border-light);
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .file-icon {
            font-size: 16px;
        }

        .file-remove {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .file-remove:hover {
            background: var(--bg-hover);
            color: var(--error-red);
        }

        /* 隐藏类 */
        .hidden {
            display: none !important;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
            }

            .content {
                padding: 16px;
            }

            .test-case-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .test-case-meta {
                margin-top: 8px;
            }
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 优化的状态指示器 */
        .status-indicator {
            padding: 18px 20px;
            border-radius: var(--border-radius-large);
            margin: 20px 0;
            border-left: 5px solid;
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .status-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, currentColor, transparent);
            opacity: 0.3;
        }

        .status-success {
            background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.05));
            border-left-color: var(--success-green);
            color: var(--success-green);
            border: 1px solid rgba(39, 174, 96, 0.2);
        }

        .status-error {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.05));
            border-left-color: var(--error-red);
            color: var(--error-red);
            border: 1px solid rgba(231, 76, 60, 0.2);
        }

        .status-warning {
            background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.05));
            border-left-color: var(--warning-orange);
            color: var(--warning-orange);
            border: 1px solid rgba(243, 156, 18, 0.2);
        }

        .status-info {
            background: linear-gradient(135deg, rgba(44, 90, 160, 0.1), rgba(52, 152, 219, 0.05));
            border-left-color: var(--primary-blue);
            color: var(--primary-blue);
            border: 1px solid rgba(44, 90, 160, 0.2);
        }

        .status-info {
            background: rgba(26, 115, 232, 0.1);
            border-left-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        /* 需求智能理解样式 */
        .requirement-section {
            background: var(--bg-light);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 24px;
            border-left: 4px solid var(--primary-blue);
        }

        .requirement-section-title {
            color: var(--text-primary);
            margin-bottom: 16px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .requirement-content {
            color: var(--text-primary);
        }

        .requirement-field {
            display: flex;
            margin-bottom: 12px;
            align-items: flex-start;
            gap: 12px;
        }

        .requirement-field-label {
            font-weight: 600;
            color: var(--text-primary);
            min-width: 100px;
            flex-shrink: 0;
        }

        .requirement-field-value {
            color: var(--text-secondary);
            flex: 1;
            line-height: 1.5;
        }

        .requirement-module {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .requirement-module-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            flex-wrap: wrap;
            gap: 8px;
        }

        .requirement-module-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 16px;
        }

        .requirement-module-meta {
            display: flex;
            gap: 8px;
        }

        .requirement-module-content {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .requirement-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .requirement-item-header {
            margin-bottom: 12px;
        }

        .requirement-item-title {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 16px;
        }

        .requirement-item-content {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .sub-functions {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .sub-function {
            margin-bottom: 8px;
            padding: 8px;
            background: rgba(26, 115, 232, 0.05);
            border-radius: 4px;
            font-size: 14px;
        }

        .data-fields {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .data-field {
            margin-bottom: 6px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .field-name {
            font-weight: 600;
            color: var(--primary-blue);
        }

        .field-type {
            color: var(--warning-orange);
            font-size: 12px;
        }

        .field-required {
            color: var(--error-red);
            font-size: 12px;
            font-weight: 600;
        }

        .field-desc {
            color: var(--text-secondary);
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>🧠 数据中台智能助手</h1>
                <div class="subtitle">AutoGen + DeepSeek AI驱动</div>
            </div>
            <nav class="nav-menu">
                <div class="nav-item active" onclick="showPage('home', this)">
                    <span class="icon">🏠</span>
                    <span class="text">系统首页</span>
                </div>

                <!-- 智能问数模块 -->
                <div class="nav-section">
                    <div class="nav-section-title">智能问数</div>
                    <div class="nav-item" onclick="showPage('queryUnderstanding', this)">
                        <span class="icon">🔍</span>
                        <span class="text">查询需求理解</span>
                    </div>
                    <div class="nav-item" onclick="showPage('requirementAssets', this)">
                        <span class="icon">📚</span>
                        <span class="text">需求资产库</span>
                    </div>
                </div>

                <!-- 智能测试模块 -->
                <div class="nav-section">
                    <div class="nav-section-title">智能测试</div>
                    <div class="nav-item" onclick="showPage('testRequirementUnderstanding', this)">
                        <span class="icon">🧪</span>
                        <span class="text">测试需求理解</span>
                    </div>
                    <div class="nav-item" onclick="showPage('testCaseGeneration', this)">
                        <span class="icon">📝</span>
                        <span class="text">测试用例生成</span>
                    </div>
                    <div class="nav-item" onclick="showPage('aiTestGeneration', this)">
                        <span class="icon">🤖</span>
                        <span class="text">AI智能测试用例生成</span>
                    </div>
                    <div class="nav-item" onclick="showPage('testCaseLibrary', this)">
                        <span class="icon">📋</span>
                        <span class="text">测试用例库</span>
                    </div>
                </div>

                <!-- AI能力接口管理模块 -->
                <div class="nav-section">
                    <div class="nav-section-title">AI能力接口管理</div>
                    <div class="nav-item" onclick="showPage('apiQueryUnderstanding', this)">
                        <span class="icon">🔌</span>
                        <span class="text">查询需求理解接口</span>
                    </div>
                </div>

                <!-- 系统管理模块 -->
                <div class="nav-section">
                    <div class="nav-section-title">系统管理</div>
                    <div class="nav-item" onclick="showPage('prompts', this)">
                        <span class="icon">📝</span>
                        <span class="text">提示词管理</span>
                    </div>
                    <div class="nav-item" onclick="showPage('analytics', this)">
                        <span class="icon">📊</span>
                        <span class="text">数据分析</span>
                    </div>
                    <div class="nav-item" onclick="showPage('system', this)">
                        <span class="icon">⚙️</span>
                        <span class="text">系统状态</span>
                    </div>
                </div>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <h2 id="pageTitle">数据中台智能助手</h2>
            </div>

            <div class="content">
                <!-- 首页 -->
                <div id="homePage" class="page">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">🎯</span>
                            欢迎使用数据中台智能助手
                        </div>
                        <p style="margin-bottom: 20px; color: var(--text-secondary); font-size: 16px;">
                            基于AutoGen + DeepSeek的AI专家团队，为您的数据中台项目提供智能化支持。
                            从需求理解到测试用例生成，全流程AI驱动，支持多模态文件分析。
                        </p>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 24px 0;">
                            <div style="background: var(--bg-light); padding: 20px; border-radius: var(--border-radius); border: 1px solid var(--border-light);">
                                <h4 style="color: var(--primary-blue); margin-bottom: 8px;">🧠 智能需求理解</h4>
                                <p style="color: var(--text-secondary); font-size: 14px;">多模态AI分析，将思维导图、流程图转化为结构化需求</p>
                            </div>
                            <div style="background: var(--bg-light); padding: 20px; border-radius: var(--border-radius); border: 1px solid var(--border-light);">
                                <h4 style="color: var(--success-green); margin-bottom: 8px;">🤖 AI专家协作</h4>
                                <p style="color: var(--text-secondary); font-size: 14px;">需求分析师 + 数据测试专家 + 多模态分析师团队协作</p>
                            </div>
                            <div style="background: var(--bg-light); padding: 20px; border-radius: var(--border-radius); border: 1px solid var(--border-light);">
                                <h4 style="color: var(--warning-orange); margin-bottom: 8px;">⚡ 流式实时反馈</h4>
                                <p style="color: var(--text-secondary); font-size: 14px;">实时查看AI分析过程，透明化AI决策链路</p>
                            </div>
                            <div style="background: var(--bg-light); padding: 20px; border-radius: var(--border-radius); border: 1px solid var(--border-light);">
                                <h4 style="color: var(--error-red); margin-bottom: 8px;">📊 专业输出</h4>
                                <p style="color: var(--text-secondary); font-size: 14px;">结构化需求文档 + 专业测试用例 + Excel导出</p>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-primary" onclick="showPage('queryUnderstanding')" style="margin-right: 12px;">
                                <span>🔍</span>
                                查询需求理解
                            </button>
                            <button class="btn btn-success" onclick="showPage('aiTestGeneration')">
                                <span>🚀</span>
                                AI测试用例生成
                            </button>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-title">
                            <span class="icon">📈</span>
                            核心能力
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
                            <div>
                                <h4 style="color: var(--text-primary); margin-bottom: 8px;">🧠 智能需求分析</h4>
                                <ul style="color: var(--text-secondary); font-size: 14px; margin-left: 20px;">
                                    <li>多模态文件智能解析（思维导图、流程图、截图）</li>
                                    <li>结构化需求自动生成（功能、数据、接口、流程）</li>
                                    <li>业务逻辑智能提取和规范化输出</li>
                                    <li>项目需求文档标准化生成</li>
                                </ul>
                            </div>
                            <div>
                                <h4 style="color: var(--text-primary); margin-bottom: 8px;">🎯 专业测试生成</h4>
                                <ul style="color: var(--text-secondary); font-size: 14px; margin-left: 20px;">
                                    <li>数据质量测试（完整性、准确性、一致性）</li>
                                    <li>ETL流程测试（抽取、转换、加载）</li>
                                    <li>数据接口测试（API性能、格式验证）</li>
                                    <li>数据安全测试（权限控制、数据脱敏）</li>
                                </ul>
                            </div>
                            <div>
                                <h4 style="color: var(--text-primary); margin-bottom: 8px;">🔧 技术架构</h4>
                                <ul style="color: var(--text-secondary); font-size: 14px; margin-left: 20px;">
                                    <li>AI引擎：AutoGen + DeepSeek多专家协作</li>
                                    <li>前端：原生HTML + Gemini设计风格</li>
                                    <li>处理模式：真正的AI生成，非模拟模式</li>
                                    <li>输出格式：结构化文档 + Excel专业导出</li>
                                </ul>
                            </div>
                            <div>
                                <h4 style="color: var(--text-primary); margin-bottom: 8px;">⚡ 用户体验</h4>
                                <ul style="color: var(--text-secondary); font-size: 14px; margin-left: 20px;">
                                    <li>流式实时输出，透明AI决策过程</li>
                                    <li>提示词可配置，支持个性化定制</li>
                                    <li>多模态文件拖拽上传，操作便捷</li>
                                    <li>生成日志详细记录，便于调试优化</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 查询需求理解页面 -->
                <div id="queryUnderstandingPage" class="page hidden">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">🔍</span>
                            查询需求理解
                        </div>

                        <div style="margin-bottom: 24px;">
                            <p style="color: var(--text-secondary); margin-bottom: 16px;">
                                上传思维导图、流程图或界面截图，结合需求和上下文信息，利用多模态大模型自动生成结构化的需求描述。
                            </p>
                            <div style="background: rgba(26, 115, 232, 0.1); padding: 12px; border-radius: 6px; border-left: 3px solid var(--primary-blue); margin-bottom: 16px;">
                                <p style="color: var(--primary-blue); font-weight: bold; margin-bottom: 8px;">🧠 智能理解特性：</p>
                                <ul style="margin: 0; padding-left: 20px; color: var(--text-secondary); font-size: 14px;">
                                    <li><strong>多模态分析</strong>：支持思维导图、流程图、界面截图等文件智能分析</li>
                                    <li><strong>结构化输出</strong>：自动生成项目概览、功能需求、数据需求等结构化描述</li>
                                    <li><strong>流式生成</strong>：实时查看AI分析过程和生成进度</li>
                                    <li><strong>Excel导出</strong>：支持导出为标准Excel格式，便于项目管理</li>
                                </ul>
                            </div>
                        </div>

                        <form id="understandingForm">
                            <div class="form-group">
                                <label class="form-label" for="understandingProjectName">项目名称 *</label>
                                <input type="text" id="understandingProjectName" name="project_name" class="form-input" required
                                       placeholder="请输入项目名称，如：智慧城市数据中台系统">
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="understandingRequirements">需求描述 *</label>
                                <textarea id="understandingRequirements" name="requirements" class="form-textarea" required
                                          placeholder="请描述项目的基本需求和目标，如：&#10;1. 项目背景和业务目标&#10;2. 主要功能模块&#10;3. 用户群体和使用场景&#10;4. 预期效果和成功标准"></textarea>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="understandingContextInfo">技术上下文</label>
                                <textarea id="understandingContextInfo" name="context_info" class="form-textarea"
                                          placeholder="请描述技术环境和约束条件，如：&#10;- 现有系统架构&#10;- 技术栈要求&#10;- 集成系统&#10;- 性能要求&#10;- 安全要求"></textarea>
                            </div>

                            <div class="form-group">
                                <label class="form-label">多模态文件上传（推荐）</label>
                                <div class="upload-area" id="understandingUploadArea">
                                    <div class="upload-icon">🧠</div>
                                    <div class="upload-text">拖拽文件到此处或点击上传</div>
                                    <div class="upload-hint">支持思维导图、流程图、界面截图、需求文档等，AI将智能分析文件内容</div>
                                    <input type="file" id="understandingFileInput" multiple
                                           accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                                           style="display: none;">
                                </div>
                                <div id="understandingFileList" class="file-list"></div>
                            </div>

                            <button type="submit" class="btn btn-primary" id="understandingBtn">
                                <span>🧠</span>
                                启动需求智能理解
                            </button>
                        </form>

                        <!-- AI理解进度 -->
                        <div id="understandingProgress" class="hidden">
                            <div class="progress-container">
                                <h4 style="margin-bottom: 16px; color: var(--text-primary);">🧠 AI需求理解分析中...</h4>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="understandingProgressFill" style="width: 0%"></div>
                                </div>
                                <div class="progress-text" id="understandingProgressText">准备启动需求智能理解...</div>
                            </div>

                            <div class="ai-log" id="understandingAiLog">
                                <div class="log-entry">
                                    <span class="log-timestamp">[系统]</span>
                                    <span class="log-agent">需求智能理解</span> 多模态AI分析系统已就绪
                                </div>
                            </div>
                        </div>

                        <!-- 结构化需求展示 -->
                        <div id="structuredRequirements" class="hidden">
                            <div class="card-title" style="margin-top: 32px;">
                                <span class="icon">📋</span>
                                结构化需求描述
                            </div>
                            <div id="requirementsContent"></div>
                        </div>
                    </div>
                </div>

                <!-- 提示词管理页面 -->
                <div id="promptsPage" class="page hidden">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">📝</span>
                            提示词管理
                        </div>

                        <div style="margin-bottom: 24px;">
                            <p style="color: var(--text-secondary); margin-bottom: 16px;">
                                管理AI专家的系统提示词，可以根据项目需求自定义AI专家的行为和输出风格。
                            </p>
                            <div style="background: rgba(255, 193, 7, 0.1); padding: 12px; border-radius: 6px; border-left: 3px solid var(--warning-orange); margin-bottom: 16px;">
                                <p style="color: var(--warning-orange); font-weight: bold; margin-bottom: 8px;">📝 提示词配置说明：</p>
                                <ul style="margin: 0; padding-left: 20px; color: var(--text-secondary); font-size: 14px;">
                                    <li><strong>运行时修改</strong>：在此页面修改的提示词立即生效，但重启服务后会恢复默认</li>
                                    <li><strong>永久修改</strong>：要永久保存修改，请直接编辑 <code>backend/prompts_config.py</code> 文件</li>
                                    <li><strong>提示词组合</strong>：实际发送给AI的提示词 = 系统提示词 + 项目需求 + 生成要求</li>
                                    <li><strong>JSON格式要求</strong>：数据测试专家的提示词包含严格的JSON输出格式要求</li>
                                </ul>
                            </div>
                            <div style="display: flex; gap: 12px;">
                                <button class="btn btn-primary" onclick="loadPrompts()">
                                    <span>🔄</span>
                                    刷新提示词
                                </button>
                                <button class="btn btn-success" onclick="savePrompts()">
                                    <span>💾</span>
                                    保存修改
                                </button>
                                <button class="btn" style="background: var(--warning-orange); color: white;" onclick="resetPrompts()">
                                    <span>🔄</span>
                                    重置默认
                                </button>
                            </div>
                        </div>

                        <div id="promptsContainer">
                            <!-- 需求分析专家提示词 -->
                            <div class="prompt-section" style="margin-bottom: 32px;">
                                <h3 style="color: var(--text-primary); margin-bottom: 16px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px;">🔍</span>
                                    需求分析专家 (requirement_analyst)
                                </h3>
                                <div class="form-group">
                                    <label class="form-label">系统提示词</label>
                                    <textarea id="requirementAnalystPrompt" class="form-textarea"
                                              style="height: 200px; font-family: 'Consolas', monospace; font-size: 13px;"
                                              placeholder="需求分析专家的系统提示词..."></textarea>
                                </div>
                                <div style="font-size: 12px; color: var(--text-secondary);">
                                    此提示词定义需求分析专家的角色、职责和分析方法
                                </div>
                            </div>

                            <!-- 数据测试专家提示词 -->
                            <div class="prompt-section" style="margin-bottom: 32px;">
                                <h3 style="color: var(--text-primary); margin-bottom: 16px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px;">🧪</span>
                                    数据测试专家 (data_test_expert)
                                </h3>
                                <div class="form-group">
                                    <label class="form-label">系统提示词</label>
                                    <textarea id="dataTestExpertPrompt" class="form-textarea"
                                              style="height: 200px; font-family: 'Consolas', monospace; font-size: 13px;"
                                              placeholder="数据测试专家的系统提示词..."></textarea>
                                </div>
                                <div style="font-size: 12px; color: var(--text-secondary);">
                                    此提示词定义数据测试专家的专业领域和测试用例生成规范
                                </div>
                            </div>

                            <!-- 需求智能理解专家提示词 -->
                            <div class="prompt-section" style="margin-bottom: 32px;">
                                <h3 style="color: var(--text-primary); margin-bottom: 16px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px;">🧠</span>
                                    需求智能理解专家 (requirement_understanding)
                                </h3>
                                <div class="form-group">
                                    <label class="form-label">系统提示词</label>
                                    <textarea id="requirementUnderstandingPrompt" class="form-textarea"
                                              style="height: 200px; font-family: 'Consolas', monospace; font-size: 13px;"
                                              placeholder="需求智能理解专家的系统提示词..."></textarea>
                                </div>
                                <div style="font-size: 12px; color: var(--text-secondary);">
                                    此提示词定义需求智能理解专家的多模态分析能力和结构化输出格式
                                </div>
                            </div>

                            <!-- 文档分析专家提示词 -->
                            <div class="prompt-section">
                                <h3 style="color: var(--text-primary); margin-bottom: 16px; display: flex; align-items: center;">
                                    <span style="margin-right: 8px;">📄</span>
                                    文档分析专家 (document_analyst)
                                </h3>
                                <div class="form-group">
                                    <label class="form-label">系统提示词</label>
                                    <textarea id="documentAnalystPrompt" class="form-textarea"
                                              style="height: 200px; font-family: 'Consolas', monospace; font-size: 13px;"
                                              placeholder="文档分析专家的系统提示词..."></textarea>
                                </div>
                                <div style="font-size: 12px; color: var(--text-secondary);">
                                    此提示词定义文档分析专家的分析能力和文件处理方法
                                </div>
                            </div>
                        </div>

                        <div id="promptsStatus" style="margin-top: 24px;"></div>
                    </div>
                </div>

                <!-- 需求资产库页面 -->
                <div id="requirementAssetsPage" class="page hidden">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">📚</span>
                            需求资产库
                        </div>

                        <div style="margin-bottom: 24px;">
                            <p style="color: var(--text-secondary); margin-bottom: 16px;">
                                管理所有查询需求理解生成的结构化需求文档，支持查看详情、导出Excel和删除操作。
                            </p>
                            <div style="display: flex; gap: 12px;">
                                <button class="btn btn-primary" onclick="loadRequirementAssets()">
                                    <span>🔄</span>
                                    刷新列表
                                </button>
                                <button class="btn btn-success" onclick="exportLatestRequirements()">
                                    <span>📥</span>
                                    导出最新
                                </button>
                            </div>
                        </div>

                        <div id="requirementAssetsContent">
                            <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                                <div style="font-size: 48px; margin-bottom: 16px;">📚</div>
                                <p>暂无需求资产，请先进行查询需求理解</p>
                                <button class="btn btn-primary" onclick="showPage('queryUnderstanding')" style="margin-top: 16px;">
                                    <span>🔍</span>
                                    开始查询需求理解
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 测试需求理解页面 -->
                <div id="testRequirementUnderstandingPage" class="page hidden">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">🧪</span>
                            测试需求理解
                        </div>
                        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <div style="font-size: 48px; margin-bottom: 16px;">🧪</div>
                            <p>测试需求理解功能开发中...</p>
                        </div>
                    </div>
                </div>

                <!-- 测试用例生成页面 -->
                <div id="testCaseGenerationPage" class="page hidden">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">📝</span>
                            测试用例生成
                        </div>
                        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <div style="font-size: 48px; margin-bottom: 16px;">📝</div>
                            <p>测试用例生成功能开发中...</p>
                        </div>
                    </div>
                </div>

                <!-- AI智能测试用例生成页面 -->
                <div id="aiTestGenerationPage" class="page hidden">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">🤖</span>
                            AI智能测试用例生成
                        </div>

                        <form id="generateForm">
                            <div class="form-group">
                                <label class="form-label" for="projectName">项目名称 *</label>
                                <input type="text" id="projectName" name="project_name" class="form-input" required
                                       placeholder="请输入项目名称，如：用户行为数据仓库系统">
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="requirements">需求描述 *</label>
                                <textarea id="requirements" name="requirements" class="form-textarea" required
                                          placeholder="请详细描述测试需求，包括：&#10;1. 业务场景和数据流程&#10;2. 关键功能模块&#10;3. 数据质量要求&#10;4. 性能指标要求&#10;5. 安全合规要求"></textarea>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="contextInfo">技术上下文</label>
                                <textarea id="contextInfo" name="context_info" class="form-textarea"
                                          placeholder="请描述技术架构和工具栈，如：&#10;- 数据源：MySQL、Oracle、API接口&#10;- 消息队列：Kafka&#10;- 流处理：Flink、Spark Streaming&#10;- 数据仓库：ClickHouse、Hive&#10;- 可视化：Grafana、DataV"></textarea>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="testCaseCount">生成测试用例数量</label>
                                <input type="number" id="testCaseCount" name="test_case_count" class="form-input"
                                       value="4" min="1" max="20"
                                       placeholder="请输入要生成的测试用例数量（1-20）">
                                <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                                    建议生成3-10个测试用例，数量过多可能影响生成质量
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">多模态文件上传（可选）</label>
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-icon">📁</div>
                                    <div class="upload-text">拖拽文件到此处或点击上传</div>
                                    <div class="upload-hint">支持思维导图、流程图、界面截图、需求文档等</div>
                                    <input type="file" id="fileInput" multiple
                                           accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                                           style="display: none;">
                                </div>
                                <div id="fileList" class="file-list"></div>
                            </div>

                            <button type="submit" class="btn btn-primary" id="generateBtn">
                                <span>🚀</span>
                                启动AI专家团队生成
                            </button>
                        </form>

                        <!-- AI生成进度 -->
                        <div id="generationProgress" class="hidden">
                            <div class="progress-container">
                                <h4 style="margin-bottom: 16px; color: var(--text-primary);">🤖 AI专家团队协作中...</h4>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                                </div>
                                <div class="progress-text" id="progressText">准备启动AI专家团队...</div>
                            </div>

                            <div class="ai-log" id="aiLog">
                                <div class="log-entry">
                                    <span class="log-timestamp">[系统]</span>
                                    <span class="log-agent">AutoGen + DeepSeek</span> AI专家团队已就绪
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 测试用例库页面 -->
                <div id="testCaseLibraryPage" class="page hidden">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">📋</span>
                            测试用例库管理
                        </div>

                        <div style="margin-bottom: 24px;">
                            <p style="color: var(--text-secondary); margin-bottom: 16px;">
                                管理所有生成的测试用例，支持查看详情、导出Excel和删除操作。测试用例存储在内存中，重启服务后会清空。
                            </p>
                            <div style="display: flex; gap: 12px;">
                                <button class="btn btn-primary" onclick="loadTestCasesLibrary()">
                                    <span>🔄</span>
                                    刷新列表
                                </button>
                                <button class="btn btn-success" onclick="exportLatestTestCases()">
                                    <span>📥</span>
                                    导出最新
                                </button>
                            </div>
                        </div>

                        <div id="resultsContent">
                            <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                                <div style="font-size: 48px; margin-bottom: 16px;">📝</div>
                                <p>暂无测试用例，请先进行AI生成</p>
                                <button class="btn btn-primary" onclick="showPage('aiTestGeneration')" style="margin-top: 16px;">
                                    <span>🤖</span>
                                    开始生成
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据分析页面 -->
                <div id="analyticsPage" class="page hidden">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">📊</span>
                            测试用例数据分析
                        </div>
                        <div id="analyticsContent">
                            <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                                <div style="font-size: 48px; margin-bottom: 16px;">📊</div>
                                <p>数据分析功能开发中...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 查询需求理解接口页面 -->
                <div id="apiQueryUnderstandingPage" class="page hidden">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">🔌</span>
                            查询需求理解接口
                        </div>

                        <div style="margin-bottom: 24px;">
                            <div style="background: rgba(26, 115, 232, 0.1); padding: 16px; border-radius: 8px; border-left: 4px solid var(--primary-blue);">
                                <h4 style="color: var(--primary-blue); margin-bottom: 12px;">📋 接口说明</h4>
                                <p style="color: var(--text-secondary); margin-bottom: 12px;">
                                    查询需求理解接口提供基于AutoGen + DeepSeek的多模态AI需求分析能力，支持上传思维导图、流程图、界面截图等文件，
                                    自动生成结构化的需求描述文档。
                                </p>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 12px; margin-top: 16px;">
                                    <div>
                                        <strong>接口地址:</strong><br>
                                        <code style="background: rgba(0,0,0,0.1); padding: 2px 6px; border-radius: 4px;">POST /api/requirement-understanding</code>
                                    </div>
                                    <div>
                                        <strong>响应格式:</strong><br>
                                        <span style="color: var(--text-secondary);">流式JSON (Server-Sent Events)</span>
                                    </div>
                                    <div>
                                        <strong>AI模型:</strong><br>
                                        <span style="color: var(--text-secondary);">AutoGen + DeepSeek</span>
                                    </div>
                                    <div>
                                        <strong>支持文件:</strong><br>
                                        <span style="color: var(--text-secondary);">图片、PDF、Office文档</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 接口测试区域 -->
                        <div class="card" style="margin-top: 24px;">
                            <div class="card-title">
                                <span class="icon">🧪</span>
                                接口测试
                            </div>

                            <form id="apiTestForm">
                                <div class="form-group">
                                    <label class="form-label" for="apiProjectName">项目名称 *</label>
                                    <input type="text" id="apiProjectName" name="project_name" class="form-input" required
                                           placeholder="请输入项目名称，如：智慧城市数据中台系统">
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="apiRequirements">需求描述 *</label>
                                    <textarea id="apiRequirements" name="requirements" class="form-textarea" required
                                              placeholder="请描述项目的基本需求和目标..."></textarea>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="apiContextInfo">技术上下文</label>
                                    <textarea id="apiContextInfo" name="context_info" class="form-textarea"
                                              placeholder="请描述技术环境和约束条件..."></textarea>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">测试文件上传（可选）</label>
                                    <div class="upload-area" id="apiUploadArea">
                                        <div class="upload-icon">🔌</div>
                                        <div class="upload-text">拖拽文件到此处或点击上传</div>
                                        <div class="upload-hint">支持思维导图、流程图、界面截图等</div>
                                        <input type="file" id="apiFileInput" multiple
                                               accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                                               style="display: none;">
                                    </div>
                                    <div id="apiFileList" class="file-list"></div>
                                </div>

                                <button type="submit" class="btn btn-primary" id="apiTestBtn">
                                    <span>🚀</span>
                                    测试接口调用
                                </button>
                            </form>

                            <!-- API测试结果 -->
                            <div id="apiTestResults" class="hidden">
                                <div class="card-title" style="margin-top: 32px;">
                                    <span class="icon">📊</span>
                                    接口调用结果
                                </div>

                                <div id="apiTestLog" class="ai-log" style="margin-top: 16px;">
                                    <div class="log-entry">
                                        <span class="log-timestamp">[系统]</span>
                                        <span class="log-agent">API测试</span> 准备测试接口调用...
                                    </div>
                                </div>

                                <div id="apiResponseData" style="margin-top: 16px;"></div>
                            </div>
                        </div>

                        <!-- 接口文档 -->
                        <div class="card" style="margin-top: 24px;">
                            <div class="card-title">
                                <span class="icon">📖</span>
                                接口文档
                            </div>

                            <div style="font-family: 'Consolas', monospace; font-size: 13px;">
                                <h4 style="color: var(--text-primary); margin-bottom: 12px;">请求参数</h4>
                                <div style="background: var(--bg-light); padding: 12px; border-radius: 6px; margin-bottom: 16px;">
                                    <pre style="margin: 0; white-space: pre-wrap;">
{
  "project_name": "string (必填) - 项目名称",
  "requirements": "string (必填) - 需求描述",
  "context_info": "string (可选) - 技术上下文",
  "files": "file[] (可选) - 上传的文件"
}</pre>
                                </div>

                                <h4 style="color: var(--text-primary); margin-bottom: 12px;">响应格式 (流式)</h4>
                                <div style="background: var(--bg-light); padding: 12px; border-radius: 6px;">
                                    <pre style="margin: 0; white-space: pre-wrap;">
data: {"type": "progress", "message": "处理进度", "progress": 50}
data: {"type": "prompt", "message": "提示词", "agent": "专家名称", "prompt": "完整提示词"}
data: {"type": "ai_response", "message": "AI响应", "agent": "专家名称", "response": "AI回复"}
data: {"type": "complete", "message": "完成", "result": {...}}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统状态页面 -->
                <div id="systemPage" class="page hidden">
                    <div class="card">
                        <div class="card-title">
                            <span class="icon">⚙️</span>
                            系统状态监控
                        </div>
                        <div id="systemStatus">
                            <div style="text-align: center; padding: 20px;">
                                <div class="loading-spinner"></div>
                                <p style="margin-top: 16px; color: var(--text-secondary);">正在检查系统状态...</p>
                            </div>
                        </div>

                        <div style="margin-top: 20px; text-align: center;">
                            <button class="btn btn-primary" onclick="testAutoGenConnection()" style="margin-right: 10px;">
                                <span>🤖</span>
                                测试DeepSeek连接
                            </button>
                            <button class="btn" onclick="checkSystemHealth()" style="background: var(--success-green); color: white;">
                                <span>🔄</span>
                                刷新状态
                            </button>
                        </div>
                        <div style="display: flex; gap: 12px; margin-top: 16px;">
                            <button class="btn btn-primary" onclick="checkSystemHealth()">
                                <span>🔄</span>
                                刷新状态
                            </button>
                            <button class="btn btn-success" onclick="testAutoGenConnection()">
                                <span>🤖</span>
                                测试AutoGen连接
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模块化JavaScript文件 -->
    <script src="js/modules/navigation.js"></script>
    <script src="js/modules/requirement-understanding.js"></script>
    <script src="js/modules/requirement-assets.js"></script>
    <script src="js/modules/api-interface.js"></script>
    <script src="js/modules/test-case.js"></script>
    <script src="js/modules/system.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 全局变量
        let currentTaskId = null;
        let generatedTestCases = [];
        let uploadedFiles = [];
        let understandingUploadedFiles = [];
        let currentUnderstandingTaskId = null;
        let apiTestUploadedFiles = [];

        // 需求资产存储
        let requirementAssets = {
            tasks: {},
            latest_task_id: null
        };

        // 全局安全DOM操作函数
        function safeGetElement(id) {
            try {
                const element = document.getElementById(id);
                if (!element) {
                    console.warn(`元素 ${id} 不存在`);
                    return null;
                }
                return element;
            } catch (error) {
                console.error(`获取元素 ${id} 时出错:`, error);
                return null;
            }
        }

        function safeToggleClass(elementId, className, remove = false) {
            try {
                const element = safeGetElement(elementId);
                if (element && element.classList) {
                    if (remove) {
                        element.classList.remove(className);
                    } else {
                        element.classList.add(className);
                    }
                    return true;
                }
            } catch (error) {
                console.error(`切换类 ${className} 在元素 ${elementId} 时出错:`, error);
            }
            return false;
        }

        function safeSetProperty(elementId, property, value) {
            try {
                const element = safeGetElement(elementId);
                if (element) {
                    element[property] = value;
                    return true;
                }
            } catch (error) {
                console.error(`设置属性 ${property} 在元素 ${elementId} 时出错:`, error);
            }
            return false;
        }

        // 页面切换功能
        function showPage(pageId, element = null) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.add('hidden');
            });

            // 移除所有导航项的active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示目标页面
            safeToggleClass(pageId + 'Page', 'hidden', true);

            // 设置对应导航项为active
            if (element) {
                element.classList.add('active');
            } else {
                // 如果没有传入元素，通过事件获取
                const clickedElement = event ? event.target.closest('.nav-item') : null;
                if (clickedElement) {
                    clickedElement.classList.add('active');
                }
            }

            // 更新页面标题
            const titles = {
                'home': '数据中台智能助手',
                'queryUnderstanding': '查询需求理解',
                'requirementAssets': '需求资产库',
                'testRequirementUnderstanding': '测试需求理解',
                'testCaseGeneration': '测试用例生成',
                'aiTestGeneration': 'AI智能测试用例生成',
                'testCaseLibrary': '测试用例库',
                'apiQueryUnderstanding': '查询需求理解接口',
                'prompts': '提示词管理',
                'analytics': '测试数据分析',
                'system': '系统状态监控'
            };
            document.getElementById('pageTitle').textContent = titles[pageId];

            // 如果是系统页面，自动检查状态
            if (pageId === 'system') {
                checkSystemHealth();
            }

            // 如果是提示词页面，自动加载提示词
            if (pageId === 'prompts') {
                loadPrompts();
            }

            // 如果是测试用例库页面，自动加载测试用例
            if (pageId === 'testCaseLibrary') {
                loadTestCasesLibrary();
            }

            // 如果是需求资产库页面，自动加载需求资产
            if (pageId === 'requirementAssets') {
                loadRequirementAssets();
            }
        }

        // 添加AI生成日志
        function addAILog(message, type = 'info', agent = null) {
            const log = document.getElementById('aiLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;

            const typeIcons = {
                'info': '💡',
                'success': '✅',
                'error': '❌',
                'progress': '⚡',
                'analysis': '🔍',
                'agent': '🤖'
            };

            let agentInfo = '';
            if (agent) {
                agentInfo = `<span class="log-agent">[${agent}]</span> `;
            }

            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                ${agentInfo}${typeIcons[type] || '💡'} ${message}
            `;

            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // 文件上传处理 - 测试用例生成
        document.getElementById('uploadArea').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });

        document.getElementById('fileInput').addEventListener('change', handleFileSelect);

        // 文件上传处理 - 需求智能理解
        document.getElementById('understandingUploadArea').addEventListener('click', () => {
            document.getElementById('understandingFileInput').click();
        });

        document.getElementById('understandingFileInput').addEventListener('change', handleUnderstandingFileSelect);

        // 拖拽上传
        const uploadArea = document.getElementById('uploadArea');

        if (uploadArea) {
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                if (uploadArea.classList) {
                    uploadArea.classList.add('dragover');
                }
            });

            uploadArea.addEventListener('dragleave', () => {
                if (uploadArea.classList) {
                    uploadArea.classList.remove('dragover');
                }
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                if (uploadArea.classList) {
                    uploadArea.classList.remove('dragover');
                }
                handleFileSelect({ target: { files: e.dataTransfer.files } });
            });
        }

        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            const fileList = document.getElementById('fileList');

            files.forEach(file => {
                // 检查文件大小（限制10MB）
                if (file.size > 10 * 1024 * 1024) {
                    alert(`文件 ${file.name} 超过10MB限制`);
                    return;
                }

                uploadedFiles.push(file);

                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <span class="file-icon">${getFileIcon(file.name)}</span>
                        <span>${file.name}</span>
                        <span style="color: var(--text-secondary); font-size: 12px;">(${(file.size / 1024).toFixed(1)} KB)</span>
                    </div>
                    <button class="file-remove" onclick="removeFile(this, '${file.name}')">✕</button>
                `;
                fileList.appendChild(fileItem);
            });
        }

        function getFileIcon(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            const icons = {
                'png': '🖼️', 'jpg': '🖼️', 'jpeg': '🖼️', 'gif': '🖼️', 'bmp': '🖼️',
                'pdf': '📄', 'doc': '📝', 'docx': '📝', 'xls': '📊', 'xlsx': '📊',
                'ppt': '📊', 'pptx': '📊'
            };
            return icons[ext] || '📎';
        }

        function removeFile(button, filename) {
            uploadedFiles = uploadedFiles.filter(file => file.name !== filename);
            button.parentElement.remove();
        }

        // 需求智能理解的拖拽上传
        const understandingUploadArea = document.getElementById('understandingUploadArea');

        understandingUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            understandingUploadArea.classList.add('dragover');
        });

        understandingUploadArea.addEventListener('dragleave', () => {
            understandingUploadArea.classList.remove('dragover');
        });

        understandingUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            understandingUploadArea.classList.remove('dragover');
            handleUnderstandingFileSelect({ target: { files: e.dataTransfer.files } });
        });

        function handleUnderstandingFileSelect(event) {
            const files = Array.from(event.target.files);
            const fileList = document.getElementById('understandingFileList');

            files.forEach(file => {
                // 检查文件大小（限制10MB）
                if (file.size > 10 * 1024 * 1024) {
                    alert(`文件 ${file.name} 超过10MB限制`);
                    return;
                }

                understandingUploadedFiles.push(file);

                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <span class="file-icon">${getFileIcon(file.name)}</span>
                        <span>${file.name}</span>
                        <span style="color: var(--text-secondary); font-size: 12px;">(${(file.size / 1024).toFixed(1)} KB)</span>
                    </div>
                    <button class="file-remove" onclick="removeUnderstandingFile(this, '${file.name}')">✕</button>
                `;
                fileList.appendChild(fileItem);
            });
        }

        function removeUnderstandingFile(button, filename) {
            understandingUploadedFiles = understandingUploadedFiles.filter(file => file.name !== filename);
            button.parentElement.remove();
        }

        // 显示提示词详情
        function showPromptDetails(agent, prompt, logContainerId = 'aiLog') {
            const log = document.getElementById(logContainerId);
            const promptEntry = document.createElement('div');
            promptEntry.className = 'log-entry';
            promptEntry.style.cssText = 'background: rgba(255, 255, 255, 0.05); padding: 12px; margin: 8px 0; border-radius: 6px; border-left: 3px solid #ffd700;';

            const agentNames = {
                'requirement_analyst': '需求分析专家',
                'data_test_expert': '数据测试专家',
                'requirement_understanding': '需求智能理解专家'
            };

            promptEntry.innerHTML = `
                <div style="color: #ffd700; font-weight: bold; margin-bottom: 8px;">
                    📝 ${agentNames[agent] || agent} 提示词详情:
                </div>
                <div style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 4px; font-size: 12px; max-height: 200px; overflow-y: auto; white-space: pre-wrap; line-height: 1.4;">
${prompt}
                </div>
                <div style="margin-top: 8px; font-size: 11px; color: #888;">
                    提示词长度: ${prompt.length} 字符 | 点击可展开/收起
                </div>
            `;

            // 添加点击展开/收起功能
            let isExpanded = false;
            const contentDiv = promptEntry.querySelector('div:nth-child(2)');
            const toggleDiv = promptEntry.querySelector('div:nth-child(3)');

            // 初始状态：收起
            contentDiv.style.maxHeight = '100px';

            promptEntry.addEventListener('click', () => {
                if (isExpanded) {
                    contentDiv.style.maxHeight = '100px';
                    toggleDiv.textContent = `提示词长度: ${prompt.length} 字符 | 点击可展开/收起`;
                } else {
                    contentDiv.style.maxHeight = 'none';
                    toggleDiv.textContent = `提示词长度: ${prompt.length} 字符 | 点击收起`;
                }
                isExpanded = !isExpanded;
            });

            log.appendChild(promptEntry);
            log.scrollTop = log.scrollHeight;
        }

        // 显示AI响应详情
        function showAIResponseDetails(agent, response, logContainerId = 'aiLog') {
            const log = document.getElementById(logContainerId);
            const responseEntry = document.createElement('div');
            responseEntry.className = 'log-entry';
            responseEntry.style.cssText = 'background: rgba(0, 255, 0, 0.05); padding: 12px; margin: 8px 0; border-radius: 6px; border-left: 3px solid #00ff00;';

            const agentNames = {
                'requirement_analyst': '需求分析专家',
                'data_test_expert': '数据测试专家',
                'requirement_understanding': '需求智能理解专家'
            };

            // 尝试检测是否包含JSON
            let isJsonResponse = false;
            let jsonContent = '';
            try {
                const jsonMatch = response.match(/\[[\s\S]*\]/);
                if (jsonMatch) {
                    isJsonResponse = true;
                    jsonContent = jsonMatch[0];
                }
            } catch (e) {
                // 不是JSON格式
            }

            responseEntry.innerHTML = `
                <div style="color: #00ff00; font-weight: bold; margin-bottom: 8px;">
                    🤖 ${agentNames[agent] || agent} AI响应内容:
                    ${isJsonResponse ? '<span style="color: #ffd700; font-size: 12px;">[包含JSON格式测试用例]</span>' : '<span style="color: #ff6b6b; font-size: 12px;">[文本响应]</span>'}
                </div>
                <div style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 4px; font-size: 12px; max-height: 200px; overflow-y: auto; white-space: pre-wrap; line-height: 1.4;">
${response}
                </div>
                <div style="margin-top: 8px; font-size: 11px; color: #888;">
                    响应长度: ${response.length} 字符 | ${isJsonResponse ? '✅ 检测到JSON格式' : '⚠️ 非JSON格式'} | 点击可展开/收起
                </div>
            `;

            // 添加点击展开/收起功能
            let isExpanded = false;
            const contentDiv = responseEntry.querySelector('div:nth-child(2)');
            const toggleDiv = responseEntry.querySelector('div:nth-child(3)');

            // 初始状态：收起
            contentDiv.style.maxHeight = '100px';

            responseEntry.addEventListener('click', () => {
                if (isExpanded) {
                    contentDiv.style.maxHeight = '100px';
                    toggleDiv.innerHTML = `响应长度: ${response.length} 字符 | ${isJsonResponse ? '✅ 检测到JSON格式' : '⚠️ 非JSON格式'} | 点击可展开/收起`;
                } else {
                    contentDiv.style.maxHeight = 'none';
                    toggleDiv.innerHTML = `响应长度: ${response.length} 字符 | ${isJsonResponse ? '✅ 检测到JSON格式' : '⚠️ 非JSON格式'} | 点击收起`;
                }
                isExpanded = !isExpanded;
            });

            log.appendChild(responseEntry);
            log.scrollTop = log.scrollHeight;
        }

        // 表单提交 - AI生成
        document.getElementById('generateForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);

            // 添加上传的文件
            uploadedFiles.forEach(file => {
                formData.append('files', file);
            });

            // 显示进度区域 - 使用安全函数
            safeToggleClass('generationProgress', 'hidden', true);
            safeSetProperty('generateBtn', 'disabled', true);

            // 清空之前的日志
            const aiLogElement = safeGetElement('aiLog');
            if (aiLogElement) {
                aiLogElement.innerHTML = '';
            }
            addAILog('🚀 启动AutoGen + DeepSeek AI专家团队...', 'info');

            try {
                const response = await fetch('/api/generate-test-cases', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));

                                // 更新进度条
                                if (data.progress !== undefined) {
                                    const progressFill = safeGetElement('progressFill');
                                    if (progressFill) {
                                        progressFill.style.width = data.progress + '%';
                                    }
                                }

                                // 更新进度文本
                                if (data.message) {
                                    const progressText = safeGetElement('progressText');
                                    if (progressText) {
                                        progressText.textContent = data.message;
                                    }
                                }

                                // 添加日志
                                if (data.type === 'progress') {
                                    addAILog(data.message, 'progress', data.agent);
                                } else if (data.type === 'prompt') {
                                    addAILog(data.message, 'info', data.agent);
                                    // 显示提示词详情
                                    showPromptDetails(data.agent, data.prompt);
                                } else if (data.type === 'ai_response') {
                                    addAILog(data.message, 'info', data.agent);
                                    // 显示AI响应详情
                                    showAIResponseDetails(data.agent, data.response);
                                } else if (data.type === 'analysis') {
                                    addAILog(data.message, 'analysis');
                                    if (data.content) {
                                        addAILog(`分析结果: ${data.content}`, 'info');
                                    }
                                } else if (data.type === 'complete') {
                                    addAILog(data.message, 'success');

                                    // 保存结果
                                    if (data.result) {
                                        currentTaskId = data.result.task_id;
                                        generatedTestCases = data.result.test_cases;

                                        // 显示测试用例
                                        console.log('收到测试用例数据:', data.result);
                                        displayTestCases(data.result.test_cases, data.result);

                                        // 确保切换到结果页面
                                        setTimeout(() => {
                                            showPage('results');
                                        }, 100);

                                        addAILog(`🎉 成功生成 ${data.result.test_cases.length} 个专业测试用例`, 'success');
                                    }
                                } else if (data.type === 'error') {
                                    addAILog(data.message, 'error');
                                } else if (data.type === 'info') {
                                    addAILog(data.message, 'info');
                                }
                            } catch (parseError) {
                                console.error('解析数据失败:', parseError);
                                addAILog(`数据解析失败: ${parseError.message}`, 'error');
                            }
                        }
                    }
                }
            } catch (error) {
                addAILog(`生成失败: ${error.message}`, 'error');
                console.error('生成失败:', error);
            } finally {
                safeSetProperty('generateBtn', 'disabled', false);
            }
        });

        // 显示测试用例
        function displayTestCases(testCases, result = null) {
            console.log('displayTestCases 被调用:', { testCases, result });

            const container = safeGetElement('resultsContent');
            if (!container) {
                console.error('找不到 resultsContent 容器');
                return;
            }

            if (!testCases || testCases.length === 0) {
                console.warn('测试用例为空或未定义');
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                        <div style="font-size: 48px; margin-bottom: 16px;">📝</div>
                        <p>暂无测试用例</p>
                        <p style="font-size: 12px; margin-top: 16px; opacity: 0.7;">调试信息: testCases = ${JSON.stringify(testCases)}</p>
                    </div>
                `;
                return;
            }

            console.log(`准备显示 ${testCases.length} 个测试用例`);

            let html = `
                <div style="margin-bottom: 24px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 16px;">
                    <div>
                        <h3 style="color: var(--text-primary); margin-bottom: 8px;">共生成 ${testCases.length} 个专业测试用例</h3>
                        ${result ? `<p style="color: var(--text-secondary); font-size: 14px;">AI框架: ${result.ai_framework} | 生成时间: ${new Date(result.generation_time).toLocaleString()}</p>` : ''}
                    </div>
                    <div style="display: flex; gap: 12px;">
                        <button class="btn btn-success" onclick="exportExcel()">
                            <span>📥</span>
                            导出Excel
                        </button>
                        <button class="btn btn-primary" onclick="showPage('aiTestGeneration')">
                            <span>🔄</span>
                            重新生成
                        </button>
                    </div>
                </div>
            `;

            // 统计信息
            const stats = getTestCaseStats(testCases);
            html += `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                    <div style="background: var(--bg-light); padding: 16px; border-radius: var(--border-radius); text-align: center;">
                        <div style="font-size: 24px; font-weight: 500; color: var(--error-red);">${stats.high}</div>
                        <div style="color: var(--text-secondary); font-size: 14px;">高优先级</div>
                    </div>
                    <div style="background: var(--bg-light); padding: 16px; border-radius: var(--border-radius); text-align: center;">
                        <div style="font-size: 24px; font-weight: 500; color: var(--warning-orange);">${stats.medium}</div>
                        <div style="color: var(--text-secondary); font-size: 14px;">中优先级</div>
                    </div>
                    <div style="background: var(--bg-light); padding: 16px; border-radius: var(--border-radius); text-align: center;">
                        <div style="font-size: 24px; font-weight: 500; color: var(--success-green);">${stats.low}</div>
                        <div style="color: var(--text-secondary); font-size: 14px;">低优先级</div>
                    </div>
                    <div style="background: var(--bg-light); padding: 16px; border-radius: var(--border-radius); text-align: center;">
                        <div style="font-size: 24px; font-weight: 500; color: var(--primary-blue);">${stats.types}</div>
                        <div style="color: var(--text-secondary); font-size: 14px;">测试类型</div>
                    </div>
                </div>
            `;

            // 测试用例列表
            testCases.forEach((testCase, index) => {
                const priorityClass = {
                    '高': 'tag-high',
                    '中': 'tag-medium',
                    '低': 'tag-low'
                }[testCase.priority] || 'tag-medium';

                const riskClass = {
                    '高': 'tag-high',
                    '中': 'tag-medium',
                    '低': 'tag-low'
                }[testCase.risk_level] || 'tag-medium';

                html += `
                    <div class="test-case">
                        <div class="test-case-header">
                            <div class="test-case-title">${testCase.id || `TC_${(index + 1).toString().padStart(3, '0')}`}. ${testCase.title}</div>
                            <div class="test-case-meta">
                                <span class="tag ${priorityClass}">${testCase.priority}优先级</span>
                                <span class="tag ${riskClass}">${testCase.risk_level}风险</span>
                                <span class="tag" style="background: rgba(26, 115, 232, 0.1); color: var(--primary-blue); border: 1px solid rgba(26, 115, 232, 0.2);">${testCase.test_type}</span>
                            </div>
                        </div>
                        <div class="test-case-content">
                            <div class="test-case-field">
                                <span class="test-case-field-label">🎯 测试目标:</span>
                                <div class="test-case-field-value">${testCase.objective}</div>
                            </div>
                            <div class="test-case-field">
                                <span class="test-case-field-label">📋 前置条件:</span>
                                <div class="test-case-field-value">${testCase.preconditions}</div>
                            </div>
                            <div class="test-case-field">
                                <span class="test-case-field-label">🔧 测试步骤:</span>
                                <div class="test-case-steps">${testCase.test_steps}</div>
                            </div>
                            <div class="test-case-field">
                                <span class="test-case-field-label">✅ 预期结果:</span>
                                <div class="test-case-field-value">${testCase.expected_result}</div>
                            </div>
                            <div class="test-case-field">
                                <span class="test-case-field-label">💼 业务影响:</span>
                                <div class="test-case-field-value">${testCase.business_impact}</div>
                            </div>
                            ${testCase.test_data ? `
                            <div class="test-case-field">
                                <span class="test-case-field-label">📊 测试数据:</span>
                                <div class="test-case-field-value">${testCase.test_data}</div>
                            </div>
                            ` : ''}
                            ${testCase.automation_feasibility ? `
                            <div class="test-case-field">
                                <span class="test-case-field-label">🤖 自动化可行性:</span>
                                <div class="test-case-field-value">${testCase.automation_feasibility}</div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
            console.log('测试用例已成功显示到页面');
        }

        // 获取测试用例统计信息
        function getTestCaseStats(testCases) {
            const stats = {
                high: 0,
                medium: 0,
                low: 0,
                types: new Set()
            };

            testCases.forEach(testCase => {
                switch (testCase.priority) {
                    case '高': stats.high++; break;
                    case '中': stats.medium++; break;
                    case '低': stats.low++; break;
                }
                if (testCase.test_type) {
                    stats.types.add(testCase.test_type);
                }
            });

            stats.types = stats.types.size;
            return stats;
        }

        // 导出Excel
        async function exportExcel() {
            if (!currentTaskId) {
                alert('没有可导出的测试用例');
                return;
            }

            try {
                addAILog('📥 开始导出Excel文件...', 'info');

                const response = await fetch(`/api/export/${currentTaskId}`);

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `数据中台测试用例_${new Date().toISOString().slice(0, 10)}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    addAILog('✅ Excel文件导出成功', 'success');
                } else {
                    throw new Error(`导出失败: ${response.status}`);
                }
            } catch (error) {
                addAILog(`❌ 导出失败: ${error.message}`, 'error');
                console.error('导出失败:', error);
            }
        }

        // 检查系统状态
        async function checkSystemHealth() {
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div class="loading-spinner"></div>
                    <p style="margin-top: 16px; color: var(--text-secondary);">正在检查系统状态...</p>
                </div>
            `;

            try {
                // 首先尝试快速健康检查
                const response = await fetch('/api/health/quick');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('快速健康检查API响应:', data);

                // 安全地处理features数组
                const featuresHtml = data.features && Array.isArray(data.features)
                    ? data.features.map(feature => `<li>${feature}</li>`).join('')
                    : '<li>功能信息不可用</li>';

                statusDiv.innerHTML = `
                    <div class="status-indicator status-success">
                        <h4 style="margin-bottom: 12px;">✅ 系统运行正常</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
                            <div>
                                <p><strong>版本:</strong> ${data.version || '未知'}</p>
                                <p><strong>AI框架:</strong> ${data.ai_framework || '未知'}</p>
                                <p><strong>状态:</strong> ${data.status || '未知'}</p>
                            </div>
                            <div>
                                <p><strong>功能特性:</strong></p>
                                <ul style="margin-left: 20px; margin-top: 8px;">
                                    ${featuresHtml}
                                </ul>
                            </div>
                        </div>
                        <p style="margin-top: 16px; font-size: 14px; opacity: 0.8;">
                            <strong>检查时间:</strong> ${data.timestamp ? new Date(data.timestamp).toLocaleString() : '未知'}
                        </p>
                    </div>
                `;
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="status-indicator status-error">
                        <h4 style="margin-bottom: 12px;">❌ 系统连接异常</h4>
                        <p>无法连接到后端服务</p>
                        <p style="font-size: 14px; margin-top: 8px;">错误信息: ${error.message}</p>
                        <p style="font-size: 14px; margin-top: 8px;">请检查后端服务是否正常运行</p>
                    </div>
                `;
            }
        }

        // 测试AutoGen + DeepSeek连接
        async function testAutoGenConnection() {
            const statusDiv = document.getElementById('systemStatus');

            // 添加测试状态
            const testDiv = document.createElement('div');
            testDiv.innerHTML = `
                <div class="status-indicator status-info" style="margin-top: 16px;">
                    <h4 style="margin-bottom: 12px;">🤖 测试AutoGen + DeepSeek连接...</h4>
                    <div style="text-align: center;">
                        <div class="loading-spinner"></div>
                        <p style="margin-top: 8px;">正在调用DeepSeek API...</p>
                    </div>
                </div>
            `;
            statusDiv.appendChild(testDiv);

            try {
                const response = await fetch('/api/test-autogen');
                const data = await response.json();

                if (data.status === 'success') {
                    testDiv.innerHTML = `
                        <div class="status-indicator status-success" style="margin-top: 16px;">
                            <h4 style="margin-bottom: 12px;">✅ AutoGen + DeepSeek连接成功</h4>
                            <div style="background: var(--bg-light); padding: 12px; border-radius: 6px; margin: 12px 0;">
                                <p><strong>AI响应:</strong></p>
                                <p style="font-style: italic; margin-top: 8px;">"${data.ai_response}"</p>
                            </div>
                            <div style="font-size: 14px; opacity: 0.8;">
                                <p><strong>模型:</strong> ${data.config.model}</p>
                                <p><strong>API地址:</strong> ${data.config.base_url}</p>
                                <p><strong>API密钥:</strong> ${data.config.api_key.substring(0, 10)}...</p>
                            </div>
                        </div>
                    `;
                    addAILog('✅ AutoGen + DeepSeek连接测试成功', 'success');
                } else {
                    testDiv.innerHTML = `
                        <div class="status-indicator status-error" style="margin-top: 16px;">
                            <h4 style="margin-bottom: 12px;">❌ AutoGen + DeepSeek连接失败</h4>
                            <p><strong>错误信息:</strong> ${data.message}</p>
                            <div style="font-size: 14px; opacity: 0.8; margin-top: 8px;">
                                <p><strong>配置信息:</strong></p>
                                <p>模型: ${data.config.model}</p>
                                <p>API地址: ${data.config.base_url}</p>
                                <p>API密钥: ${data.config.api_key.substring(0, 10)}...</p>
                            </div>
                        </div>
                    `;
                    addAILog(`❌ AutoGen连接测试失败: ${data.message}`, 'error');
                }
            } catch (error) {
                testDiv.innerHTML = `
                    <div class="status-indicator status-error" style="margin-top: 16px;">
                        <h4 style="margin-bottom: 12px;">❌ 连接测试失败</h4>
                        <p>无法连接到测试接口: ${error.message}</p>
                    </div>
                `;
                addAILog(`❌ 连接测试失败: ${error.message}`, 'error');
            }
        }

        // 提示词管理功能
        async function loadPrompts() {
            const statusDiv = document.getElementById('promptsStatus');
            statusDiv.innerHTML = `
                <div class="status-indicator status-info">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div class="loading-spinner"></div>
                        <span>正在加载提示词...</span>
                    </div>
                </div>
            `;

            try {
                const response = await fetch('/api/prompts');
                const data = await response.json();

                if (data.status === 'success') {
                    // 填充提示词到文本框
                    document.getElementById('requirementAnalystPrompt').value = data.prompts.requirement_analyst;
                    document.getElementById('dataTestExpertPrompt').value = data.prompts.data_test_expert;
                    document.getElementById('documentAnalystPrompt').value = data.prompts.document_analyst;
                    document.getElementById('requirementUnderstandingPrompt').value = data.prompts.requirement_understanding;

                    statusDiv.innerHTML = `
                        <div class="status-indicator status-success">
                            ✅ 提示词加载成功
                        </div>
                    `;
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="status-indicator status-error">
                        ❌ 加载失败: ${error.message}
                    </div>
                `;
            }
        }

        async function savePrompts() {
            const statusDiv = document.getElementById('promptsStatus');
            statusDiv.innerHTML = `
                <div class="status-indicator status-info">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div class="loading-spinner"></div>
                        <span>正在保存提示词...</span>
                    </div>
                </div>
            `;

            try {
                const formData = new FormData();
                formData.append('requirement_analyst', document.getElementById('requirementAnalystPrompt').value);
                formData.append('data_test_expert', document.getElementById('dataTestExpertPrompt').value);
                formData.append('document_analyst', document.getElementById('documentAnalystPrompt').value);
                formData.append('requirement_understanding', document.getElementById('requirementUnderstandingPrompt').value);

                const response = await fetch('/api/prompts', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.status === 'success') {
                    statusDiv.innerHTML = `
                        <div class="status-indicator status-success">
                            ✅ 提示词保存成功！AI专家已更新配置
                        </div>
                    `;
                    addAILog('✅ 提示词配置已更新', 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="status-indicator status-error">
                        ❌ 保存失败: ${error.message}
                    </div>
                `;
                addAILog(`❌ 提示词保存失败: ${error.message}`, 'error');
            }
        }

        async function resetPrompts() {
            if (!confirm('确定要重置为默认提示词吗？当前的修改将丢失。')) {
                return;
            }

            const statusDiv = document.getElementById('promptsStatus');
            statusDiv.innerHTML = `
                <div class="status-indicator status-info">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div class="loading-spinner"></div>
                        <span>正在重置提示词...</span>
                    </div>
                </div>
            `;

            try {
                const response = await fetch('/api/prompts/default');
                const data = await response.json();

                if (data.status === 'success') {
                    // 填充默认提示词
                    document.getElementById('requirementAnalystPrompt').value = data.prompts.requirement_analyst;
                    document.getElementById('dataTestExpertPrompt').value = data.prompts.data_test_expert;
                    document.getElementById('documentAnalystPrompt').value = data.prompts.document_analyst;
                    document.getElementById('requirementUnderstandingPrompt').value = data.prompts.requirement_understanding;

                    statusDiv.innerHTML = `
                        <div class="status-indicator status-success">
                            ✅ 已重置为默认提示词，请点击"保存修改"应用更改
                        </div>
                    `;
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="status-indicator status-error">
                        ❌ 重置失败: ${error.message}
                    </div>
                `;
            }
        }

        // 测试用例库管理功能
        async function loadTestCasesLibrary() {
            const container = document.getElementById('resultsContent');
            container.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div class="loading-spinner"></div>
                    <p style="margin-top: 16px; color: var(--text-secondary);">正在加载测试用例库...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/test-cases');
                const data = await response.json();

                console.log('测试用例库API响应:', data);

                if (data.status === 'success' && data.tasks.length > 0) {
                    console.log(`找到 ${data.tasks.length} 个测试用例任务`);
                    displayTestCasesLibrary(data.tasks);
                } else {
                    console.log('测试用例库为空或获取失败:', data);
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <div style="font-size: 48px; margin-bottom: 16px;">📝</div>
                            <p>暂无测试用例，请先进行AI生成</p>
                            <button class="btn btn-primary" onclick="showPage('aiTestGeneration')" style="margin-top: 16px;">
                                <span>🤖</span>
                                开始生成
                            </button>
                        </div>
                    `;
                }
            } catch (error) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: var(--error-red);">
                        <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
                        <p>加载失败: ${error.message}</p>
                        <button class="btn btn-primary" onclick="loadTestCasesLibrary()" style="margin-top: 16px;">
                            <span>🔄</span>
                            重试
                        </button>
                    </div>
                `;
            }
        }

        function displayTestCasesLibrary(tasks) {
            const container = document.getElementById('resultsContent');

            let html = `
                <div style="margin-bottom: 24px;">
                    <h3 style="color: var(--text-primary); margin-bottom: 16px;">测试用例任务列表 (共 ${tasks.length} 个任务)</h3>
                </div>
            `;

            tasks.forEach((task, index) => {
                const isLatest = index === 0;
                html += `
                    <div class="test-case" style="margin-bottom: 16px;">
                        <div class="test-case-header">
                            <div class="test-case-title">
                                ${task.project_name}
                                ${isLatest ? '<span class="tag" style="background: var(--success-green); color: white; margin-left: 8px;">最新</span>' : ''}
                            </div>
                            <div class="test-case-meta">
                                <span class="tag" style="background: rgba(26, 115, 232, 0.1); color: var(--primary-blue);">
                                    ${task.total_cases} 个用例
                                </span>
                                <span class="tag" style="background: rgba(255, 193, 7, 0.1); color: var(--warning-orange);">
                                    ${task.ai_framework}
                                </span>
                            </div>
                        </div>
                        <div class="test-case-content">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                                <div style="color: var(--text-secondary); font-size: 14px;">
                                    生成时间: ${new Date(task.generation_time).toLocaleString()}
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn" style="background: var(--primary-blue); color: white; padding: 6px 12px; font-size: 12px;"
                                            onclick="viewTaskDetails('${task.task_id}')">
                                        <span>👁️</span>
                                        查看详情
                                    </button>
                                    <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;"
                                            onclick="exportTaskTestCases('${task.task_id}')">
                                        <span>📥</span>
                                        导出Excel
                                    </button>
                                    <button class="btn" style="background: var(--error-red); color: white; padding: 6px 12px; font-size: 12px;"
                                            onclick="deleteTask('${task.task_id}', '${task.project_name}')">
                                        <span>🗑️</span>
                                        删除
                                    </button>
                                </div>
                            </div>
                            <div style="font-size: 12px; color: var(--text-secondary);">
                                任务ID: ${task.task_id}
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        async function viewTaskDetails(taskId) {
            try {
                const response = await fetch(`/api/test-cases/${taskId}`);
                const data = await response.json();

                if (data.status === 'success') {
                    // 显示详细的测试用例
                    displayTestCases(data.test_cases, data.metadata);
                    currentTaskId = taskId;
                    generatedTestCases = data.test_cases;
                } else {
                    alert(`获取测试用例详情失败: ${data.message}`);
                }
            } catch (error) {
                alert(`获取测试用例详情失败: ${error.message}`);
            }
        }

        async function exportTaskTestCases(taskId) {
            try {
                addAILog(`📥 开始导出任务 ${taskId} 的Excel文件...`, 'info');

                const response = await fetch(`/api/export/${taskId}`);

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `测试用例_${taskId}_${new Date().toISOString().slice(0, 10)}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    addAILog('✅ Excel文件导出成功', 'success');
                } else {
                    throw new Error(`导出失败: ${response.status}`);
                }
            } catch (error) {
                addAILog(`❌ 导出失败: ${error.message}`, 'error');
            }
        }

        async function exportLatestTestCases() {
            try {
                const response = await fetch('/api/test-cases');
                const data = await response.json();

                if (data.status === 'success' && data.latest_task_id) {
                    await exportTaskTestCases(data.latest_task_id);
                } else {
                    alert('没有可导出的测试用例');
                }
            } catch (error) {
                alert(`导出失败: ${error.message}`);
            }
        }

        async function deleteTask(taskId, projectName) {
            if (!confirm(`确定要删除项目"${projectName}"的测试用例吗？此操作不可恢复。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/test-cases/${taskId}`, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (data.status === 'success') {
                    addAILog(`✅ 任务 ${taskId} 删除成功`, 'success');
                    // 重新加载测试用例库
                    loadTestCasesLibrary();
                } else {
                    alert(`删除失败: ${data.message}`);
                }
            } catch (error) {
                alert(`删除失败: ${error.message}`);
            }
        }

        // 需求智能理解表单提交
        document.getElementById('understandingForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);

            // 添加上传的文件
            understandingUploadedFiles.forEach(file => {
                formData.append('files', file);
            });

            // 显示进度区域
            safeToggleClass('understandingProgress', 'hidden', true);
            safeSetProperty('understandingBtn', 'disabled', true);

            // 清空之前的日志
            const understandingAiLog = safeGetElement('understandingAiLog');
            if (understandingAiLog) {
                understandingAiLog.innerHTML = '';
            }
            addUnderstandingLog('🧠 启动需求智能理解分析...', 'info');

            try {
                const response = await fetch('/api/requirement-understanding', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));

                                // 更新进度条
                                if (data.progress !== undefined) {
                                    document.getElementById('understandingProgressFill').style.width = data.progress + '%';
                                }

                                // 更新进度文本
                                if (data.message) {
                                    document.getElementById('understandingProgressText').textContent = data.message;
                                }

                                // 添加日志
                                if (data.type === 'progress') {
                                    addUnderstandingLog(data.message, 'progress', data.agent);
                                } else if (data.type === 'prompt') {
                                    addUnderstandingLog(data.message, 'info', data.agent);
                                    showPromptDetails(data.agent, data.prompt, 'understandingAiLog');
                                } else if (data.type === 'ai_response') {
                                    addUnderstandingLog(data.message, 'info', data.agent);
                                    showAIResponseDetails(data.agent, data.response, 'understandingAiLog');
                                } else if (data.type === 'complete') {
                                    addUnderstandingLog(data.message, 'success');

                                    // 保存结果
                                    if (data.result) {
                                        currentUnderstandingTaskId = data.result.task_id;

                                        // 保存到需求资产库
                                        requirementAssets.tasks[data.result.task_id] = {
                                            task_id: data.result.task_id,
                                            structured_requirements: data.result.structured_requirements,
                                            metadata: {
                                                project_name: data.result.project_name,
                                                requirements: data.result.requirements,
                                                context_info: data.result.context_info,
                                                ai_framework: data.result.ai_framework,
                                                generation_time: data.result.generation_time
                                            }
                                        };
                                        requirementAssets.latest_task_id = data.result.task_id;

                                        // 显示结构化需求
                                        displayStructuredRequirements(data.result.structured_requirements, data.result);

                                        addUnderstandingLog(`🎉 需求智能理解完成！已保存到需求资产库`, 'success');
                                    }
                                } else if (data.type === 'error') {
                                    addUnderstandingLog(data.message, 'error');
                                } else if (data.type === 'info') {
                                    addUnderstandingLog(data.message, 'info');
                                }
                            } catch (parseError) {
                                console.error('解析数据失败:', parseError);
                                addUnderstandingLog(`数据解析失败: ${parseError.message}`, 'error');
                            }
                        }
                    }
                }
            } catch (error) {
                addUnderstandingLog(`生成失败: ${error.message}`, 'error');
                console.error('生成失败:', error);
            } finally {
                document.getElementById('understandingBtn').disabled = false;
            }
        });

        // 添加需求理解日志
        function addUnderstandingLog(message, type = 'info', agent = null) {
            const log = document.getElementById('understandingAiLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;

            const typeIcons = {
                'info': '💡',
                'success': '✅',
                'error': '❌',
                'progress': '⚡',
                'analysis': '🔍',
                'agent': '🤖'
            };

            let agentInfo = '';
            if (agent) {
                agentInfo = `<span class="log-agent">[${agent}]</span> `;
            }

            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                ${agentInfo}${typeIcons[type] || '💡'} ${message}
            `;

            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // 显示结构化需求
        function displayStructuredRequirements(requirements, result = null) {
            const container = safeGetElement('requirementsContent');
            safeToggleClass('structuredRequirements', 'hidden', true);

            if (!requirements) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                        <div style="font-size: 48px; margin-bottom: 16px;">📝</div>
                        <p>结构化需求生成失败</p>
                    </div>
                `;
                return;
            }

            // 检查是否是错误响应
            if (requirements.error) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: var(--error-red);">
                        <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
                        <h3>AI响应解析失败</h3>
                        <p style="margin: 16px 0;">${requirements.message}</p>
                        <div style="background: var(--bg-light); padding: 16px; border-radius: 8px; margin: 16px 0; text-align: left;">
                            <h4>AI原始响应:</h4>
                            <pre style="white-space: pre-wrap; font-size: 12px; color: var(--text-secondary); max-height: 300px; overflow-y: auto; border: 1px solid rgba(0,0,0,0.1); padding: 8px; border-radius: 4px;">${requirements.ai_response}</pre>
                        </div>
                        <div style="background: rgba(255, 193, 7, 0.1); padding: 12px; border-radius: 6px; border-left: 3px solid var(--warning-orange); margin: 16px 0; text-align: left;">
                            <h4 style="color: var(--warning-orange); margin-bottom: 8px;">💡 解决建议:</h4>
                            <ul style="margin: 0; padding-left: 20px; color: var(--text-secondary); font-size: 14px;">
                                <li>检查需求智能理解专家的提示词，确保要求输出纯JSON格式</li>
                                <li>确认提示词中明确要求"不要包含markdown代码块标记"</li>
                                <li>检查DeepSeek API连接是否正常</li>
                                <li>尝试简化项目描述，避免特殊字符</li>
                            </ul>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary);">
                            请检查提示词配置，确保AI输出正确的JSON格式，或者重新尝试生成。
                        </p>
                        <button class="btn btn-primary" onclick="showPage('queryUnderstanding')" style="margin-top: 16px;">
                            <span>🔄</span>
                            重新分析
                        </button>
                    </div>
                `;
                return;
            }

            let html = `
                <div style="margin-bottom: 24px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 16px;">
                    <div>
                        <h3 style="color: var(--text-primary); margin-bottom: 8px;">结构化需求描述</h3>
                        ${result ? `<p style="color: var(--text-secondary); font-size: 14px;">AI框架: ${result.ai_framework} | 生成时间: ${new Date(result.generation_time).toLocaleString()}</p>` : ''}
                    </div>
                    <div style="display: flex; gap: 12px;">
                        <button class="btn btn-success" onclick="exportRequirementsExcel()">
                            <span>📥</span>
                            导出Excel
                        </button>
                        <button class="btn btn-primary" onclick="showPage('understanding')">
                            <span>🔄</span>
                            重新分析
                        </button>
                    </div>
                </div>
            `;

            // 项目概览
            if (requirements.project_overview) {
                html += `
                    <div class="requirement-section">
                        <h4 class="requirement-section-title">🎯 项目概览</h4>
                        <div class="requirement-content">
                            <div class="requirement-field">
                                <span class="requirement-field-label">项目名称:</span>
                                <span class="requirement-field-value">${requirements.project_overview.project_name || '未定义'}</span>
                            </div>
                            <div class="requirement-field">
                                <span class="requirement-field-label">项目描述:</span>
                                <div class="requirement-field-value">${requirements.project_overview.project_description || '未定义'}</div>
                            </div>
                            <div class="requirement-field">
                                <span class="requirement-field-label">业务领域:</span>
                                <span class="requirement-field-value">${requirements.project_overview.business_domain || '未定义'}</span>
                            </div>
                            <div class="requirement-field">
                                <span class="requirement-field-label">目标用户:</span>
                                <span class="requirement-field-value">${requirements.project_overview.target_users || '未定义'}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 功能性需求
            if (requirements.functional_requirements && requirements.functional_requirements.length > 0) {
                html += `
                    <div class="requirement-section">
                        <h4 class="requirement-section-title">⚙️ 功能性需求 (${requirements.functional_requirements.length}个模块)</h4>
                        <div class="requirement-content">
                `;

                requirements.functional_requirements.forEach((module, index) => {
                    const priorityClass = {
                        '高': 'tag-high',
                        '中': 'tag-medium',
                        '低': 'tag-low'
                    }[module.priority] || 'tag-medium';

                    const complexityClass = {
                        '高': 'tag-high',
                        '中': 'tag-medium',
                        '低': 'tag-low'
                    }[module.complexity] || 'tag-medium';

                    html += `
                        <div class="requirement-module">
                            <div class="requirement-module-header">
                                <span class="requirement-module-title">${module.module_name}</span>
                                <div class="requirement-module-meta">
                                    <span class="tag ${priorityClass}">${module.priority}优先级</span>
                                    <span class="tag ${complexityClass}">${module.complexity}复杂度</span>
                                </div>
                            </div>
                            <div class="requirement-module-content">
                                <p>${module.module_description}</p>
                                ${module.sub_functions && module.sub_functions.length > 0 ? `
                                    <div class="sub-functions">
                                        <strong>子功能:</strong>
                                        ${module.sub_functions.map(func => `
                                            <div class="sub-function">
                                                <strong>${func.function_name}:</strong> ${func.function_description}
                                            </div>
                                        `).join('')}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `;
                });

                html += `</div></div>`;
            }

            // 非功能性需求
            if (requirements.non_functional_requirements && requirements.non_functional_requirements.length > 0) {
                html += `
                    <div class="requirement-section">
                        <h4 class="requirement-section-title">🛡️ 非功能性需求</h4>
                        <div class="requirement-content">
                `;

                requirements.non_functional_requirements.forEach(req => {
                    html += `
                        <div class="requirement-item">
                            <div class="requirement-item-header">
                                <span class="tag" style="background: rgba(255, 193, 7, 0.1); color: var(--warning-orange);">${req.category}</span>
                            </div>
                            <div class="requirement-item-content">
                                <p><strong>要求:</strong> ${req.requirement}</p>
                                <p><strong>验收标准:</strong> ${req.acceptance_criteria}</p>
                            </div>
                        </div>
                    `;
                });

                html += `</div></div>`;
            }

            // 数据需求
            if (requirements.data_requirements && requirements.data_requirements.length > 0) {
                html += `
                    <div class="requirement-section">
                        <h4 class="requirement-section-title">📊 数据需求</h4>
                        <div class="requirement-content">
                `;

                requirements.data_requirements.forEach(data => {
                    html += `
                        <div class="requirement-item">
                            <div class="requirement-item-header">
                                <span class="requirement-item-title">${data.data_entity}</span>
                            </div>
                            <div class="requirement-item-content">
                                <p>${data.data_description}</p>
                                ${data.data_fields && data.data_fields.length > 0 ? `
                                    <div class="data-fields">
                                        <strong>数据字段:</strong>
                                        <div style="margin-top: 8px;">
                                            ${data.data_fields.map(field => `
                                                <div class="data-field">
                                                    <span class="field-name">${field.field_name}</span>
                                                    <span class="field-type">(${field.field_type})</span>
                                                    ${field.is_required ? '<span class="field-required">*必填</span>' : ''}
                                                    <span class="field-desc">- ${field.field_description}</span>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `;
                });

                html += `</div></div>`;
            }

            container.innerHTML = html;
        }

        // 导出需求Excel（待实现）
        async function exportRequirementsExcel() {
            if (!currentUnderstandingTaskId) {
                alert('没有可导出的结构化需求');
                return;
            }

            try {
                addUnderstandingLog('📥 开始导出需求Excel文件...', 'info');

                // 这里可以调用后端API导出Excel
                // const response = await fetch(`/api/export-requirements/${currentUnderstandingTaskId}`);

                // 暂时提示功能开发中
                alert('Excel导出功能开发中，敬请期待！');
                addUnderstandingLog('⚠️ Excel导出功能开发中', 'info');

            } catch (error) {
                addUnderstandingLog(`❌ 导出失败: ${error.message}`, 'error');
            }
        }

        // 加载需求资产库
        async function loadRequirementAssets() {
            const container = document.getElementById('requirementAssetsContent');

            try {
                // 这里应该调用后端API获取需求资产
                // const response = await fetch('/api/requirement-assets');
                // const data = await response.json();

                // 暂时使用本地存储的数据
                const assets = Object.values(requirementAssets.tasks);

                if (assets.length === 0) {
                    container.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                            <div style="font-size: 48px; margin-bottom: 16px;">📚</div>
                            <p>暂无需求资产，请先进行查询需求理解</p>
                            <button class="btn btn-primary" onclick="showPage('queryUnderstanding')" style="margin-top: 16px;">
                                <span>🔍</span>
                                开始查询需求理解
                            </button>
                        </div>
                    `;
                    return;
                }

                let html = `
                    <div style="margin-bottom: 24px;">
                        <h3 style="color: var(--text-primary); margin-bottom: 16px;">需求资产列表 (${assets.length}个)</h3>
                    </div>
                `;

                assets.forEach((asset, index) => {
                    const metadata = asset.metadata;
                    html += `
                        <div class="requirement-asset-item" style="background: var(--bg-white); border: 1px solid var(--border-light); border-radius: 8px; padding: 20px; margin-bottom: 16px;">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;">
                                <div>
                                    <h4 style="color: var(--text-primary); margin-bottom: 8px;">${metadata.project_name}</h4>
                                    <div style="display: flex; gap: 12px; font-size: 14px; color: var(--text-secondary);">
                                        <span>📅 ${new Date(metadata.generation_time).toLocaleString()}</span>
                                        <span>🤖 ${metadata.ai_framework}</span>
                                    </div>
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn" style="background: var(--primary-blue); color: white; padding: 6px 12px; font-size: 12px;" onclick="viewRequirementAsset('${asset.task_id}')">
                                        <span>👁️</span>
                                        查看详情
                                    </button>
                                    <button class="btn" style="background: var(--success-green); color: white; padding: 6px 12px; font-size: 12px;" onclick="exportRequirementAsset('${asset.task_id}')">
                                        <span>📥</span>
                                        导出
                                    </button>
                                </div>
                            </div>
                            <div style="color: var(--text-secondary); font-size: 14px; line-height: 1.5;">
                                ${metadata.requirements.substring(0, 200)}${metadata.requirements.length > 200 ? '...' : ''}
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = html;

            } catch (error) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: var(--error-red);">
                        <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
                        <p>加载需求资产失败: ${error.message}</p>
                        <button class="btn btn-primary" onclick="loadRequirementAssets()" style="margin-top: 16px;">
                            <span>🔄</span>
                            重新加载
                        </button>
                    </div>
                `;
            }
        }

        // 查看需求资产详情
        function viewRequirementAsset(taskId) {
            const asset = requirementAssets.tasks[taskId];
            if (!asset) {
                alert('需求资产不存在');
                return;
            }

            // 显示需求资产详情（可以复用displayStructuredRequirements函数）
            showPage('queryUnderstanding');
            setTimeout(() => {
                displayStructuredRequirements(asset.structured_requirements, asset.metadata);
            }, 100);
        }

        // 导出需求资产
        async function exportRequirementAsset(taskId) {
            try {
                alert('需求资产导出功能开发中，敬请期待！');
            } catch (error) {
                alert(`导出失败: ${error.message}`);
            }
        }

        // 导出最新需求资产
        async function exportLatestRequirements() {
            if (!requirementAssets.latest_task_id) {
                alert('没有可导出的需求资产');
                return;
            }

            await exportRequirementAsset(requirementAssets.latest_task_id);
        }

        // API测试文件上传处理
        document.getElementById('apiUploadArea').addEventListener('click', () => {
            document.getElementById('apiFileInput').click();
        });

        document.getElementById('apiFileInput').addEventListener('change', handleApiFileSelect);

        // API测试拖拽上传
        const apiUploadArea = document.getElementById('apiUploadArea');

        apiUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            apiUploadArea.classList.add('dragover');
        });

        apiUploadArea.addEventListener('dragleave', () => {
            apiUploadArea.classList.remove('dragover');
        });

        apiUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            apiUploadArea.classList.remove('dragover');
            handleApiFileSelect({ target: { files: e.dataTransfer.files } });
        });

        function handleApiFileSelect(event) {
            const files = Array.from(event.target.files);
            const fileList = document.getElementById('apiFileList');

            files.forEach(file => {
                if (file.size > 10 * 1024 * 1024) {
                    alert(`文件 ${file.name} 超过10MB限制`);
                    return;
                }

                apiTestUploadedFiles.push(file);

                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <span class="file-icon">${getFileIcon(file.name)}</span>
                        <span>${file.name}</span>
                        <span style="color: var(--text-secondary); font-size: 12px;">(${(file.size / 1024).toFixed(1)} KB)</span>
                    </div>
                    <button class="file-remove" onclick="removeApiFile(this, '${file.name}')">✕</button>
                `;
                fileList.appendChild(fileItem);
            });
        }

        function removeApiFile(button, filename) {
            apiTestUploadedFiles = apiTestUploadedFiles.filter(file => file.name !== filename);
            button.parentElement.remove();
        }

        // API测试表单提交
        document.getElementById('apiTestForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);

            // 添加上传的文件
            apiTestUploadedFiles.forEach(file => {
                formData.append('files', file);
            });

            // 显示测试结果区域
            safeToggleClass('apiTestResults', 'hidden', true);
            safeSetProperty('apiTestBtn', 'disabled', true);

            // 清空之前的日志
            const logContainer = safeGetElement('apiTestLog');
            if (logContainer) {
                logContainer.innerHTML = '';
            }
            addApiTestLog('🚀 开始API接口测试...', 'info');

            try {
                const response = await fetch('/api/requirement-understanding', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                addApiTestLog('✅ 接口连接成功，开始接收流式响应...', 'success');

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let responseData = null;

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));

                                // 记录API响应
                                addApiTestLog(`📡 接收到响应: ${data.type} - ${data.message}`, 'info');

                                if (data.type === 'complete' && data.result) {
                                    responseData = data.result;
                                    addApiTestLog('🎉 接口调用成功完成！', 'success');

                                    // 显示响应数据
                                    displayApiResponse(responseData);
                                } else if (data.type === 'error') {
                                    addApiTestLog(`❌ 接口返回错误: ${data.message}`, 'error');
                                }

                            } catch (parseError) {
                                addApiTestLog(`⚠️ 响应解析失败: ${parseError.message}`, 'error');
                            }
                        }
                    }
                }

            } catch (error) {
                addApiTestLog(`❌ 接口调用失败: ${error.message}`, 'error');
            } finally {
                document.getElementById('apiTestBtn').disabled = false;
            }
        });

        // 添加API测试日志
        function addApiTestLog(message, type = 'info') {
            const log = document.getElementById('apiTestLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';

            const typeIcons = {
                'info': '💡',
                'success': '✅',
                'error': '❌'
            };

            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                ${typeIcons[type] || '💡'} ${message}
            `;

            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // 显示API响应数据
        function displayApiResponse(responseData) {
            const container = document.getElementById('apiResponseData');

            const html = `
                <div class="card" style="margin-top: 16px;">
                    <div class="card-title">
                        <span class="icon">📊</span>
                        API响应数据
                    </div>
                    <div style="background: var(--bg-light); padding: 16px; border-radius: 8px;">
                        <h4 style="margin-bottom: 12px;">响应摘要</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 12px; margin-bottom: 16px;">
                            <div>
                                <strong>任务ID:</strong><br>
                                <code>${responseData.task_id}</code>
                            </div>
                            <div>
                                <strong>AI框架:</strong><br>
                                <span>${responseData.ai_framework}</span>
                            </div>
                            <div>
                                <strong>生成时间:</strong><br>
                                <span>${new Date(responseData.generation_time).toLocaleString()}</span>
                            </div>
                            <div>
                                <strong>项目名称:</strong><br>
                                <span>${responseData.project_name}</span>
                            </div>
                        </div>

                        <h4 style="margin-bottom: 12px;">结构化需求数据</h4>
                        <div style="background: white; padding: 12px; border-radius: 6px; border: 1px solid var(--border-light);">
                            <pre style="margin: 0; white-space: pre-wrap; font-size: 12px; max-height: 300px; overflow-y: auto;">${JSON.stringify(responseData.structured_requirements, null, 2)}</pre>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            addAILog('🎉 数据中台智能助手已就绪', 'success');
            addAILog('🤖 AutoGen + DeepSeek AI专家团队待命中', 'info');
            checkSystemHealth();
        });
    </script>
</body>
</html>
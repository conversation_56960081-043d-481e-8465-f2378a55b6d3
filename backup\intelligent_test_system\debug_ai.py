#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试AI调用的调试脚本
"""

import sys
import os
import json
import asyncio
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

async def test_ai_direct():
    """直接测试AI调用"""
    print("🧪 开始直接测试AutoGen + DeepSeek...")
    
    try:
        # 导入AI生成器
        from ai_generator import AITestCaseGenerator
        
        print("📋 初始化AI生成器...")
        ai_gen = AITestCaseGenerator()
        
        # 测试连接
        print("🔗 测试AI连接...")
        connection_result = await ai_gen.test_ai_connection()
        print(f"连接结果: {connection_result}")
        
        if connection_result['status'] != 'success':
            print("❌ AI连接失败，停止测试")
            return False
        
        # 测试需求分析
        print("\n🔍 测试需求分析...")
        test_prompt = """
项目名称: 用户管理系统
需求描述: 实现用户注册、登录、个人信息管理功能
技术上下文: 基于Spring Boot + MySQL的Web应用

请分析这个项目的测试要点。
"""
        
        try:
            response = await asyncio.to_thread(
                ai_gen.requirement_analyst.generate_reply,
                messages=[{"role": "user", "content": test_prompt}]
            )
            
            print(f"✅ 需求分析响应: {str(response)[:300]}...")
            
        except Exception as e:
            print(f"❌ 需求分析失败: {e}")
            return False
        
        # 测试测试用例生成
        print("\n🧪 测试测试用例生成...")
        test_case_prompt = """
基于用户管理系统的需求分析，请生成3个测试用例。

请严格按照以下JSON格式输出：

```json
[
  {
    "title": "用户注册功能测试",
    "objective": "验证用户注册功能的正确性",
    "preconditions": "系统已启动，数据库连接正常",
    "test_steps": "1. 打开注册页面\\n2. 输入用户信息\\n3. 点击注册按钮\\n4. 验证注册结果",
    "expected_result": "用户成功注册，系统返回成功提示",
    "priority": "高",
    "test_type": "功能测试",
    "risk_level": "中",
    "business_impact": "注册失败影响用户使用",
    "test_data": "有效用户信息",
    "automation_feasibility": "高"
  }
]
```
"""
        
        try:
            response = await asyncio.to_thread(
                ai_gen.data_test_expert.generate_reply,
                messages=[{"role": "user", "content": test_case_prompt}]
            )
            
            print(f"✅ 测试用例生成响应: {str(response)[:500]}...")
            
            # 尝试解析JSON
            import re
            json_patterns = [
                r'```json\s*(\[[\s\S]*?\])\s*```',
                r'```\s*(\[[\s\S]*?\])\s*```',
                r'(\[[\s\S]*\])',
            ]
            
            json_str = None
            for i, pattern in enumerate(json_patterns):
                match = re.search(pattern, str(response), re.DOTALL)
                if match:
                    json_str = match.group(1)
                    print(f"✅ 使用模式 {i} 提取到JSON")
                    break
            
            if json_str:
                try:
                    parsed_data = json.loads(json_str)
                    print(f"✅ 成功解析JSON，包含 {len(parsed_data)} 个测试用例")
                    print(f"第一个测试用例: {json.dumps(parsed_data[0], indent=2, ensure_ascii=False)}")
                    return True
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"提取的JSON: {json_str[:200]}...")
                    return False
            else:
                print("❌ 无法从响应中提取JSON")
                print(f"完整响应: {str(response)}")
                return False
                
        except Exception as e:
            print(f"❌ 测试用例生成失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_full_generation():
    """测试完整的生成流程"""
    print("\n🚀 测试完整生成流程...")
    
    try:
        from modules.test_case_generation import TestCaseGeneration
        from ai_generator import AITestCaseGenerator
        
        # 初始化
        ai_gen = AITestCaseGenerator()
        test_gen = TestCaseGeneration(ai_gen)
        
        # 模拟生成请求
        async for chunk in test_gen.generate_test_cases(
            project_name="用户管理系统",
            requirements="实现用户注册、登录、个人信息管理功能",
            context_info="基于Spring Boot + MySQL的Web应用",
            test_case_count=3,
            files=[]
        ):
            print(f"📨 收到chunk: {chunk.get('type')} - {chunk.get('message', '')}")
            
            if chunk.get('type') == 'complete':
                result = chunk.get('result')
                if result:
                    print(f"🎉 生成完成！包含 {len(result.get('test_cases', []))} 个测试用例")
                    return True
            elif chunk.get('type') == 'error':
                print(f"❌ 生成失败: {chunk.get('message')}")
                return False
        
        return False
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 AutoGen + DeepSeek 调试测试")
    print("=" * 50)
    
    # 测试直接AI调用
    direct_ok = await test_ai_direct()
    
    if direct_ok:
        # 测试完整流程
        full_ok = await test_full_generation()
        
        print("\n" + "=" * 50)
        print("📊 测试结果:")
        print(f"  🤖 直接AI调用: {'✅ 成功' if direct_ok else '❌ 失败'}")
        print(f"  🔄 完整生成流程: {'✅ 成功' if full_ok else '❌ 失败'}")
        
        if direct_ok and full_ok:
            print("\n🎉 所有测试通过！AI生成功能正常")
        else:
            print("\n⚠️ 部分测试失败，需要进一步调试")
    else:
        print("\n❌ 直接AI调用失败，跳过完整流程测试")

if __name__ == "__main__":
    asyncio.run(main())

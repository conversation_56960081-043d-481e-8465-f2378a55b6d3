/**
 * 测试用例模块 - 负责AI测试用例生成和管理
 */

class TestCaseModule {
    constructor() {
        this.uploadedFiles = [];
        this.generatedTestCases = [];
        this.currentTaskId = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupFileUpload();
        
        // 注册到导航模块
        if (window.navigationModule) {
            window.navigationModule.pageHandlers.set('testCaseLibrary', () => this.loadTestCasesLibrary());
        }
    }

    setupEventListeners() {
        // AI测试用例生成表单提交
        const form = document.getElementById('generateForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    }

    setupFileUpload() {
        // 文件上传处理
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        if (uploadArea && fileInput) {
            uploadArea.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
            
            // 拖拽上传
            if (uploadArea) {
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    if (uploadArea.classList) {
                        uploadArea.classList.add('dragover');
                    }
                });

                uploadArea.addEventListener('dragleave', () => {
                    if (uploadArea.classList) {
                        uploadArea.classList.remove('dragover');
                    }
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    if (uploadArea.classList) {
                        uploadArea.classList.remove('dragover');
                    }
                    this.handleFileSelect({ target: { files: e.dataTransfer.files } });
                });
            }
        }
    }

    handleFileSelect(event) {
        const files = Array.from(event.target.files);
        const fileList = document.getElementById('fileList');
        
        files.forEach(file => {
            if (file.size > 10 * 1024 * 1024) {
                alert(`文件 ${file.name} 超过10MB限制`);
                return;
            }
            
            this.uploadedFiles.push(file);
            
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <span class="file-icon">${this.getFileIcon(file.name)}</span>
                    <span>${file.name}</span>
                    <span style="color: var(--text-secondary); font-size: 12px;">(${(file.size / 1024).toFixed(1)} KB)</span>
                </div>
                <button class="file-remove" onclick="testCaseModule.removeFile(this, '${file.name}')">✕</button>
            `;
            fileList.appendChild(fileItem);
        });
    }

    removeFile(button, filename) {
        this.uploadedFiles = this.uploadedFiles.filter(file => file.name !== filename);
        button.parentElement.remove();
    }

    getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const icons = {
            'png': '🖼️', 'jpg': '🖼️', 'jpeg': '🖼️', 'gif': '🖼️', 'bmp': '🖼️',
            'pdf': '📄', 'doc': '📝', 'docx': '📝', 'xls': '📊', 'xlsx': '📊',
            'ppt': '📊', 'pptx': '📊'
        };
        return icons[ext] || '📎';
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        
        // 添加上传的文件
        this.uploadedFiles.forEach(file => {
            formData.append('files', file);
        });
        
        // 显示进度区域 - 使用安全的DOM操作
        this.safeToggleClass('generationProgress', 'hidden', false);
        this.safeSetProperty('generateBtn', 'disabled', true);

        // 清空之前的日志
        const aiLogElement = this.safeGetElement('aiLog');
        if (aiLogElement) {
            aiLogElement.innerHTML = '';
        }

        this.addAILog('🚀 启动AI测试用例生成...', 'info');
        
        try {
            const response = await fetch('/api/generate-test-cases', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            await this.processStreamResponse(response);
            
        } catch (error) {
            this.addAILog(`生成失败: ${error.message}`, 'error');
            console.error('生成失败:', error);
        } finally {
            // 恢复按钮状态
            this.safeSetProperty('generateBtn', 'disabled', false);
        }
    }

    async processStreamResponse(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        this.handleStreamData(data);
                    } catch (parseError) {
                        console.error('解析数据失败:', parseError);
                        this.addAILog(`数据解析失败: ${parseError.message}`, 'error');
                    }
                }
            }
        }
    }

    handleStreamData(data) {
        // 更新进度条
        if (data.progress !== undefined) {
            const progressFill = this.safeGetElement('progressFill');
            if (progressFill) {
                progressFill.style.width = data.progress + '%';
            }
        }

        // 更新进度文本
        if (data.message) {
            const progressText = this.safeGetElement('progressText');
            if (progressText) {
                progressText.textContent = data.message;
            }
        }
        
        // 处理不同类型的数据
        switch (data.type) {
            case 'progress':
                this.addAILog(data.message, 'progress', data.agent);
                break;
            case 'prompt':
                this.addAILog(data.message, 'prompt', data.agent, data.prompt);
                break;
            case 'ai_response':
                this.addAILog(data.message, 'ai_response', data.agent, data.response);
                break;
            case 'complete':
                this.handleComplete(data);
                break;
            case 'error':
                this.addAILog(data.message, 'error');
                break;
            case 'info':
                this.addAILog(data.message, 'info');
                break;
        }
    }

    handleComplete(data) {
        this.addAILog(data.message, 'success');

        if (data.result && data.result.test_cases) {
            this.currentTaskId = data.result.task_id;
            this.generatedTestCases = data.result.test_cases;

            // 显示测试用例
            this.displayTestCases(data.result.test_cases, data.result);

            this.addAILog(`🎉 AI测试用例生成完成！生成了 ${data.result.test_cases.length} 个测试用例`, 'success');

            // 恢复按钮状态
            this.safeSetProperty('generateBtn', 'disabled', false);
        }
    }

    addAILog(message, type = 'info', agent = null, details = null) {
        const log = document.getElementById('aiLog');
        if (!log) {
            console.error('找不到aiLog元素');
            return;
        }

        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;

        const typeIcons = {
            'info': '💡',
            'success': '✅',
            'error': '❌',
            'progress': '⚡',
            'analysis': '🔍',
            'agent': '🤖',
            'prompt': '📝',
            'ai_response': '🤖'
        };

        let agentInfo = '';
        if (agent) {
            const agentNames = {
                'requirement_analyst': '需求分析专家',
                'data_test_expert': '数据测试专家',
                'document_analyst': '文档分析专家'
            };
            agentInfo = `<span class="log-agent">[${agentNames[agent] || agent}]</span> `;
        }

        let detailsHtml = '';
        if (details) {
            const detailsId = `details-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
            detailsHtml = `
                <div class="log-details-toggle" onclick="toggleLogDetails(this)" style="cursor: pointer; color: #1a73e8; margin-top: 8px; font-size: 12px;">
                    <span class="toggle-icon">▶</span> 查看详情
                </div>
                <div class="log-details hidden" id="${detailsId}" style="margin-top: 8px; padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 3px solid #1a73e8;">
                    <pre style="white-space: pre-wrap; font-size: 11px; margin: 0; max-height: 300px; overflow-y: auto;">${this.escapeHtml(details)}</pre>
                </div>
            `;
        }

        logEntry.innerHTML = `
            <span class="log-timestamp">[${timestamp}]</span>
            ${agentInfo}${typeIcons[type] || '💡'} ${message}
            ${detailsHtml}
        `;

        log.appendChild(logEntry);
        log.scrollTop = log.scrollHeight;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 安全的DOM操作函数
    safeGetElement(id) {
        try {
            const element = document.getElementById(id);
            if (!element) {
                console.warn(`元素 ${id} 不存在`);
                return null;
            }
            return element;
        } catch (error) {
            console.error(`获取元素 ${id} 时出错:`, error);
            return null;
        }
    }

    safeToggleClass(elementId, className, add = true) {
        try {
            const element = this.safeGetElement(elementId);
            if (element && element.classList) {
                if (add) {
                    element.classList.add(className);
                } else {
                    element.classList.remove(className);
                }
                return true;
            }
        } catch (error) {
            console.error(`切换类 ${className} 在元素 ${elementId} 时出错:`, error);
        }
        return false;
    }

    safeSetProperty(elementId, property, value) {
        try {
            const element = this.safeGetElement(elementId);
            if (element) {
                element[property] = value;
                return true;
            }
        } catch (error) {
            console.error(`设置属性 ${property} 在元素 ${elementId} 时出错:`, error);
        }
        return false;
    }

    showPromptDetails(agent, prompt) {
        // 实现提示词详情显示
        console.log('Show prompt details:', agent, prompt);
    }

    showAIResponseDetails(agent, response) {
        // 实现AI响应详情显示
        console.log('Show AI response details:', agent, response);
    }

    displayTestCases(testCases, result = null) {
        // 实现测试用例显示
        console.log('Display test cases:', testCases, result);
    }

    async loadTestCasesLibrary() {
        const container = document.getElementById('testCasesContent');
        
        try {
            // 这里可以调用后端API获取测试用例库
            // const response = await fetch('/api/test-cases');
            // const data = await response.json();
            
            // 暂时使用本地数据
            if (this.generatedTestCases.length === 0) {
                this.renderEmptyTestCases(container);
                return;
            }
            
            this.renderTestCasesList(container, this.generatedTestCases);
            
        } catch (error) {
            this.renderTestCasesError(container, error);
        }
    }

    renderEmptyTestCases(container) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                <div style="font-size: 48px; margin-bottom: 16px;">📋</div>
                <p>暂无测试用例，请先进行AI测试用例生成</p>
                <button class="btn btn-primary" onclick="showPage('aiTestGeneration')" style="margin-top: 16px;">
                    <span>🤖</span>
                    开始生成测试用例
                </button>
            </div>
        `;
    }

    renderTestCasesList(container, testCases) {
        // 实现测试用例列表渲染
        console.log('Render test cases list:', testCases);
    }

    renderTestCasesError(container, error) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: var(--error-red);">
                <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
                <p>加载测试用例失败: ${error.message}</p>
                <button class="btn btn-primary" onclick="testCaseModule.loadTestCasesLibrary()" style="margin-top: 16px;">
                    <span>🔄</span>
                    重新加载
                </button>
            </div>
        `;
    }
}

// 全局实例
window.testCaseModule = new TestCaseModule();

// 全局函数：切换日志详情显示
window.toggleLogDetails = function(toggleElement) {
    try {
        const detailsElement = toggleElement.nextElementSibling;
        const iconElement = toggleElement.querySelector('.toggle-icon');

        if (detailsElement && detailsElement.classList) {
            detailsElement.classList.toggle('hidden');

            if (iconElement) {
                iconElement.textContent = detailsElement.classList.contains('hidden') ? '▶' : '▼';
            }
        }
    } catch (error) {
        console.error('切换日志详情时出错:', error);
    }
};

// 向后兼容的全局函数
function removeFile(button, filename) {
    window.testCaseModule.removeFile(button, filename);
}

function loadTestCasesLibrary() {
    window.testCaseModule.loadTestCasesLibrary();
}

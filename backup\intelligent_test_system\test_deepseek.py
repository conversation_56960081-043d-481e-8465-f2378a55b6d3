#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API连接测试脚本
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加backend目录到Python路径
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

async def test_deepseek_direct():
    """直接测试DeepSeek API连接"""
    print("🤖 直接测试DeepSeek API连接...")
    
    try:
        from ai_generator import AITestCaseGenerator
        
        print("📋 初始化AI生成器...")
        ai_gen = AITestCaseGenerator()
        
        # 检查配置
        if hasattr(ai_gen, 'llm_config') and ai_gen.llm_config:
            config = ai_gen.llm_config.get('config_list', [])
            if config and len(config) > 0:
                api_config = config[0]
                print(f"🔑 API配置:")
                print(f"  模型: {api_config.get('model', 'unknown')}")
                print(f"  API地址: {api_config.get('base_url', 'unknown')}")
                print(f"  API密钥: {api_config.get('api_key', 'unknown')[:10]}...")
            else:
                print("❌ API配置为空")
                return False
        else:
            print("❌ 无法获取API配置")
            return False
        
        # 测试简单调用
        print("\n🚀 发送测试请求...")
        test_prompt = "请简单回复：连接测试成功"
        
        try:
            response = await asyncio.wait_for(
                asyncio.to_thread(
                    ai_gen.requirement_analyst.generate_reply,
                    messages=[{"role": "user", "content": test_prompt}]
                ),
                timeout=30.0
            )
            
            print(f"✅ DeepSeek响应成功!")
            print(f"📝 响应内容: {str(response)[:200]}...")
            return True
            
        except asyncio.TimeoutError:
            print("⚠️ DeepSeek API响应超时 (30秒)")
            print("💡 可能的原因:")
            print("  1. 网络连接问题")
            print("  2. DeepSeek服务器响应慢")
            print("  3. API密钥问题")
            return False
            
        except Exception as e:
            print(f"❌ DeepSeek API调用失败: {e}")
            print("💡 可能的原因:")
            print("  1. API密钥无效")
            print("  2. 网络连接问题")
            print("  3. DeepSeek服务不可用")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_network_connectivity():
    """测试网络连接"""
    print("\n🌐 测试网络连接...")
    
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            # 测试DeepSeek API基础连接
            try:
                async with session.get('https://api.deepseek.com', timeout=10) as response:
                    print(f"✅ DeepSeek API基础连接正常 (状态码: {response.status})")
                    return True
            except asyncio.TimeoutError:
                print("⚠️ DeepSeek API连接超时")
                return False
            except Exception as e:
                print(f"❌ DeepSeek API连接失败: {e}")
                return False
                
    except ImportError:
        print("⚠️ aiohttp未安装，跳过网络测试")
        return True
    except Exception as e:
        print(f"❌ 网络测试失败: {e}")
        return False

def test_api_key_format():
    """测试API密钥格式"""
    print("\n🔑 检查API密钥格式...")
    
    try:
        from config import get_api_key_status
        
        status = get_api_key_status()
        print(f"📊 API密钥状态: {status}")
        
        if status.get('is_configured') == True:
            print("✅ API密钥已配置")
            return True
        else:
            print("❌ API密钥未正确配置")
            print(f"详细状态: {status}")
            return False
            
    except Exception as e:
        print(f"❌ API密钥检查失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🔧 DeepSeek API连接诊断")
    print("=" * 50)
    
    # 测试API密钥格式
    key_ok = test_api_key_format()
    
    # 测试网络连接
    network_ok = await test_network_connectivity()
    
    # 测试直接API调用
    if key_ok and network_ok:
        api_ok = await test_deepseek_direct()
    else:
        print("⚠️ 跳过API测试（前置条件不满足）")
        api_ok = False
    
    print("\n" + "=" * 50)
    print("📊 诊断结果总结:")
    print(f"  🔑 API密钥: {'✅ 正常' if key_ok else '❌ 异常'}")
    print(f"  🌐 网络连接: {'✅ 正常' if network_ok else '❌ 异常'}")
    print(f"  🤖 DeepSeek API: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if key_ok and network_ok and api_ok:
        print("\n🎉 DeepSeek API连接完全正常！")
        print("💡 系统状态监控中的超时问题可能是临时的")
    elif not key_ok:
        print("\n❌ API密钥配置有问题")
        print("💡 请检查config.py中的DeepSeek API密钥配置")
    elif not network_ok:
        print("\n❌ 网络连接有问题")
        print("💡 请检查网络连接和防火墙设置")
    else:
        print("\n⚠️ DeepSeek API调用有问题")
        print("💡 建议:")
        print("  1. 检查API密钥是否有效")
        print("  2. 稍后重试（可能是服务器临时问题）")
        print("  3. 在系统状态监控中使用'测试DeepSeek连接'按钮")

if __name__ == "__main__":
    asyncio.run(main())

"""
测试AutoGen配置
"""

import json
from config import get_deepseek_config, validate_api_key

def test_autogen_config():
    """测试AutoGen配置格式"""
    print("🔍 测试AutoGen配置...")
    
    # 检查API密钥
    if not validate_api_key():
        print("⚠️ API密钥未配置，使用示例配置进行测试")
    
    # 获取配置
    config = get_deepseek_config()
    print(f"📋 配置内容: {json.dumps(config, indent=2, ensure_ascii=False)}")
    
    # 测试AutoGen导入
    try:
        from autogen import AssistantAgent
        print("✅ AutoGen导入成功")
        
        # 测试最小配置
        minimal_config = {
            "config_list": [
                {
                    "model": "deepseek-chat",
                    "api_key": "test-key",
                    "base_url": "https://api.deepseek.com/v1",
                    "api_type": "openai"
                }
            ],
            "temperature": 0.7
        }
        
        print("🧪 测试最小配置...")
        print(f"最小配置: {json.dumps(minimal_config, indent=2)}")
        
        # 尝试创建代理（不会真正调用API）
        try:
            agent = AssistantAgent(
                name="test_agent",
                llm_config=minimal_config,
                system_message="测试代理"
            )
            print("✅ AutoGen代理创建成功")
            return True
            
        except Exception as e:
            print(f"❌ AutoGen代理创建失败: {e}")
            
            # 尝试更简单的配置
            simple_config = {
                "config_list": [
                    {
                        "model": "deepseek-chat",
                        "api_key": "test-key",
                        "base_url": "https://api.deepseek.com/v1"
                    }
                ]
            }
            
            print("🧪 尝试更简单的配置...")
            try:
                agent = AssistantAgent(
                    name="test_agent",
                    llm_config=simple_config,
                    system_message="测试代理"
                )
                print("✅ 简化配置成功")
                return True
            except Exception as e2:
                print(f"❌ 简化配置也失败: {e2}")
                return False
                
    except ImportError as e:
        print(f"❌ AutoGen导入失败: {e}")
        return False

if __name__ == "__main__":
    success = test_autogen_config()
    if success:
        print("\n🎉 AutoGen配置测试成功！")
    else:
        print("\n❌ AutoGen配置测试失败，需要调整配置格式")

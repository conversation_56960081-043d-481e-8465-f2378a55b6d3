#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化测试
"""

import os
import sys
from pathlib import Path

# 设置路径
project_root = Path(__file__).parent
backend_dir = project_root / "backend"
sys.path.insert(0, str(backend_dir))

print("Python版本:", sys.version)
print("当前目录:", os.getcwd())
print("后端目录:", backend_dir)
print("路径存在:", backend_dir.exists())

# 测试基础导入
try:
    print("\n测试基础导入...")
    import fastapi
    print("FastAPI版本:", fastapi.__version__)
    
    import sqlalchemy
    print("SQLAlchemy版本:", sqlalchemy.__version__)
    
    import pymysql
    print("PyMySQL版本:", pymysql.__version__)
    
except Exception as e:
    print(f"基础导入失败: {e}")
    sys.exit(1)

# 测试配置导入
try:
    print("\n测试配置导入...")
    from app.core.config import Settings
    settings = Settings()
    print("配置创建成功")
    print(f"应用名称: {settings.APP_NAME}")
    
except Exception as e:
    print(f"配置导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n基础测试通过!")

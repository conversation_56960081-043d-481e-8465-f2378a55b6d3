#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复emoji字符
"""

import os
import re
from pathlib import Path

def fix_emoji_in_file(file_path):
    """修复文件中的emoji字符"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 定义emoji替换规则
        emoji_replacements = {
            '🔧': '',
            '📝': '',
            '✅': '',
            '❌': '',
            '⚠️': '',
            '📋': '',
            '🗑️': '',
            '🤖': '',
            '📊': '',
            '✏️': '',
            '🚀': '',
            '🗄️': '',
            '🎉': '',
            '📍': '',
            '📚': '',
            '🌐': '',
            '🛑': '',
            '👋': '',
            '🔍': '',
            '💥': '',
            '🎨': '',
            '🔗': '',
            '📄': '',
            '🎯': '',
            '🔥': '',
            '⭐': '',
            '💡': '',
            '🚨': '',
            '📈': '',
            '📉': '',
            '🔒': '',
            '🔓': '',
            '⚡': '',
            '🌟': '',
            '🎪': '',
            '🎭': '',
            '🎨': '',
            '🎯': '',
            '🎲': '',
            '🎮': '',
            '🎸': '',
            '🎺': '',
            '🎻': '',
            '🎼': '',
            '🎵': '',
            '🎶': '',
            '🎤': '',
            '🎧': '',
            '🎬': '',
            '🎭': '',
            '🎪': '',
            '🎨': '',
            '🎯': '',
            '🎲': '',
            '🎮': '',
            '🎸': '',
            '🎺': '',
            '🎻': '',
            '🎼': '',
            '🎵': '',
            '🎶': '',
            '🎤': '',
            '🎧': '',
            '🎬': ''
        }
        
        # 替换emoji
        modified = False
        for emoji, replacement in emoji_replacements.items():
            if emoji in content:
                content = content.replace(emoji, replacement)
                modified = True
        
        # 如果有修改，写回文件
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"修复: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"处理文件失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    # 需要处理的文件类型
    file_patterns = ['*.py']
    
    fixed_count = 0
    total_count = 0
    
    print("开始修复emoji字符...")
    
    # 遍历后端目录
    for pattern in file_patterns:
        for file_path in backend_dir.rglob(pattern):
            total_count += 1
            if fix_emoji_in_file(file_path):
                fixed_count += 1
    
    print(f"修复完成: 处理了 {total_count} 个文件，修复了 {fixed_count} 个文件")

if __name__ == "__main__":
    main()

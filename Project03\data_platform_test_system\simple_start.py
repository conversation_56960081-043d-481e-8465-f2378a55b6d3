#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化启动脚本
用于快速启动系统，跳过一些可能有问题的组件
"""

import os
import sys
import subprocess
import time
import socket
from pathlib import Path

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result == 0
    except:
        return False

def create_simple_frontend(static_dir):
    """创建简单的前端页面"""
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中台智能测试系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
            padding: 40px 20px;
        }
        .title {
            font-size: 3em;
            margin-bottom: 20px;
        }
        .subtitle {
            font-size: 1.2em;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        .link {
            display: inline-block;
            margin: 10px;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s;
        }
        .link:hover {
            background: rgba(255,255,255,0.3);
        }
        .status {
            margin-top: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">数据中台智能测试系统</h1>
        <p class="subtitle">基于AutoGen + DeepSeek的智能需求分析平台</p>

        <div class="status">
            <h3>系统运行正常</h3>
            <p>前端和后端服务已启动</p>
        </div>

        <div style="margin-top: 40px;">
            <a href="http://localhost:8000/docs" class="link">API文档</a>
            <a href="http://localhost:8000/health" class="link">健康检查</a>
            <a href="http://localhost:8000/api/v1/requirements/" class="link">需求API</a>
        </div>

        <div style="margin-top: 40px;">
            <p>后端服务: <a href="http://localhost:8000" class="link">http://localhost:8000</a></p>
            <p>前端服务: <a href="http://localhost:3000" class="link">http://localhost:3000</a></p>
        </div>

        <div style="margin-top: 40px; font-size: 14px; opacity: 0.8;">
            <p>如果需要完整功能，请访问: <a href="index.html" class="link">完整版前端</a></p>
        </div>
    </div>
</body>
</html>'''

    with open(static_dir / "index.html", "w", encoding="utf-8") as f:
        f.write(html_content)

    print(f"✅ 创建简单前端页面: {static_dir / 'index.html'}")

def start_backend():
    """启动后端服务"""
    print("启动后端服务...")
    
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONPATH'] = str(backend_dir)
    
    # 启动后端
    process = subprocess.Popen(
        [sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"],
        cwd=backend_dir,
        env=env
    )
    
    # 等待启动
    print("等待后端服务启动...")
    for i in range(30):
        if check_port(8000):
            print("后端服务启动成功: http://localhost:8000")
            return process
        time.sleep(1)
    
    print("后端服务启动超时")
    return None

def start_frontend():
    """启动前端服务"""
    print("启动前端服务...")

    project_root = Path(__file__).parent
    frontend_dir = project_root / "frontend"

    # 检查前端目录和index.html是否存在
    if frontend_dir.exists() and (frontend_dir / "index.html").exists():
        print("使用完整前端...")
        frontend_path = frontend_dir
    else:
        print("前端目录不完整，创建简单静态页面...")
        static_dir = project_root / "static_frontend"
        static_dir.mkdir(exist_ok=True)
        frontend_path = static_dir

        # 创建简单的index.html
        create_simple_frontend(static_dir)

    # 如果使用简单静态页面，创建HTML
    if frontend_path.name == "static_frontend":
        html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中台智能测试系统</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            min-height: 100vh; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            text-align: center; 
            padding: 40px 20px; 
        }
        .title { 
            font-size: 3em; 
            margin-bottom: 20px; 
        }
        .subtitle { 
            font-size: 1.2em; 
            margin-bottom: 40px; 
            opacity: 0.9; 
        }
        .cards { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin-top: 40px; 
        }
        .card { 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 10px; 
            backdrop-filter: blur(10px); 
        }
        .card h3 { 
            margin-bottom: 15px; 
        }
        .link { 
            display: inline-block; 
            margin: 10px; 
            padding: 12px 24px; 
            background: rgba(255,255,255,0.2); 
            color: white; 
            text-decoration: none; 
            border-radius: 6px; 
            transition: all 0.3s; 
        }
        .link:hover { 
            background: rgba(255,255,255,0.3); 
        }
        .status { 
            margin-top: 30px; 
            padding: 20px; 
            background: rgba(255,255,255,0.1); 
            border-radius: 8px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">数据中台智能测试系统</h1>
        <p class="subtitle">基于AutoGen + DeepSeek的智能需求分析平台</p>
        
        <div class="status">
            <h3>系统运行正常</h3>
            <p>前端和后端服务已启动</p>
        </div>
        
        <div class="cards">
            <div class="card">
                <h3>需求管理</h3>
                <p>创建、编辑和管理数据开发需求</p>
                <a href="http://localhost:8000/api/v1/requirements/" class="link">需求API</a>
            </div>
            <div class="card">
                <h3>AI分析</h3>
                <p>使用AutoGen + DeepSeek进行智能分析</p>
                <a href="http://localhost:8000/api/v1/requirements/analyses/" class="link">分析API</a>
            </div>
            <div class="card">
                <h3>API文档</h3>
                <p>查看完整的API接口文档</p>
                <a href="http://localhost:8000/docs" class="link">API文档</a>
            </div>
            <div class="card">
                <h3>系统监控</h3>
                <p>查看系统运行状态和健康检查</p>
                <a href="http://localhost:8000/health" class="link">健康检查</a>
            </div>
        </div>
        
        <div style="margin-top: 40px;">
            <p>后端服务: <a href="http://localhost:8000" class="link">http://localhost:8000</a></p>
            <p>前端服务: <a href="http://localhost:3000" class="link">http://localhost:3000</a></p>
        </div>
    </div>
</body>
</html>'''

        with open(frontend_path / "index.html", "w", encoding="utf-8") as f:
            f.write(html_content)

    # 启动前端服务器
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'

        # 尝试启动HTTP服务器
        process = subprocess.Popen(
            [sys.executable, "-m", "http.server", "3000", "--bind", "localhost"],
            cwd=frontend_path,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        # 等待服务启动
        print("等待前端服务启动...")
        for i in range(10):
            if check_port(3000):
                print("✅ 前端服务启动成功: http://localhost:3000")
                return process
            time.sleep(1)
            print(f"等待中... ({i+1}/10)")

        # 检查进程是否还在运行
        if process.poll() is None:
            print("⚠️ 前端服务可能已启动，但端口检查失败")
            print("请手动访问: http://localhost:3000")
            return process
        else:
            # 获取错误信息
            stdout, stderr = process.communicate()
            print(f"❌ 前端服务启动失败")
            if stderr:
                print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
            return None

    except Exception as e:
        print(f"❌ 前端服务启动异常: {e}")
        return None

def main():
    """主函数"""
    print("简化启动数据中台智能测试系统...")
    print("=" * 50)
    
    # 启动后端
    backend_process = start_backend()
    if not backend_process:
        print("后端启动失败")
        return False
    
    # 启动前端
    frontend_process = start_frontend()
    if not frontend_process:
        print("⚠️ 前端启动失败，但后端正常运行")
        print("您可以手动启动前端或直接使用API")
        frontend_process = None
    
    print("=" * 50)
    print("🎉 系统启动完成!")
    print("")
    print("📋 访问地址:")
    if frontend_process:
        print("  🌐 前端界面: http://localhost:3000")
    else:
        print("  ⚠️ 前端未启动，请手动启动或直接使用API")
    print("  🔧 后端API: http://localhost:8000")
    print("  📖 API文档: http://localhost:8000/docs")
    print("  💊 健康检查: http://localhost:8000/health")
    print("")
    if not frontend_process:
        print("💡 手动启动前端:")
        print("  cd frontend && python -m http.server 3000")
        print("")
    print("⏹️ 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        if backend_process:
            print("停止后端服务...")
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()
        if frontend_process:
            print("停止前端服务...")
            frontend_process.terminate()
            try:
                frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                frontend_process.kill()
        print("✅ 所有服务已停止")

if __name__ == "__main__":
    main()

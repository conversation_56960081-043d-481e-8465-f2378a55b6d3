#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统诊断脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print(f"Python版本: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    print("✅ Python版本符合要求")
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n检查依赖包...")
    
    required_packages = {
        'fastapi': '0.104.1',
        'uvicorn': '0.24.0',
        'sqlalchemy': '2.0.23',
        'pymysql': '1.1.0',
        'jinja2': '3.1.2',
        'pydantic': '2.5.0'
    }
    
    missing = []
    for package, version in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (缺失)")
            missing.append(package)
    
    if missing:
        print(f"\n缺失的包: {', '.join(missing)}")
        print("请运行: pip install -r backend/requirements.txt")
        return False
    
    return True

def check_file_structure():
    """检查文件结构"""
    print("\n检查文件结构...")
    
    project_root = Path(__file__).parent
    required_files = [
        "backend/main.py",
        "backend/app/core/config.py",
        "backend/app/database/connection.py",
        "backend/app/services/ai_service.py",
        "backend/app/services/requirement_service.py",
        "backend/app/api/v1/requirements.py",
        "backend/requirements.txt"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (缺失)")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_backend_import():
    """测试后端导入"""
    print("\n测试后端导入...")
    
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    sys.path.insert(0, str(backend_dir))
    
    try:
        print("1. 导入配置...")
        from app.core.config import settings
        print(f"   ✅ 配置加载成功: {settings.APP_NAME}")
        
        print("2. 导入数据库...")
        from app.database.connection import db_manager
        print("   ✅ 数据库模块导入成功")
        
        print("3. 导入AI服务...")
        from app.services.ai_service import ai_service
        print("   ✅ AI服务模块导入成功")
        
        print("4. 导入需求服务...")
        from app.services.requirement_service import requirement_service
        print("   ✅ 需求服务模块导入成功")
        
        print("5. 导入API路由...")
        from app.api.v1.requirements import router
        print("   ✅ API路由导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_uvicorn_start():
    """测试uvicorn启动"""
    print("\n测试uvicorn启动...")
    
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        # 测试uvicorn命令
        result = subprocess.run(
            [sys.executable, "-m", "uvicorn", "--help"],
            cwd=backend_dir,
            env=env,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ uvicorn命令可用")
            return True
        else:
            print(f"❌ uvicorn命令失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ uvicorn测试失败: {e}")
        return False

def test_database_config():
    """测试数据库配置"""
    print("\n测试数据库配置...")
    
    try:
        project_root = Path(__file__).parent
        backend_dir = project_root / "backend"
        sys.path.insert(0, str(backend_dir))
        
        from app.core.config import settings
        
        print(f"数据库主机: {settings.MYSQL_HOST}")
        print(f"数据库端口: {settings.MYSQL_PORT}")
        print(f"数据库名称: {settings.MYSQL_DATABASE}")
        print(f"数据库用户: {settings.MYSQL_USER}")
        print(f"DeepSeek API: {'已配置' if settings.DEEPSEEK_API_KEY and settings.DEEPSEEK_API_KEY != 'your-api-key-here' else '未配置'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("数据中台智能测试系统 - 系统诊断")
    print("=" * 60)
    
    all_passed = True
    
    # 检查Python版本
    if not check_python_version():
        all_passed = False
    
    # 检查依赖包
    if not check_dependencies():
        all_passed = False
    
    # 检查文件结构
    if not check_file_structure():
        all_passed = False
    
    # 测试后端导入
    if not test_backend_import():
        all_passed = False
    
    # 测试uvicorn
    if not test_uvicorn_start():
        all_passed = False
    
    # 测试数据库配置
    if not test_database_config():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ 所有诊断项目通过!")
        print("系统应该可以正常启动")
    else:
        print("❌ 发现问题，请根据上述信息修复")
    
    return all_passed

if __name__ == "__main__":
    main()

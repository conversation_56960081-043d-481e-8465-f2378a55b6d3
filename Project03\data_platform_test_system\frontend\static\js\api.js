/**
 * API接口模块
 * 提供与后端API的交互功能
 */

// ==================== API配置 ====================
const API_CONFIG = {
    BASE_URL: 'http://localhost:8000',
    API_PREFIX: '/api/v1',
    TIMEOUT: 30000, // 30秒超时
    RETRY_TIMES: 3, // 重试次数
    RETRY_DELAY: 1000 // 重试延迟
};

// ==================== HTTP客户端类 ====================
class HttpClient {
    constructor(config = {}) {
        this.config = { ...API_CONFIG, ...config };
        this.interceptors = {
            request: [],
            response: []
        };
    }
    
    /**
     * 添加请求拦截器
     * @param {Function} interceptor - 拦截器函数
     */
    addRequestInterceptor(interceptor) {
        this.interceptors.request.push(interceptor);
    }
    
    /**
     * 添加响应拦截器
     * @param {Function} interceptor - 拦截器函数
     */
    addResponseInterceptor(interceptor) {
        this.interceptors.response.push(interceptor);
    }
    
    /**
     * 执行请求拦截器
     * @param {Object} config - 请求配置
     * @returns {Object} 处理后的配置
     */
    async executeRequestInterceptors(config) {
        let processedConfig = config;
        for (const interceptor of this.interceptors.request) {
            processedConfig = await interceptor(processedConfig);
        }
        return processedConfig;
    }
    
    /**
     * 执行响应拦截器
     * @param {Response} response - 响应对象
     * @returns {Response} 处理后的响应
     */
    async executeResponseInterceptors(response) {
        let processedResponse = response;
        for (const interceptor of this.interceptors.response) {
            processedResponse = await interceptor(processedResponse);
        }
        return processedResponse;
    }
    
    /**
     * 发送HTTP请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} 请求Promise
     */
    async request(url, options = {}) {
        // 构建完整URL
        const fullUrl = url.startsWith('http') ? url : `${this.config.BASE_URL}${url}`;
        
        // 默认请求配置
        let requestConfig = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: this.config.TIMEOUT,
            ...options
        };
        
        // 执行请求拦截器
        requestConfig = await this.executeRequestInterceptors(requestConfig);
        
        // 处理请求体
        if (requestConfig.data && requestConfig.method !== 'GET') {
            if (requestConfig.headers['Content-Type'] === 'application/json') {
                requestConfig.body = JSON.stringify(requestConfig.data);
            } else {
                requestConfig.body = requestConfig.data;
            }
            delete requestConfig.data;
        }
        
        // 发送请求（带重试机制）
        let lastError;
        for (let i = 0; i <= this.config.RETRY_TIMES; i++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), requestConfig.timeout);
                
                const response = await fetch(fullUrl, {
                    ...requestConfig,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                // 执行响应拦截器
                const processedResponse = await this.executeResponseInterceptors(response);
                
                // 检查响应状态
                if (!processedResponse.ok) {
                    throw new Error(`HTTP ${processedResponse.status}: ${processedResponse.statusText}`);
                }
                
                // 解析响应数据
                const contentType = processedResponse.headers.get('content-type');
                let data;
                
                if (contentType && contentType.includes('application/json')) {
                    data = await processedResponse.json();
                } else {
                    data = await processedResponse.text();
                }
                
                return {
                    data,
                    status: processedResponse.status,
                    statusText: processedResponse.statusText,
                    headers: processedResponse.headers
                };
                
            } catch (error) {
                lastError = error;
                
                // 如果是最后一次重试或者是非网络错误，直接抛出
                if (i === this.config.RETRY_TIMES || error.name === 'AbortError') {
                    break;
                }
                
                // 等待后重试
                await new Promise(resolve => setTimeout(resolve, this.config.RETRY_DELAY));
                console.warn(`请求失败，正在重试 (${i + 1}/${this.config.RETRY_TIMES}):`, error.message);
            }
        }
        
        throw lastError;
    }
    
    /**
     * GET请求
     * @param {string} url - 请求URL
     * @param {Object} params - 查询参数
     * @param {Object} options - 请求选项
     * @returns {Promise} 请求Promise
     */
    async get(url, params = {}, options = {}) {
        // 构建查询字符串
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return this.request(fullUrl, {
            method: 'GET',
            ...options
        });
    }
    
    /**
     * POST请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise} 请求Promise
     */
    async post(url, data = {}, options = {}) {
        return this.request(url, {
            method: 'POST',
            data,
            ...options
        });
    }
    
    /**
     * PUT请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise} 请求Promise
     */
    async put(url, data = {}, options = {}) {
        return this.request(url, {
            method: 'PUT',
            data,
            ...options
        });
    }
    
    /**
     * DELETE请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} 请求Promise
     */
    async delete(url, options = {}) {
        return this.request(url, {
            method: 'DELETE',
            ...options
        });
    }
}

// ==================== 创建HTTP客户端实例 ====================
const httpClient = new HttpClient();

// 添加请求拦截器 - 显示加载状态
httpClient.addRequestInterceptor(async (config) => {
    // 显示加载指示器
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('show');
    }
    
    return config;
});

// 添加响应拦截器 - 隐藏加载状态和错误处理
httpClient.addResponseInterceptor(async (response) => {
    // 隐藏加载指示器
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.remove('show');
    }
    
    return response;
});

// ==================== API接口类 ====================
class API {
    constructor() {
        this.client = httpClient;
        this.baseUrl = API_CONFIG.API_PREFIX;
    }
    
    // ==================== 系统接口 ====================
    
    /**
     * 健康检查
     * @returns {Promise} 健康检查结果
     */
    async healthCheck() {
        try {
            const response = await this.client.get('/health');
            return response.data;
        } catch (error) {
            console.error('健康检查失败:', error);
            throw error;
        }
    }
    
    // ==================== 需求管理接口 ====================
    
    /**
     * 获取需求列表
     * @param {Object} params - 查询参数
     * @returns {Promise} 需求列表
     */
    async getRequirements(params = {}) {
        try {
            const response = await this.client.get(`${this.baseUrl}/requirements/`, params);
            return response.data;
        } catch (error) {
            console.error('获取需求列表失败:', error);
            // 返回空数据而不是抛出错误，避免页面崩溃
            return {
                requirements: [],
                total: 0,
                page: 1,
                page_size: 20,
                total_pages: 0
            };
        }
    }
    
    /**
     * 获取需求详情
     * @param {number} id - 需求ID
     * @returns {Promise} 需求详情
     */
    async getRequirement(id) {
        try {
            const response = await this.client.get(`${this.baseUrl}/requirements/${id}`);
            return response.data;
        } catch (error) {
            console.error('获取需求详情失败:', error);
            if (window.Utils) {
                Utils.error('获取需求详情失败: ' + error.message);
            }
            throw error;
        }
    }
    
    /**
     * 创建需求
     * @param {Object} data - 需求数据
     * @returns {Promise} 创建结果
     */
    async createRequirement(data) {
        try {
            const response = await this.client.post(`${this.baseUrl}/requirements/`, data);
            if (window.Utils) {
                Utils.success('需求创建成功');
            }
            return response.data;
        } catch (error) {
            console.error('创建需求失败:', error);
            if (window.Utils) {
                Utils.error('创建需求失败: ' + error.message);
            }
            throw error;
        }
    }
    
    /**
     * 更新需求
     * @param {number} id - 需求ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateRequirement(id, data) {
        try {
            const response = await this.client.put(`${this.baseUrl}/requirements/${id}`, data);
            if (window.Utils) {
                Utils.success('需求更新成功');
            }
            return response.data;
        } catch (error) {
            console.error('更新需求失败:', error);
            if (window.Utils) {
                Utils.error('更新需求失败: ' + error.message);
            }
            throw error;
        }
    }
    
    /**
     * 删除需求
     * @param {number} id - 需求ID
     * @returns {Promise} 删除结果
     */
    async deleteRequirement(id) {
        try {
            const response = await this.client.delete(`${this.baseUrl}/requirements/${id}`);
            if (window.Utils) {
                Utils.success('需求删除成功');
            }
            return response.data;
        } catch (error) {
            console.error('删除需求失败:', error);
            if (window.Utils) {
                Utils.error('删除需求失败: ' + error.message);
            }
            throw error;
        }
    }
    
    // ==================== AI分析接口 ====================
    
    /**
     * 提交AI分析
     * @param {Array} requirementIds - 需求ID列表
     * @returns {Promise} 分析结果
     */
    async analyzeRequirements(requirementIds) {
        try {
            const response = await this.client.post(`${this.baseUrl}/requirements/ai-analysis`, {
                requirement_ids: requirementIds
            });
            if (window.Utils) {
                Utils.success('AI分析提交成功');
            }
            return response.data;
        } catch (error) {
            console.error('AI分析失败:', error);
            if (window.Utils) {
                Utils.error('AI分析失败: ' + error.message);
            }
            throw error;
        }
    }
    
    /**
     * 获取分析结果列表
     * @param {Object} params - 查询参数
     * @returns {Promise} 分析结果列表
     */
    async getAnalysisResults(params = {}) {
        try {
            const response = await this.client.get(`${this.baseUrl}/requirements/analyses/`, params);
            return response.data;
        } catch (error) {
            console.error('获取分析结果失败:', error);
            // 返回空数据而不是抛出错误
            return {
                analyses: [],
                total: 0,
                page: 1,
                page_size: 20,
                total_pages: 0
            };
        }
    }
    
    /**
     * 更新人工调整
     * @param {number} analysisId - 分析ID
     * @param {Object} data - 调整数据
     * @returns {Promise} 更新结果
     */
    async updateManualAdjustment(analysisId, data) {
        try {
            const response = await this.client.put(`${this.baseUrl}/requirements/analyses/${analysisId}/adjust`, data);
            if (window.Utils) {
                Utils.success('人工调整保存成功');
            }
            return response.data;
        } catch (error) {
            console.error('保存人工调整失败:', error);
            if (window.Utils) {
                Utils.error('保存人工调整失败: ' + error.message);
            }
            throw error;
        }
    }
    
    // ==================== 文件上传接口 ====================
    
    /**
     * 上传文件
     * @param {File} file - 文件对象
     * @param {Function} onProgress - 进度回调
     * @returns {Promise} 上传结果
     */
    async uploadFile(file, onProgress) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            
            // 创建XMLHttpRequest以支持进度监控
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                
                // 监听上传进度
                xhr.upload.addEventListener('progress', (event) => {
                    if (event.lengthComputable && onProgress) {
                        const progress = Math.round((event.loaded / event.total) * 100);
                        onProgress(progress);
                    }
                });
                
                // 监听响应
                xhr.addEventListener('load', () => {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (error) {
                            resolve({ url: xhr.responseText });
                        }
                    } else {
                        reject(new Error(`上传失败: ${xhr.status} ${xhr.statusText}`));
                    }
                });
                
                // 监听错误
                xhr.addEventListener('error', () => {
                    reject(new Error('上传失败: 网络错误'));
                });
                
                // 发送请求
                xhr.open('POST', `${API_CONFIG.BASE_URL}/upload`);
                xhr.send(formData);
            });
            
        } catch (error) {
            console.error('文件上传失败:', error);
            if (window.Utils) {
                Utils.error('文件上传失败: ' + error.message);
            }
            throw error;
        }
    }
}

// ==================== 创建API实例 ====================
try {
    window.API = new API();
    console.log('✅ API对象创建成功:', window.API);
    console.log('API方法列表:', Object.getOwnPropertyNames(Object.getPrototypeOf(window.API)));
} catch (error) {
    console.error('❌ API对象创建失败:', error);
    window.API = null;
}

// ==================== 导出模块 ====================
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { API, HttpClient };
}

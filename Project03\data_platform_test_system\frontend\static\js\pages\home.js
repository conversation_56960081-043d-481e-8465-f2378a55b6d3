/**
 * 首页JavaScript
 * 负责首页的数据加载和交互
 */

window.HomePage = {
    async init() {
        console.log('初始化首页...');
        await this.loadStatistics();
        await this.loadSystemStatus();
        this.bindEvents();
    },
    
    bindEvents() {
        // 功能卡片点击事件已在main.js中处理
        
        // 定期更新系统状态
        setInterval(() => {
            this.loadSystemStatus();
        }, 30000); // 每30秒更新一次
    },
    
    async loadStatistics() {
        try {
            // 加载需求统计
            const requirementsData = await API.getRequirements({ page: 1, page_size: 1 });
            const totalRequirements = requirementsData.total || 0;
            
            // 加载分析统计
            const analysisData = await API.getAnalysisResults({ page: 1, page_size: 1 });
            const totalAnalyses = analysisData.total || 0;
            
            // 更新统计数字
            this.updateStatistics({
                totalRequirements,
                totalAnalyses
            });
            
        } catch (error) {
            console.error('加载统计数据失败:', error);
            // 设置默认值
            this.updateStatistics({
                totalRequirements: '-',
                totalAnalyses: '-'
            });
        }
    },
    
    updateStatistics(stats) {
        const elements = {
            totalRequirements: document.getElementById('totalRequirements'),
            totalAnalyses: document.getElementById('totalAnalyses')
        };
        
        Object.keys(elements).forEach(key => {
            const element = elements[key];
            if (element) {
                element.textContent = stats[key];
                
                // 添加动画效果
                element.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 200);
            }
        });
    },
    
    async loadSystemStatus() {
        try {
            const status = await API.healthCheck();
            this.updateSystemStatus(status);
            
        } catch (error) {
            console.error('加载系统状态失败:', error);
            this.updateSystemStatus({
                status: 'unhealthy',
                services: {
                    database: { connected: false },
                    ai_service: { status: 'error' }
                },
                version: '未知'
            });
        }
    },
    
    updateSystemStatus(status) {
        const elements = {
            dbStatus: document.getElementById('dbStatus'),
            aiStatus: document.getElementById('aiStatus'),
            systemVersion: document.getElementById('systemVersion'),
            uptime: document.getElementById('uptime')
        };
        
        // 数据库状态
        if (elements.dbStatus) {
            const dbConnected = status.services?.database?.connected;
            elements.dbStatus.textContent = dbConnected ? '正常' : '异常';
            elements.dbStatus.className = `status-value ${dbConnected ? 'success' : 'error'}`;
        }
        
        // AI服务状态
        if (elements.aiStatus) {
            const aiServiceStatus = status.services?.ai_service?.status;
            const statusText = aiServiceStatus === 'ready' ? '就绪' : 
                              aiServiceStatus === 'error' ? '异常' : '未知';
            elements.aiStatus.textContent = statusText;
            elements.aiStatus.className = `status-value ${aiServiceStatus === 'ready' ? 'success' : 'error'}`;
        }
        
        // 系统版本
        if (elements.systemVersion) {
            elements.systemVersion.textContent = status.version || '未知';
        }
        
        // 运行时间
        if (elements.uptime) {
            elements.uptime.textContent = this.calculateUptime();
        }
    },
    
    calculateUptime() {
        // 简单的运行时间计算（从页面加载开始）
        if (!window.pageLoadTime) {
            window.pageLoadTime = Date.now();
        }
        
        const uptime = Date.now() - window.pageLoadTime;
        const minutes = Math.floor(uptime / 60000);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) {
            return `${days}天 ${hours % 24}小时`;
        } else if (hours > 0) {
            return `${hours}小时 ${minutes % 60}分钟`;
        } else {
            return `${minutes}分钟`;
        }
    }
};

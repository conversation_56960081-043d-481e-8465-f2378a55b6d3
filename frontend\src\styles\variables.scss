/**
 * 样式变量定义
 * 定义Gemini风格的设计系统变量
 */

// 主色调 - Gemini风格
$color-primary: #4285f4;
$color-secondary: #34a853;
$color-success: #34a853;
$color-warning: #fbbc04;
$color-danger: #ea4335;
$color-info: #4285f4;

// 渐变色
$primary-gradient: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
$secondary-gradient: linear-gradient(135deg, #34a853 0%, #fbbc04 100%);
$success-gradient: linear-gradient(135deg, #34a853 0%, #0f9d58 100%);
$warning-gradient: linear-gradient(135deg, #fbbc04 0%, #f4b400 100%);
$danger-gradient: linear-gradient(135deg, #ea4335 0%, #d33b2c 100%);

// 背景渐变
$background-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
$card-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);

// 文字颜色
$text-color-primary: #202124;
$text-color-secondary: #5f6368;
$text-color-tertiary: #80868b;
$text-color-disabled: #bdc1c6;
$text-color-white: #ffffff;

// 边框颜色
$border-color-light: #e8eaed;
$border-color-base: #dadce0;
$border-color-dark: #5f6368;

// 背景颜色
$background-color-base: #ffffff;
$background-color-light: #f8f9fa;
$background-color-dark: #202124;

// 字体
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
$font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 32px;
$font-size-4xl: 40px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-relaxed: 1.6;
$line-height-loose: 2;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-base: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-2xl: 48px;
$spacing-3xl: 64px;

// 圆角
$border-radius-sm: 4px;
$border-radius-base: 6px;
$border-radius-medium: 8px;
$border-radius-large: 12px;
$border-radius-xl: 16px;
$border-radius-2xl: 24px;
$border-radius-full: 50%;

// 阴影
$box-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$box-shadow-base: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$box-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
$box-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
$box-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
$box-shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

// Gemini特色阴影
$box-shadow-card: 0 2px 12px rgba(0, 0, 0, 0.1);
$box-shadow-hover: 0 8px 24px rgba(0, 0, 0, 0.15);
$box-shadow-focus: 0 0 0 3px rgba(66, 133, 244, 0.1);

// 过渡动画
$transition-fast: 0.15s ease;
$transition-base: 0.3s ease;
$transition-slow: 0.5s ease;

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 1024px;
$breakpoint-lg: 1280px;
$breakpoint-xl: 1536px;

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 透明度
$opacity-disabled: 0.6;
$opacity-loading: 0.8;
$opacity-overlay: 0.5;

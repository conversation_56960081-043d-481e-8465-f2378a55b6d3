"""
测试API端点
"""

import requests
import json

def test_api_endpoints():
    """测试各个API端点"""
    base_url = "http://localhost:8000"
    
    endpoints = [
        "/api/health",
        "/api/config/api-key",
        "/docs",
        "/"
    ]
    
    print("🔍 测试API端点...")
    
    for endpoint in endpoints:
        url = base_url + endpoint
        try:
            response = requests.get(url, timeout=5)
            print(f"✅ {endpoint}: {response.status_code}")
            if endpoint == "/api/health":
                data = response.json()
                print(f"   状态: {data.get('overall_status', 'unknown')}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {endpoint}: 连接失败 (服务器未运行?)")
        except requests.exceptions.Timeout:
            print(f"⏰ {endpoint}: 超时")
        except Exception as e:
            print(f"❌ {endpoint}: {str(e)}")
    
    # 测试POST端点
    print("\n🔍 测试POST端点...")
    
    # 测试测试用例生成API
    try:
        url = base_url + "/api/generate-test-cases"
        data = {
            'project_name': '测试项目',
            'requirements': '测试需求',
            'context_info': '测试上下文',
            'test_case_count': 3
        }
        
        response = requests.post(url, data=data, timeout=10)
        print(f"✅ /api/generate-test-cases: {response.status_code}")
        
        if response.status_code == 200:
            print("   流式响应开始...")
            # 读取前几行流式响应
            lines = response.text.split('\n')[:5]
            for line in lines:
                if line.strip():
                    print(f"   {line}")
        else:
            print(f"   错误: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ /api/generate-test-cases: {str(e)}")

if __name__ == "__main__":
    test_api_endpoints()

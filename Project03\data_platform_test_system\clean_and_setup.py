#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理历史代码并创建新的项目架构
数据中台智能测试系统 - 全新模块化设计
"""

import os
import shutil
from pathlib import Path

def clean_and_setup():
    """清理历史代码并创建新架构"""
    print("🧹 开始清理历史代码...")
    
    # 当前目录
    current_dir = Path(".")
    
    # 需要保留的文件
    keep_files = {
        "README.md",
        "clean_and_setup.py"
    }
    
    # 删除所有文件和目录（除了保留的）
    for item in current_dir.iterdir():
        if item.name not in keep_files:
            try:
                if item.is_dir():
                    shutil.rmtree(item)
                    print(f"✅ 删除目录: {item.name}")
                else:
                    item.unlink()
                    print(f"✅ 删除文件: {item.name}")
            except Exception as e:
                print(f"❌ 删除失败 {item.name}: {e}")
    
    print("\n🏗️ 创建新的项目架构...")
    
    # 创建新的目录结构
    directories = [
        # 后端目录
        "backend",
        "backend/app",
        "backend/app/api",
        "backend/app/api/v1",
        "backend/app/core",
        "backend/app/models",
        "backend/app/services",
        "backend/app/utils",
        "backend/app/database",
        "backend/uploads",
        "backend/logs",
        
        # 前端目录
        "frontend",
        "frontend/static",
        "frontend/static/css",
        "frontend/static/js",
        "frontend/static/images",
        "frontend/templates",
        "frontend/components",
        
        # 数据库目录
        "database",
        "database/migrations",
        
        # 配置目录
        "config",
        
        # 文档目录
        "docs",
        
        # 测试目录
        "tests",
        "tests/backend",
        "tests/frontend"
    ]
    
    for directory in directories:
        dir_path = current_dir / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")
    
    # 创建Python包初始化文件
    init_files = [
        "backend/__init__.py",
        "backend/app/__init__.py",
        "backend/app/api/__init__.py",
        "backend/app/api/v1/__init__.py",
        "backend/app/core/__init__.py",
        "backend/app/models/__init__.py",
        "backend/app/services/__init__.py",
        "backend/app/utils/__init__.py",
        "backend/app/database/__init__.py"
    ]
    
    for init_file in init_files:
        init_path = current_dir / init_file
        init_path.touch()
        print(f"✅ 创建文件: {init_file}")
    
    print("\n🎉 项目架构创建完成!")
    print("\n📁 新的目录结构:")
    print("├── backend/                 # 后端代码")
    print("│   ├── app/                # 应用核心")
    print("│   │   ├── api/           # API路由")
    print("│   │   ├── core/          # 核心配置")
    print("│   │   ├── models/        # 数据模型")
    print("│   │   ├── services/      # 业务逻辑")
    print("│   │   ├── utils/         # 工具函数")
    print("│   │   └── database/      # 数据库连接")
    print("│   ├── uploads/           # 文件上传")
    print("│   └── logs/              # 日志文件")
    print("├── frontend/               # 前端代码")
    print("│   ├── static/            # 静态资源")
    print("│   ├── templates/         # HTML模板")
    print("│   └── components/        # 组件")
    print("├── database/               # 数据库脚本")
    print("├── config/                 # 配置文件")
    print("├── docs/                   # 文档")
    print("└── tests/                  # 测试代码")

if __name__ == "__main__":
    clean_and_setup()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试后端导入
"""

import os
import sys
from pathlib import Path

# 设置编码
if os.name == 'nt':
    # Windows编码设置
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    try:
        import locale
        locale.setlocale(locale.LC_ALL, '')  # 使用系统默认locale
    except:
        pass

# 添加后端路径
project_root = Path(__file__).parent
backend_dir = project_root / "backend"
sys.path.insert(0, str(backend_dir))

def test_imports():
    """测试导入"""
    print("测试导入模块...")
    
    try:
        print("1. 测试配置导入...")
        from app.core.config import settings
        print(f"   配置加载成功: {settings.APP_NAME}")
        
        print("2. 测试数据库导入...")
        from app.database.connection import db_manager
        print("   数据库模块导入成功")
        
        print("3. 测试AI服务导入...")
        from app.services.ai_service import ai_service
        print("   AI服务模块导入成功")
        
        print("4. 测试API路由导入...")
        from app.api.v1.requirements import router
        print("   API路由导入成功")
        
        print("5. 测试主应用导入...")
        from main import app
        print("   主应用导入成功")
        
        return True
        
    except Exception as e:
        print(f"导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    try:
        from app.core.config import settings
        
        print(f"应用名称: {settings.APP_NAME}")
        print(f"版本: {settings.APP_VERSION}")
        print(f"调试模式: {settings.DEBUG}")
        print(f"数据库URL: {settings.database_url}")
        print(f"DeepSeek配置: {bool(settings.DEEPSEEK_API_KEY)}")
        
        return True
        
    except Exception as e:
        print(f"配置测试失败: {e}")
        return False

def test_database():
    """测试数据库连接"""
    print("\n测试数据库连接...")
    
    try:
        from app.database.connection import db_manager
        
        # 初始化数据库管理器
        if db_manager.initialize():
            print("数据库管理器初始化成功")
            return True
        else:
            print("数据库管理器初始化失败")
            return False
            
    except Exception as e:
        print(f"数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始直接测试后端组件...")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("导入测试失败")
        return False
    
    # 测试配置
    if not test_config():
        print("配置测试失败")
        return False
    
    # 测试数据库
    if not test_database():
        print("数据库测试失败")
        return False
    
    print("=" * 50)
    print("所有测试通过!")
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

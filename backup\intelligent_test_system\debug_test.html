<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI生成调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-case {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-case h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AI生成调试测试</h1>
        <p>直接测试AutoGen + DeepSeek的真实AI生成功能</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="projectName">项目名称:</label>
                <input type="text" id="projectName" value="用户管理系统" required>
            </div>
            
            <div class="form-group">
                <label for="requirements">需求描述:</label>
                <textarea id="requirements" required>实现用户注册、登录、个人信息管理功能。包括用户数据验证、密码加密、会话管理等核心功能。</textarea>
            </div>
            
            <div class="form-group">
                <label for="testCaseCount">测试用例数量:</label>
                <input type="number" id="testCaseCount" value="3" min="1" max="10" required>
            </div>
            
            <button type="submit" id="generateBtn">🚀 生成测试用例</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const generateBtn = document.getElementById('generateBtn');
            const resultDiv = document.getElementById('result');
            
            // 获取表单数据
            const projectName = document.getElementById('projectName').value;
            const requirements = document.getElementById('requirements').value;
            const testCaseCount = document.getElementById('testCaseCount').value;
            
            // 禁用按钮
            generateBtn.disabled = true;
            generateBtn.textContent = '⏳ 生成中...';
            
            // 显示加载状态
            resultDiv.innerHTML = `
                <div class="result">
                    <h3>🤖 正在调用AutoGen + DeepSeek...</h3>
                    <p>项目: ${projectName}</p>
                    <p>需求: ${requirements}</p>
                    <p>数量: ${testCaseCount}</p>
                </div>
            `;
            
            try {
                // 发送请求
                const formData = new FormData();
                formData.append('project_name', projectName);
                formData.append('requirements', requirements);
                formData.append('test_case_count', testCaseCount);
                
                const response = await fetch('/api/debug-generate', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    // 显示成功结果
                    let html = `
                        <div class="result success">
                            <h3>✅ 生成成功！</h3>
                            <p><strong>消息:</strong> ${data.message}</p>
                            <p><strong>AI响应:</strong> ${data.ai_response}</p>
                        </div>
                    `;
                    
                    // 显示测试用例
                    if (data.test_cases && data.test_cases.length > 0) {
                        html += '<h3>📋 生成的测试用例:</h3>';
                        data.test_cases.forEach((testCase, index) => {
                            html += `
                                <div class="test-case">
                                    <h4>测试用例 ${index + 1}: ${testCase.title}</h4>
                                    <p><strong>目标:</strong> ${testCase.objective}</p>
                                    <p><strong>前置条件:</strong> ${testCase.preconditions}</p>
                                    <p><strong>测试步骤:</strong> ${testCase.test_steps}</p>
                                    <p><strong>预期结果:</strong> ${testCase.expected_result}</p>
                                    <p><strong>优先级:</strong> ${testCase.priority} | <strong>类型:</strong> ${testCase.test_type} | <strong>风险:</strong> ${testCase.risk_level}</p>
                                </div>
                            `;
                        });
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    // 显示错误结果
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 生成失败</h3>
                            <p><strong>错误:</strong> ${data.message}</p>
                            <p><strong>AI响应:</strong> ${data.ai_response || '无响应'}</p>
                            <p><strong>提取的JSON:</strong> ${data.extracted_json || '无JSON'}</p>
                            <p><strong>错误详情:</strong> ${data.error_details || '无详情'}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 请求失败</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                    </div>
                `;
            } finally {
                // 恢复按钮
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 生成测试用例';
            }
        });
    </script>
</body>
</html>

"""
API接口管理模块 - 负责API接口的管理和监控
"""

import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from fastapi import Request


class APIInterfaceModule:
    """API接口管理模块"""
    
    def __init__(self):
        self.api_calls_log = []
        self.api_stats = {
            'total_calls': 0,
            'success_calls': 0,
            'error_calls': 0,
            'start_time': datetime.now()
        }
        self.endpoints_info = self._initialize_endpoints_info()
    
    def _initialize_endpoints_info(self) -> Dict[str, Dict[str, Any]]:
        """初始化API端点信息"""
        return {
            '/api/requirement-understanding': {
                'name': '查询需求理解接口',
                'description': '基于AutoGen + DeepSeek的多模态AI需求分析接口',
                'method': 'POST',
                'content_type': 'multipart/form-data',
                'parameters': {
                    'project_name': {
                        'type': 'string',
                        'required': True,
                        'description': '项目名称'
                    },
                    'requirements': {
                        'type': 'string',
                        'required': True,
                        'description': '需求描述'
                    },
                    'context_info': {
                        'type': 'string',
                        'required': False,
                        'description': '技术上下文信息'
                    },
                    'files': {
                        'type': 'file[]',
                        'required': False,
                        'description': '上传的文件（支持图片、PDF、Office文档）'
                    }
                },
                'response_format': 'Server-Sent Events (流式JSON)',
                'ai_model': 'AutoGen + DeepSeek',
                'features': [
                    '多模态文件分析',
                    '结构化需求生成',
                    '流式实时输出',
                    '错误处理和重试'
                ]
            },
            '/api/generate-test-cases': {
                'name': 'AI测试用例生成接口',
                'description': '基于需求描述生成专业测试用例',
                'method': 'POST',
                'content_type': 'multipart/form-data',
                'parameters': {
                    'project_name': {
                        'type': 'string',
                        'required': True,
                        'description': '项目名称'
                    },
                    'requirements': {
                        'type': 'string',
                        'required': True,
                        'description': '需求描述'
                    },
                    'context_info': {
                        'type': 'string',
                        'required': False,
                        'description': '技术上下文信息'
                    },
                    'test_case_count': {
                        'type': 'integer',
                        'required': False,
                        'description': '生成的测试用例数量（默认5个）'
                    },
                    'files': {
                        'type': 'file[]',
                        'required': False,
                        'description': '上传的文件'
                    }
                },
                'response_format': 'Server-Sent Events (流式JSON)',
                'ai_model': 'AutoGen + DeepSeek',
                'features': [
                    'AI专家协作',
                    '专业测试用例',
                    '数量控制',
                    '流式输出'
                ]
            },
            '/api/health': {
                'name': '系统健康检查接口',
                'description': '检查系统各组件的运行状态',
                'method': 'GET',
                'content_type': 'application/json',
                'parameters': {},
                'response_format': 'JSON',
                'features': [
                    '后端服务状态',
                    'AI框架状态',
                    'DeepSeek API状态',
                    '系统信息'
                ]
            },
            '/api/prompts': {
                'name': '提示词管理接口',
                'description': '管理AI专家的提示词配置',
                'method': 'POST',
                'content_type': 'multipart/form-data',
                'parameters': {
                    'requirement_analyst': {
                        'type': 'string',
                        'required': True,
                        'description': '需求分析专家提示词'
                    },
                    'data_test_expert': {
                        'type': 'string',
                        'required': True,
                        'description': '数据测试专家提示词'
                    },
                    'document_analyst': {
                        'type': 'string',
                        'required': True,
                        'description': '文档分析专家提示词'
                    },
                    'requirement_understanding': {
                        'type': 'string',
                        'required': True,
                        'description': '需求智能理解专家提示词'
                    }
                },
                'response_format': 'JSON',
                'features': [
                    '实时更新',
                    '配置持久化',
                    '默认值重置'
                ]
            }
        }
    
    def log_api_call(
        self, 
        endpoint: str, 
        method: str, 
        request: Request, 
        response_status: int, 
        response_time: float,
        error_message: str = None
    ):
        """记录API调用日志"""
        call_log = {
            'timestamp': datetime.now().isoformat(),
            'endpoint': endpoint,
            'method': method,
            'client_ip': request.client.host if request.client else 'unknown',
            'user_agent': request.headers.get('user-agent', 'unknown'),
            'response_status': response_status,
            'response_time_ms': round(response_time * 1000, 2),
            'error_message': error_message
        }
        
        self.api_calls_log.append(call_log)
        
        # 更新统计信息
        self.api_stats['total_calls'] += 1
        if 200 <= response_status < 300:
            self.api_stats['success_calls'] += 1
        else:
            self.api_stats['error_calls'] += 1
        
        # 保持日志大小在合理范围内
        if len(self.api_calls_log) > 1000:
            self.api_calls_log = self.api_calls_log[-500:]
    
    def get_api_documentation(self) -> Dict[str, Any]:
        """获取API文档"""
        return {
            'title': '数据中台智能助手 API 文档',
            'version': '2.0.0',
            'description': '基于AutoGen + DeepSeek的AI驱动数据中台智能助手API',
            'base_url': '/api',
            'endpoints': self.endpoints_info,
            'authentication': 'None (开发环境)',
            'rate_limiting': 'None (开发环境)',
            'error_codes': {
                '200': '请求成功',
                '400': '请求参数错误',
                '500': '服务器内部错误',
                '503': '服务暂时不可用'
            }
        }
    
    def get_endpoint_info(self, endpoint: str) -> Optional[Dict[str, Any]]:
        """获取特定端点信息"""
        return self.endpoints_info.get(endpoint)
    
    def get_api_stats(self) -> Dict[str, Any]:
        """获取API统计信息"""
        uptime = datetime.now() - self.api_stats['start_time']
        
        return {
            **self.api_stats,
            'uptime_seconds': uptime.total_seconds(),
            'uptime_formatted': str(uptime).split('.')[0],
            'success_rate': (
                self.api_stats['success_calls'] / self.api_stats['total_calls'] 
                if self.api_stats['total_calls'] > 0 else 0
            ),
            'error_rate': (
                self.api_stats['error_calls'] / self.api_stats['total_calls'] 
                if self.api_stats['total_calls'] > 0 else 0
            )
        }
    
    def get_recent_calls(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取最近的API调用记录"""
        return self.api_calls_log[-limit:] if self.api_calls_log else []
    
    def get_endpoint_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取各端点的统计信息"""
        endpoint_stats = {}
        
        for log in self.api_calls_log:
            endpoint = log['endpoint']
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {
                    'total_calls': 0,
                    'success_calls': 0,
                    'error_calls': 0,
                    'avg_response_time': 0,
                    'total_response_time': 0
                }
            
            stats = endpoint_stats[endpoint]
            stats['total_calls'] += 1
            stats['total_response_time'] += log['response_time_ms']
            
            if 200 <= log['response_status'] < 300:
                stats['success_calls'] += 1
            else:
                stats['error_calls'] += 1
            
            stats['avg_response_time'] = stats['total_response_time'] / stats['total_calls']
        
        return endpoint_stats
    
    def clear_logs(self):
        """清空API调用日志"""
        self.api_calls_log.clear()
        self.api_stats = {
            'total_calls': 0,
            'success_calls': 0,
            'error_calls': 0,
            'start_time': datetime.now()
        }
    
    def export_logs(self, format: str = 'json') -> str:
        """导出API调用日志"""
        if format.lower() == 'json':
            return json.dumps({
                'export_time': datetime.now().isoformat(),
                'stats': self.get_api_stats(),
                'logs': self.api_calls_log
            }, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def get_module_stats(self) -> Dict[str, Any]:
        """获取模块统计信息"""
        return {
            'module_name': 'APIInterface',
            'total_endpoints': len(self.endpoints_info),
            'total_api_calls': self.api_stats['total_calls'],
            'success_rate': (
                self.api_stats['success_calls'] / self.api_stats['total_calls'] 
                if self.api_stats['total_calls'] > 0 else 0
            ),
            'uptime': str(datetime.now() - self.api_stats['start_time']).split('.')[0]
        }

    def validate_request_parameters(self, endpoint: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证请求参数"""
        endpoint_info = self.get_endpoint_info(endpoint)
        if not endpoint_info:
            return {'valid': False, 'error': f'未知的端点: {endpoint}'}

        required_params = [
            name for name, info in endpoint_info['parameters'].items()
            if info.get('required', False)
        ]

        missing_params = [param for param in required_params if param not in parameters]
        if missing_params:
            return {
                'valid': False,
                'error': f'缺少必需参数: {", ".join(missing_params)}'
            }

        return {'valid': True}

    def generate_api_example(self, endpoint: str) -> Dict[str, Any]:
        """生成API调用示例"""
        endpoint_info = self.get_endpoint_info(endpoint)
        if not endpoint_info:
            return {'error': f'未知的端点: {endpoint}'}

        example = {
            'endpoint': endpoint,
            'method': endpoint_info['method'],
            'content_type': endpoint_info['content_type'],
            'description': endpoint_info['description']
        }

        if endpoint_info['method'] == 'POST':
            if endpoint_info['content_type'] == 'multipart/form-data':
                example['curl_example'] = self._generate_curl_multipart_example(endpoint, endpoint_info)
                example['javascript_example'] = self._generate_js_formdata_example(endpoint, endpoint_info)
            else:
                example['curl_example'] = self._generate_curl_json_example(endpoint, endpoint_info)
                example['javascript_example'] = self._generate_js_json_example(endpoint, endpoint_info)
        else:
            example['curl_example'] = f'curl -X {endpoint_info["method"]} "http://localhost:8000{endpoint}"'
            example['javascript_example'] = f'fetch("http://localhost:8000{endpoint}")'

        return example

    def _generate_curl_multipart_example(self, endpoint: str, endpoint_info: Dict[str, Any]) -> str:
        """生成multipart/form-data的curl示例"""
        curl_parts = [f'curl -X {endpoint_info["method"]}']

        for param_name, param_info in endpoint_info['parameters'].items():
            if param_info['type'] == 'file[]':
                curl_parts.append(f'  -F "{param_name}=@example_file.png"')
            else:
                example_value = self._get_example_value(param_info)
                curl_parts.append(f'  -F "{param_name}={example_value}"')

        curl_parts.append(f'  "http://localhost:8000{endpoint}"')

        return ' \\\n'.join(curl_parts)

    def _generate_curl_json_example(self, endpoint: str, endpoint_info: Dict[str, Any]) -> str:
        """生成JSON的curl示例"""
        example_data = {}
        for param_name, param_info in endpoint_info['parameters'].items():
            example_data[param_name] = self._get_example_value(param_info)

        return f'''curl -X {endpoint_info["method"]} \\
  -H "Content-Type: application/json" \\
  -d '{json.dumps(example_data, ensure_ascii=False)}' \\
  "http://localhost:8000{endpoint}"'''

    def _generate_js_formdata_example(self, endpoint: str, endpoint_info: Dict[str, Any]) -> str:
        """生成FormData的JavaScript示例"""
        js_code = '''const formData = new FormData();
'''

        for param_name, param_info in endpoint_info['parameters'].items():
            if param_info['type'] == 'file[]':
                js_code += f'formData.append("{param_name}", fileInput.files[0]);\n'
            else:
                example_value = self._get_example_value(param_info)
                js_code += f'formData.append("{param_name}", "{example_value}");\n'

        js_code += f'''
fetch("http://localhost:8000{endpoint}", {{
  method: "{endpoint_info["method"]}",
  body: formData
}})
.then(response => response.json())
.then(data => console.log(data));'''

        return js_code

    def _generate_js_json_example(self, endpoint: str, endpoint_info: Dict[str, Any]) -> str:
        """生成JSON的JavaScript示例"""
        example_data = {}
        for param_name, param_info in endpoint_info['parameters'].items():
            example_data[param_name] = self._get_example_value(param_info)

        return f'''fetch("http://localhost:8000{endpoint}", {{
  method: "{endpoint_info["method"]}",
  headers: {{
    "Content-Type": "application/json"
  }},
  body: JSON.stringify({json.dumps(example_data, ensure_ascii=False)})
}})
.then(response => response.json())
.then(data => console.log(data));'''

    def _get_example_value(self, param_info: Dict[str, Any]) -> str:
        """获取参数的示例值"""
        param_type = param_info['type']
        param_name = param_info.get('description', '示例值')

        if param_type == 'string':
            if 'name' in param_name.lower() or '名称' in param_name:
                return '智慧城市数据中台系统'
            elif 'requirements' in param_name.lower() or '需求' in param_name:
                return '实现用户管理、数据分析、报表生成等功能'
            elif 'context' in param_name.lower() or '上下文' in param_name:
                return 'Vue3 + FastAPI + MySQL架构'
            else:
                return '示例文本'
        elif param_type == 'integer':
            return '5'
        elif param_type == 'boolean':
            return 'true'
        else:
            return '示例值'

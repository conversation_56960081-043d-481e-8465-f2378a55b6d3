<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中台智能测试系统 - 工作版本</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
        }
        
        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover, .nav-item.active {
            background: rgba(255,255,255,0.1);
            border-left-color: #fff;
        }
        
        .nav-item i {
            margin-right: 10px;
            width: 20px;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
        }
        
        .page {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page.active {
            display: block;
        }
        
        .page-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .page-header h2 {
            color: #333;
            font-size: 24px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            margin: 5px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .success {
            color: #155724;
            background: #d4edda;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 16px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <h3>数据中台智能测试系统</h3>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active" onclick="showPage('home')">
                    <i class="fas fa-home"></i>
                    首页
                </li>
                <li class="nav-item" onclick="showPage('requirements')">
                    <i class="fas fa-list"></i>
                    需求列表
                </li>
                <li class="nav-item" onclick="showPage('analysis')">
                    <i class="fas fa-brain"></i>
                    分析列表
                </li>
                <li class="nav-item" onclick="showPage('create')">
                    <i class="fas fa-plus"></i>
                    需求录入
                </li>
            </ul>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 首页 -->
            <div id="home" class="page active">
                <div class="page-header">
                    <h2>系统首页</h2>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalRequirements">-</div>
                        <div class="stat-label">总需求数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalAnalyses">-</div>
                        <div class="stat-label">分析结果数</div>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="loadHomeData()">
                    <i class="fas fa-refresh"></i> 刷新数据
                </button>
                
                <div id="systemStatus"></div>
            </div>
            
            <!-- 需求列表 -->
            <div id="requirements" class="page">
                <div class="page-header">
                    <h2>需求列表</h2>
                </div>
                
                <button class="btn btn-primary" onclick="loadRequirements()">
                    <i class="fas fa-refresh"></i> 刷新列表
                </button>
                
                <div id="requirementsContent">
                    <div class="loading">点击刷新按钮加载需求列表</div>
                </div>
            </div>
            
            <!-- 分析列表 -->
            <div id="analysis" class="page">
                <div class="page-header">
                    <h2>分析列表</h2>
                </div>
                
                <button class="btn btn-primary" onclick="loadAnalysis()">
                    <i class="fas fa-refresh"></i> 刷新列表
                </button>
                
                <div id="analysisContent">
                    <div class="loading">点击刷新按钮加载分析列表</div>
                </div>
            </div>
            
            <!-- 需求录入 -->
            <div id="create" class="page">
                <div class="page-header">
                    <h2>需求录入</h2>
                </div>
                
                <form id="requirementForm">
                    <div style="margin-bottom: 20px;">
                        <label>需求标题：</label><br>
                        <input type="text" id="title" style="width: 100%; padding: 10px; margin-top: 5px;" required>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <label>需求描述：</label><br>
                        <textarea id="description" rows="5" style="width: 100%; padding: 10px; margin-top: 5px;"></textarea>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <label>优先级：</label><br>
                        <select id="priority" style="width: 200px; padding: 10px; margin-top: 5px;">
                            <option value="低">低</option>
                            <option value="中" selected>中</option>
                            <option value="高">高</option>
                        </select>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <label>创建者：</label><br>
                        <input type="text" id="createdBy" style="width: 300px; padding: 10px; margin-top: 5px;">
                    </div>
                    
                    <button type="button" class="btn btn-success" onclick="createRequirement()">
                        <i class="fas fa-save"></i> 保存需求
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        const API_PREFIX = '/api/v1';
        
        // 页面切换
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 隐藏所有导航项的active状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            event.target.classList.add('active');
            
            // 自动加载数据
            if (pageId === 'home') {
                loadHomeData();
            }
        }
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle"></i> ${message}`;
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }
        
        // 加载首页数据
        async function loadHomeData() {
            try {
                // 加载需求统计
                const reqResponse = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/?page=1&page_size=1`);
                const reqData = await reqResponse.json();
                document.getElementById('totalRequirements').textContent = reqData.total || 0;
                
                // 加载分析统计
                const analysisResponse = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/analyses/?page=1&page_size=1`);
                const analysisData = await analysisResponse.json();
                document.getElementById('totalAnalyses').textContent = analysisData.total || 0;
                
                // 加载系统状态
                const statusResponse = await fetch(`${API_BASE_URL}/health`);
                const statusData = await statusResponse.json();
                
                const statusDiv = document.getElementById('systemStatus');
                statusDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ 系统状态正常</h3>
                        <p>数据库: ${statusData.services?.database?.connected ? '正常' : '异常'}</p>
                        <p>AI服务: ${statusData.services?.ai_service?.status || '未知'}</p>
                    </div>
                `;
                
                showMessage('首页数据加载成功', 'success');
            } catch (error) {
                console.error('加载首页数据失败:', error);
                showMessage('加载首页数据失败', 'error');
            }
        }
        
        // 加载需求列表
        async function loadRequirements() {
            const container = document.getElementById('requirementsContent');
            container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> 正在加载需求列表...</div>';
            
            try {
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/?page=1&page_size=20`);
                const data = await response.json();
                
                if (data.requirements && data.requirements.length > 0) {
                    let html = `
                        <div class="success">✅ 需求列表加载成功，共 ${data.total} 条记录</div>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>标题</th>
                                    <th>优先级</th>
                                    <th>状态</th>
                                    <th>创建者</th>
                                    <th>创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    data.requirements.forEach(req => {
                        html += `
                            <tr>
                                <td>${req.id}</td>
                                <td>${req.title}</td>
                                <td><span style="padding: 4px 8px; border-radius: 4px; background: #007bff; color: white;">${req.priority}</span></td>
                                <td><span style="padding: 4px 8px; border-radius: 4px; background: #28a745; color: white;">${req.status}</span></td>
                                <td>${req.created_by || '-'}</td>
                                <td>${new Date(req.created_time).toLocaleString()}</td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table>';
                    container.innerHTML = html;
                    showMessage('需求列表加载成功', 'success');
                } else {
                    container.innerHTML = '<div class="loading">暂无需求数据</div>';
                    showMessage('暂无需求数据', 'info');
                }
            } catch (error) {
                console.error('加载需求列表失败:', error);
                container.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
                showMessage('加载需求列表失败', 'error');
            }
        }
        
        // 加载分析列表
        async function loadAnalysis() {
            const container = document.getElementById('analysisContent');
            container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> 正在加载分析列表...</div>';
            
            try {
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/analyses/?page=1&page_size=20`);
                const data = await response.json();
                
                if (data.analyses && data.analyses.length > 0) {
                    let html = `
                        <div class="success">✅ 分析列表加载成功，共 ${data.total} 条记录</div>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>需求ID</th>
                                    <th>AI模型</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    data.analyses.forEach(analysis => {
                        html += `
                            <tr>
                                <td>${analysis.id}</td>
                                <td>${analysis.req_id}</td>
                                <td><span style="padding: 4px 8px; border-radius: 4px; background: #6f42c1; color: white;">${analysis.ai_model}</span></td>
                                <td><span style="padding: 4px 8px; border-radius: 4px; background: #17a2b8; color: white;">${analysis.analysis_status}</span></td>
                                <td>${new Date(analysis.created_time).toLocaleString()}</td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table>';
                    container.innerHTML = html;
                    showMessage('分析列表加载成功', 'success');
                } else {
                    container.innerHTML = '<div class="loading">暂无分析数据</div>';
                    showMessage('暂无分析数据', 'info');
                }
            } catch (error) {
                console.error('加载分析列表失败:', error);
                container.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
                showMessage('加载分析列表失败', 'error');
            }
        }
        
        // 创建需求
        async function createRequirement() {
            const title = document.getElementById('title').value.trim();
            const description = document.getElementById('description').value.trim();
            const priority = document.getElementById('priority').value;
            const createdBy = document.getElementById('createdBy').value.trim();
            
            if (!title) {
                showMessage('请输入需求标题', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title,
                        description,
                        priority,
                        created_by: createdBy,
                        modules: []
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showMessage(`需求创建成功，ID: ${data.id}`, 'success');
                    
                    // 清空表单
                    document.getElementById('requirementForm').reset();
                    
                    // 跳转到需求列表
                    showPage('requirements');
                    loadRequirements();
                } else {
                    throw new Error(`创建失败: ${response.status}`);
                }
            } catch (error) {
                console.error('创建需求失败:', error);
                showMessage('创建需求失败', 'error');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            console.log('页面加载完成');
            loadHomeData();
        });
    </script>
</body>
</html>

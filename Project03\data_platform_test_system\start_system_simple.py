#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化系统启动脚本
分别启动后端和前端
"""

import os
import sys
import subprocess
import time
import socket
from pathlib import Path

def check_port(port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result == 0
    except:
        return False

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    if not backend_dir.exists():
        print("❌ 后端目录不存在")
        return None
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONPATH'] = str(backend_dir)
    
    try:
        # 启动后端
        process = subprocess.Popen(
            [sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"],
            cwd=backend_dir,
            env=env
        )
        
        # 等待后端启动
        print("等待后端服务启动...")
        for i in range(30):
            if check_port(8000):
                print("✅ 后端服务启动成功: http://localhost:8000")
                return process
            time.sleep(1)
            print(f"等待中... ({i+1}/30)")
        
        print("❌ 后端服务启动超时")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def main():
    """主函数"""
    print("数据中台智能测试系统 - 简化启动")
    print("=" * 50)
    
    # 启动后端
    backend_process = start_backend()
    if not backend_process:
        print("❌ 后端启动失败，退出")
        return False
    
    print("\n" + "=" * 50)
    print("✅ 后端启动成功！")
    print("📋 现在请手动启动前端：")
    print("")
    print("方法1 - 使用Python脚本：")
    print("  python start_frontend.py")
    print("")
    print("方法2 - 使用命令行：")
    print("  cd frontend")
    print("  python -m http.server 3000")
    print("")
    print("方法3 - 直接用浏览器打开：")
    print("  file:///D:/Project/Python_project/Project03/Project03/data_platform_test_system/frontend/index.html")
    print("")
    print("🌐 后端API地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("💊 健康检查: http://localhost:8000/health")
    print("")
    print("按 Ctrl+C 停止后端服务")
    print("=" * 50)
    
    try:
        # 保持后端运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止后端服务...")
        if backend_process:
            backend_process.terminate()
            backend_process.wait()
        print("✅ 后端服务已停止")
        return True

if __name__ == "__main__":
    main()

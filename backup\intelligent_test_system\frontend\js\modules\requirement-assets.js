/**
 * 需求资产库模块 - 负责需求资产的管理和展示
 */

class RequirementAssetsModule {
    constructor() {
        this.assets = {
            tasks: {},
            latest_task_id: null
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        // 注册到导航模块
        if (window.navigationModule) {
            window.navigationModule.pageHandlers.set('requirementAssets', () => this.loadAssets());
        }
    }

    setupEventListeners() {
        // 刷新按钮
        const refreshBtn = document.querySelector('[onclick="loadRequirementAssets()"]');
        if (refreshBtn) {
            refreshBtn.onclick = () => this.loadAssets();
        }

        // 导出最新按钮
        const exportBtn = document.querySelector('[onclick="exportLatestRequirements()"]');
        if (exportBtn) {
            exportBtn.onclick = () => this.exportLatest();
        }
    }

    saveAsset(result) {
        // 保存需求资产
        this.assets.tasks[result.task_id] = {
            task_id: result.task_id,
            structured_requirements: result.structured_requirements,
            metadata: {
                project_name: result.project_name,
                requirements: result.requirements,
                context_info: result.context_info,
                ai_framework: result.ai_framework,
                generation_time: result.generation_time
            }
        };
        this.assets.latest_task_id = result.task_id;
        
        // 持久化到本地存储
        this.saveToLocalStorage();
    }

    saveToLocalStorage() {
        try {
            localStorage.setItem('requirement_assets', JSON.stringify(this.assets));
        } catch (error) {
            console.error('保存需求资产到本地存储失败:', error);
        }
    }

    loadFromLocalStorage() {
        try {
            const stored = localStorage.getItem('requirement_assets');
            if (stored) {
                this.assets = JSON.parse(stored);
            }
        } catch (error) {
            console.error('从本地存储加载需求资产失败:', error);
        }
    }

    async loadAssets() {
        const container = document.getElementById('requirementAssetsContent');
        
        try {
            // 从本地存储加载
            this.loadFromLocalStorage();
            
            // 这里可以调用后端API获取需求资产
            // const response = await fetch('/api/requirement-assets');
            // const data = await response.json();
            
            const assets = Object.values(this.assets.tasks);
            
            if (assets.length === 0) {
                this.renderEmptyState(container);
                return;
            }
            
            this.renderAssetsList(container, assets);
            
        } catch (error) {
            this.renderErrorState(container, error);
        }
    }

    renderEmptyState(container) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: var(--text-secondary);">
                <div style="font-size: 48px; margin-bottom: 16px;">📚</div>
                <p>暂无需求资产，请先进行查询需求理解</p>
                <button class="btn btn-primary" onclick="showPage('queryUnderstanding')" style="margin-top: 16px;">
                    <span>🔍</span>
                    开始查询需求理解
                </button>
            </div>
        `;
    }

    renderAssetsList(container, assets) {
        let html = `
            <div style="margin-bottom: 24px;">
                <h3 style="color: var(--text-primary); margin-bottom: 16px;">需求资产列表 (${assets.length}个)</h3>
            </div>
        `;
        
        assets.forEach((asset, index) => {
            const metadata = asset.metadata;
            html += `
                <div class="requirement-asset-item" style="background: var(--bg-white); border: 1px solid var(--border-light); border-radius: 8px; padding: 20px; margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;">
                        <div>
                            <h4 style="color: var(--text-primary); margin-bottom: 8px;">${metadata.project_name}</h4>
                            <div style="display: flex; gap: 12px; font-size: 14px; color: var(--text-secondary);">
                                <span>📅 ${new Date(metadata.generation_time).toLocaleString()}</span>
                                <span>🤖 ${metadata.ai_framework}</span>
                            </div>
                        </div>
                        <div style="display: flex; gap: 8px;">
                            <button class="btn" style="background: var(--primary-blue); color: white; padding: 6px 12px; font-size: 12px;" onclick="requirementAssetsModule.viewAsset('${asset.task_id}')">
                                <span>👁️</span>
                                查看详情
                            </button>
                            <button class="btn" style="background: var(--success-green); color: white; padding: 6px 12px; font-size: 12px;" onclick="requirementAssetsModule.exportAsset('${asset.task_id}')">
                                <span>📥</span>
                                导出
                            </button>
                            <button class="btn" style="background: var(--error-red); color: white; padding: 6px 12px; font-size: 12px;" onclick="requirementAssetsModule.deleteAsset('${asset.task_id}')">
                                <span>🗑️</span>
                                删除
                            </button>
                        </div>
                    </div>
                    <div style="color: var(--text-secondary); font-size: 14px; line-height: 1.5;">
                        ${metadata.requirements.substring(0, 200)}${metadata.requirements.length > 200 ? '...' : ''}
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    renderErrorState(container, error) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: var(--error-red);">
                <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
                <p>加载需求资产失败: ${error.message}</p>
                <button class="btn btn-primary" onclick="requirementAssetsModule.loadAssets()" style="margin-top: 16px;">
                    <span>🔄</span>
                    重新加载
                </button>
            </div>
        `;
    }

    viewAsset(taskId) {
        const asset = this.assets.tasks[taskId];
        if (!asset) {
            alert('需求资产不存在');
            return;
        }
        
        // 切换到查询需求理解页面并显示详情
        showPage('queryUnderstanding');
        setTimeout(() => {
            if (window.requirementModule) {
                window.requirementModule.displayStructuredRequirements(
                    asset.structured_requirements, 
                    asset.metadata
                );
            }
        }, 100);
    }

    async exportAsset(taskId) {
        try {
            // 这里可以调用后端API导出
            alert('需求资产导出功能开发中，敬请期待！');
        } catch (error) {
            alert(`导出失败: ${error.message}`);
        }
    }

    async deleteAsset(taskId) {
        if (!confirm('确定要删除这个需求资产吗？')) {
            return;
        }
        
        try {
            delete this.assets.tasks[taskId];
            if (this.assets.latest_task_id === taskId) {
                // 重新设置最新的任务ID
                const remainingTasks = Object.keys(this.assets.tasks);
                this.assets.latest_task_id = remainingTasks.length > 0 ? remainingTasks[remainingTasks.length - 1] : null;
            }
            
            this.saveToLocalStorage();
            this.loadAssets(); // 重新加载列表
            
        } catch (error) {
            alert(`删除失败: ${error.message}`);
        }
    }

    async exportLatest() {
        if (!this.assets.latest_task_id) {
            alert('没有可导出的需求资产');
            return;
        }
        
        await this.exportAsset(this.assets.latest_task_id);
    }

    // 获取资产统计信息
    getStats() {
        const assets = Object.values(this.assets.tasks);
        return {
            total: assets.length,
            latest: this.assets.latest_task_id,
            projects: [...new Set(assets.map(a => a.metadata.project_name))].length
        };
    }
}

// 全局实例
window.requirementAssetsModule = new RequirementAssetsModule();

// 向后兼容的全局函数
function loadRequirementAssets() {
    window.requirementAssetsModule.loadAssets();
}

function exportLatestRequirements() {
    window.requirementAssetsModule.exportLatest();
}

function viewRequirementAsset(taskId) {
    window.requirementAssetsModule.viewAsset(taskId);
}

function exportRequirementAsset(taskId) {
    window.requirementAssetsModule.exportAsset(taskId);
}

<!--
  数据中台智能测试平台 - 主应用组件
  采用Gemini风格的现代化设计
-->

<template>
  <div id="app" class="app-container">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <div class="logo-icon">
            <el-icon size="32" color="#4285f4">
              <DataAnalysis />
            </el-icon>
          </div>
          <h1 class="app-title">数据中台智能测试平台</h1>
        </div>
        
        <nav class="nav-section">
          <el-menu
            mode="horizontal"
            :default-active="$route.path"
            class="nav-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item index="/">
              <el-icon><House /></el-icon>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/generate">
              <el-icon><Magic /></el-icon>
              <span>生成测试用例</span>
            </el-menu-item>
            <el-menu-item index="/history">
              <el-icon><Document /></el-icon>
              <span>历史记录</span>
            </el-menu-item>
          </el-menu>
        </nav>
        
        <div class="user-section">
          <el-button type="primary" :icon="User" circle />
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="app-main">
      <div class="main-content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </main>

    <!-- 底部信息 -->
    <footer class="app-footer">
      <div class="footer-content">
        <p>&copy; 2024 数据中台智能测试平台. 基于AI技术驱动的智能测试解决方案</p>
        <div class="footer-links">
          <a href="#" class="footer-link">帮助文档</a>
          <a href="#" class="footer-link">API文档</a>
          <a href="#" class="footer-link">联系我们</a>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { DataAnalysis, House, Magic, Document, User } from '@element-plus/icons-vue'

const router = useRouter()

/**
 * 处理菜单选择事件
 * @param index 菜单项索引
 */
const handleMenuSelect = (index: string) => {
  router.push(index)
}
</script>

<style lang="scss" scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .logo-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
    }

    .app-title {
      font-size: 20px;
      font-weight: 600;
      color: #1a73e8;
      margin: 0;
      background: linear-gradient(135deg, #1a73e8 0%, #34a853 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .nav-section {
    flex: 1;
    display: flex;
    justify-content: center;

    .nav-menu {
      border: none;
      background: transparent;

      :deep(.el-menu-item) {
        border-radius: 8px;
        margin: 0 4px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(66, 133, 244, 0.1);
          color: #1a73e8;
        }

        &.is-active {
          background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
          color: white;
          box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
        }
      }
    }
  }

  .user-section {
    display: flex;
    align-items: center;
  }
}

.app-main {
  flex: 1;
  padding: 24px;

  .main-content {
    max-width: 1200px;
    margin: 0 auto;
  }
}

.app-footer {
  background: rgba(255, 255, 255, 0.9);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 24px;

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #666;
    font-size: 14px;

    .footer-links {
      display: flex;
      gap: 24px;

      .footer-link {
        color: #666;
        text-decoration: none;
        transition: color 0.3s ease;

        &:hover {
          color: #1a73e8;
        }
      }
    }
  }
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header {
    .header-content {
      padding: 0 16px;
      flex-direction: column;
      height: auto;
      padding-top: 12px;
      padding-bottom: 12px;
    }

    .logo-section {
      margin-bottom: 12px;
    }

    .nav-section {
      width: 100%;
    }

    .user-section {
      position: absolute;
      top: 12px;
      right: 16px;
    }
  }

  .app-main {
    padding: 16px;
  }

  .app-footer {
    .footer-content {
      flex-direction: column;
      gap: 12px;
      text-align: center;
    }
  }
}
</style>

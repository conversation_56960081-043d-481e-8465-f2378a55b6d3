#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试后端启动脚本
用于调试后端启动问题
"""

import os
import sys
import subprocess
from pathlib import Path

# 设置编码
if os.name == 'nt':
    os.system('chcp 65001 > nul')

def test_backend():
    """测试后端启动"""
    print("测试后端启动...")
    
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    main_file = backend_dir / "main.py"
    
    if not main_file.exists():
        print(f"错误: 后端主文件不存在: {main_file}")
        return False
    
    print(f"后端目录: {backend_dir}")
    print(f"主文件: {main_file}")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONPATH'] = str(backend_dir)
    
    try:
        # 直接运行后端
        print("启动后端服务...")
        result = subprocess.run(
            [sys.executable, "main.py"],
            cwd=backend_dir,
            env=env,
            capture_output=True,
            text=True,
            timeout=30,
            encoding='utf-8',
            errors='ignore'  # 忽略编码错误
        )
        
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("后端启动超时")
        return False
    except Exception as e:
        print(f"启动失败: {e}")
        return False

if __name__ == "__main__":
    success = test_backend()
    print(f"测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)

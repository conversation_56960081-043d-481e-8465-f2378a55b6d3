/**
 * 全局样式文件
 * 定义Gemini风格的设计系统和通用样式
 */

// 导入变量
@import './variables.scss';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.6;
}

body {
  font-family: $font-family-base;
  color: $text-color-primary;
  background: $background-gradient;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.p-16 {
  padding: 16px;
}

.p-24 {
  padding: 24px;
}

// Gemini风格卡片
.gemini-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: $border-radius-large;
  box-shadow: $box-shadow-card;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow-hover;
  }
}

// Gemini风格按钮
.gemini-button {
  background: $primary-gradient;
  border: none;
  border-radius: $border-radius-medium;
  color: white;
  font-weight: 500;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  &.secondary {
    background: $secondary-gradient;
  }

  &.success {
    background: $success-gradient;
  }

  &.warning {
    background: $warning-gradient;
  }

  &.danger {
    background: $danger-gradient;
  }
}

// 输入框样式
.gemini-input {
  border: 2px solid transparent;
  border-radius: $border-radius-medium;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;

  &:focus {
    border-color: $color-primary;
    box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
    outline: none;
  }
}

// 加载动画
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(66, 133, 244, 0.1);
  border-top: 4px solid $color-primary;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 渐变文字
.gradient-text {
  background: $primary-gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

// 毛玻璃效果
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// 响应式网格
.grid {
  display: grid;
  gap: 24px;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
}

// Element Plus 样式覆盖
.el-button {
  border-radius: $border-radius-medium;
  font-weight: 500;
  transition: all 0.3s ease;

  &.el-button--primary {
    background: $primary-gradient;
    border: none;
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(66, 133, 244, 0.4);
    }
  }
}

.el-card {
  border-radius: $border-radius-large;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: $box-shadow-card;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow-hover;
  }
}

.el-input__wrapper {
  border-radius: $border-radius-medium;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.el-upload {
  .el-upload-dragger {
    border-radius: $border-radius-large;
    border: 2px dashed rgba(66, 133, 244, 0.3);
    background: rgba(66, 133, 244, 0.05);
    transition: all 0.3s ease;

    &:hover {
      border-color: $color-primary;
      background: rgba(66, 133, 244, 0.1);
    }
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(66, 133, 244, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;

  &:hover {
    background: rgba(66, 133, 244, 0.5);
  }
}

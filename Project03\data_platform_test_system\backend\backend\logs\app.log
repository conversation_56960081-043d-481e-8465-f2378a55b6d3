2025-06-10 07:35:50,355 - __main__ - INFO - ✅ 静态文件服务已配置
2025-06-10 07:36:43,198 - __main__ - INFO - ✅ 静态文件服务已配置
2025-06-10 07:42:11,046 - __main__ - INFO - ✅ 静态文件服务已配置
2025-06-10 07:42:28,515 - __main__ - INFO - ✅ 静态文件服务已配置
2025-06-10 11:06:55,777 - __main__ - INFO - 静态文件服务已配置
2025-06-10 11:25:23,248 - __main__ - INFO - 静态文件服务已配置
2025-06-10 11:26:16,187 - __main__ - INFO - 静态文件服务已配置
2025-06-10 11:26:46,807 - __main__ - INFO - 静态文件服务已配置
2025-06-10 11:31:30,512 - __main__ - INFO - 静态文件服务已配置
2025-06-10 11:31:30,512 - __main__ - INFO - 模板引擎已配置
2025-06-10 11:31:30,534 - __main__ - INFO - 准备启动服务器...
2025-06-10 11:31:30,534 - __main__ - INFO - 服务地址: http://0.0.0.0:8000
2025-06-10 11:31:30,534 - __main__ - INFO - API文档: http://0.0.0.0:8000/docs
2025-06-10 11:31:30,534 - __main__ - INFO - 调试模式: 开启
2025-06-10 11:31:33,242 - __mp_main__ - INFO - 静态文件服务已配置
2025-06-10 11:31:33,243 - __mp_main__ - INFO - 模板引擎已配置
2025-06-10 11:31:33,306 - main - INFO - 静态文件服务已配置
2025-06-10 11:31:33,307 - main - INFO - 模板引擎已配置
2025-06-10 11:31:33,312 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 11:31:33,312 - main - INFO - 初始化系统配置...
2025-06-10 11:31:33,313 - main - INFO - 初始化数据库连接...
2025-06-10 11:31:33,313 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 11:31:33,380 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 11:31:33,404 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 11:31:33,404 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:31:33,405 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 11:31:33,406 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:31:33,406 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 11:31:33,406 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:31:33,407 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:31:33,408 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 11:31:33,408 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] ()
2025-06-10 11:31:33,408 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 11:31:33,408 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 11:31:33,409 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:31:33,409 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 11:31:57,736 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:31:57,741 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 11:31:57,742 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:31:57,748 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 11:31:57,750 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 11:31:57,750 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 11:31:57,750 - main - INFO - 检查AI服务状态...
2025-06-10 11:31:57,750 - main - INFO - AI服务就绪
2025-06-10 11:31:57,750 - main - INFO - 系统启动完成
2025-06-10 11:31:57,750 - main - INFO - 系统信息:
2025-06-10 11:31:57,750 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 11:31:57,750 - main - INFO -   - 版本: 2.0.0
2025-06-10 11:31:57,750 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 11:31:57,750 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 11:31:57,750 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 11:31:57,750 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 11:34:07,302 - main - INFO - 静态文件服务已配置
2025-06-10 11:34:07,306 - main - INFO - 模板引擎已配置
2025-06-10 11:34:07,322 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 11:34:07,323 - main - INFO - 初始化系统配置...
2025-06-10 11:34:07,337 - main - INFO - 初始化数据库连接...
2025-06-10 11:34:07,337 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 11:34:07,419 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 11:34:07,436 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 11:34:07,437 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:34:07,439 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 11:34:07,439 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:34:07,441 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 11:34:07,441 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:34:07,442 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:34:07,443 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 11:34:07,449 - sqlalchemy.engine.Engine - INFO - [generated in 0.00564s] ()
2025-06-10 11:34:07,450 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 11:34:07,450 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 11:34:07,452 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:34:07,452 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 11:34:07,453 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:34:07,455 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 11:34:07,456 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 11:34:07,458 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 11:34:07,462 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 11:34:07,466 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 11:34:07,467 - main - INFO - 检查AI服务状态...
2025-06-10 11:34:07,467 - main - INFO - AI服务就绪
2025-06-10 11:34:07,468 - main - INFO - 系统启动完成
2025-06-10 11:34:07,468 - main - INFO - 系统信息:
2025-06-10 11:34:07,468 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 11:34:07,468 - main - INFO -   - 版本: 2.0.0
2025-06-10 11:34:07,469 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 11:34:07,470 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 11:34:07,470 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 11:34:07,471 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 11:35:07,826 - app.services.requirement_service - INFO -  获取需求列表: 页码=1, 大小=20
2025-06-10 11:35:07,828 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:35:07,836 - sqlalchemy.engine.Engine - INFO - SELECT count(requirements.id) AS count_1 
FROM requirements
2025-06-10 11:35:07,836 - sqlalchemy.engine.Engine - INFO - [generated in 0.00044s] ()
2025-06-10 11:35:07,841 - sqlalchemy.engine.Engine - INFO - SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s
2025-06-10 11:35:07,841 - sqlalchemy.engine.Engine - INFO - [generated in 0.00028s] (0, 20)
2025-06-10 11:35:07,842 - app.services.requirement_service - ERROR -  获取需求列表失败: (pymysql.err.OperationalError) (1054, "Unknown column 'requirements.modules' in 'field list'")
[SQL: SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s]
[parameters: (0, 20)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-10 11:35:07,842 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 11:35:07,842 - app.database.connection - ERROR - 异步数据库会话错误: (pymysql.err.OperationalError) (1054, "Unknown column 'requirements.modules' in 'field list'")
[SQL: SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s]
[parameters: (0, 20)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-10 11:35:07,843 - main - ERROR - 未处理的异常: 'NoneType' object has no attribute 'HTTP_500_INTERNAL_SERVER_ERROR'
Traceback (most recent call last):
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\aiomysql.py", line 97, in execute
    return self.await_(self._execute_async(operation, parameters))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\aiomysql.py", line 106, in _execute_async
    result = await self._cursor.execute(operation, parameters)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\cursors.py", line 239, in execute
    await self._query(query)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\cursors.py", line 457, in _query
    await conn.query(q)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 469, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 683, in _read_query_result
    await result.read()
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 1164, in read
    first_packet = await self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 652, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'requirements.modules' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Project\Python_project\Project03\Project03\data_platform_test_system\backend\app\api\v1\requirements.py", line 175, in get_requirements
    requirements, total = await requirement_service.get_requirements_list(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "D:\Project\Python_project\Project03\Project03\data_platform_test_system\backend\app\services\requirement_service.py", line 178, in get_requirements_list
    result = await session.execute(stmt)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 463, in execute
    result = await greenlet_spawn(
             ^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ~~~~~~~~~~~~~~~~~~~~~~^
        statement,
        ^^^^^^^^^^
    ...<4 lines>...
        _add_event=_add_event,
        ^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self,
        ^^^^^
    ...<4 lines>...
        conn,
        ^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
        statement, params or {}, execution_options=execution_options
    )
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\aiomysql.py", line 97, in execute
    return self.await_(self._execute_async(operation, parameters))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\sqlalchemy\dialects\mysql\aiomysql.py", line 106, in _execute_async
    result = await self._cursor.execute(operation, parameters)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\cursors.py", line 239, in execute
    await self._query(query)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\cursors.py", line 457, in _query
    await conn.query(q)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 469, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 683, in _read_query_result
    await result.read()
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 1164, in read
    first_packet = await self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\aiomysql\connection.py", line 652, in _read_packet
    packet.raise_for_error()
    ~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'requirements.modules' in 'field list'")
[SQL: SELECT requirements.id, requirements.title, requirements.description, requirements.priority, requirements.status, requirements.modules, requirements.attachments, requirements.created_by, requirements.created_time, requirements.updated_time, requirements.is_complete, requirements.missing_fields 
FROM requirements ORDER BY requirements.created_time DESC 
 LIMIT %s, %s]
[parameters: (0, 20)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\middleware\cors.py", line 85, in __call__
    await self.app(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "D:\Project\Python_project\Project03\.venv\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\Python_project\Project03\Project03\data_platform_test_system\backend\app\api\v1\requirements.py", line 214, in get_requirements
    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'HTTP_500_INTERNAL_SERVER_ERROR'
2025-06-10 11:35:29,866 - app.services.requirement_service - INFO -  获取分析结果列表: 页码=1, 大小=20
2025-06-10 11:35:29,872 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 11:35:29,872 - sqlalchemy.engine.Engine - INFO - SELECT count(ai_analysis_results.id) AS count_1 
FROM ai_analysis_results
2025-06-10 11:35:29,872 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ()
2025-06-10 11:35:29,877 - sqlalchemy.engine.Engine - INFO - SELECT ai_analysis_results.id, ai_analysis_results.req_id, ai_analysis_results.analysis_result, ai_analysis_results.structured_result, ai_analysis_results.key_points, ai_analysis_results.risk_assessment, ai_analysis_results.suggestions, ai_analysis_results.manual_adjust, ai_analysis_results.is_manually_adjusted, ai_analysis_results.adjusted_by, ai_analysis_results.adjusted_time, ai_analysis_results.ai_model, ai_analysis_results.ai_version, ai_analysis_results.analysis_config, ai_analysis_results.analysis_status, ai_analysis_results.error_message, ai_analysis_results.processing_time, ai_analysis_results.created_time, ai_analysis_results.update_time 
FROM ai_analysis_results ORDER BY ai_analysis_results.created_time DESC 
 LIMIT %s, %s
2025-06-10 11:35:29,877 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] (0, 20)
2025-06-10 11:35:29,878 - app.services.requirement_service - INFO -  获取分析结果列表成功: 返回0条记录，总数=0
2025-06-10 11:35:29,878 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-06-10 11:37:42,183 - main - INFO - 正在关闭系统...
2025-06-10 11:37:42,184 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 11:37:42,184 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 11:37:42,185 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 11:37:42,185 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 11:37:42,185 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 11:37:42,185 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 11:37:42,185 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 11:37:42,185 - main - INFO - 数据库连接已关闭
2025-06-10 11:37:42,186 - main - INFO - 系统已关闭
2025-06-10 11:37:42,217 - main - INFO - 正在关闭系统...
2025-06-10 11:37:42,218 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 11:37:42,243 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 11:37:42,250 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 11:37:42,250 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 11:37:42,252 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 11:37:42,255 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 11:37:42,255 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 11:37:42,256 - main - INFO - 数据库连接已关闭
2025-06-10 11:37:42,256 - main - INFO - 系统已关闭
2025-06-10 12:00:42,080 - main - INFO - 静态文件服务已配置
2025-06-10 12:00:42,080 - main - INFO - 模板引擎已配置
2025-06-10 12:00:42,090 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 12:00:42,090 - main - INFO - 初始化系统配置...
2025-06-10 12:00:42,106 - main - INFO - 初始化数据库连接...
2025-06-10 12:00:42,106 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 12:00:42,189 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 12:00:42,206 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 12:00:42,208 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:00:42,209 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 12:00:42,209 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:00:42,210 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 12:00:42,216 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:00:42,217 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:00:42,218 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 12:00:42,218 - sqlalchemy.engine.Engine - INFO - [generated in 0.00042s] ()
2025-06-10 12:00:42,219 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 12:00:42,219 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:00:42,221 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:00:42,225 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 12:00:42,225 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:00:42,229 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 12:00:42,230 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:00:42,236 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:00:42,239 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 12:00:42,239 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 12:00:42,239 - main - INFO - 检查AI服务状态...
2025-06-10 12:00:42,240 - main - INFO - AI服务就绪
2025-06-10 12:00:42,240 - main - INFO - 系统启动完成
2025-06-10 12:00:42,240 - main - INFO - 系统信息:
2025-06-10 12:00:42,240 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 12:00:42,240 - main - INFO -   - 版本: 2.0.0
2025-06-10 12:00:42,240 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 12:00:42,240 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 12:00:42,241 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 12:00:42,241 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 12:42:31,087 - main - INFO - 正在关闭系统...
2025-06-10 12:42:31,088 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 12:42:31,089 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 12:42:31,090 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 12:42:31,091 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 12:42:31,092 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 12:42:31,093 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 12:42:31,093 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 12:42:31,094 - main - INFO - 数据库连接已关闭
2025-06-10 12:42:31,095 - main - INFO - 系统已关闭
2025-06-10 12:43:32,070 - main - INFO - 静态文件服务已配置
2025-06-10 12:43:32,070 - main - INFO - 模板引擎已配置
2025-06-10 12:43:32,079 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 12:43:32,080 - main - INFO - 初始化系统配置...
2025-06-10 12:43:32,083 - main - INFO - 初始化数据库连接...
2025-06-10 12:43:32,083 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 12:43:32,285 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 12:43:32,826 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 12:43:32,827 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:43:32,938 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 12:43:32,940 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:43:32,941 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 12:43:32,942 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:43:32,943 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:43:32,944 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 12:43:32,945 - sqlalchemy.engine.Engine - INFO - [generated in 0.00075s] ()
2025-06-10 12:43:32,946 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 12:43:32,957 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:43:32,959 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:43:32,960 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 12:43:32,961 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:43:32,964 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 12:43:32,965 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:43:32,968 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:43:32,975 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 12:43:32,975 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 12:43:32,976 - main - INFO - 检查AI服务状态...
2025-06-10 12:43:32,976 - main - INFO - AI服务就绪
2025-06-10 12:43:32,976 - main - INFO - 系统启动完成
2025-06-10 12:43:32,977 - main - INFO - 系统信息:
2025-06-10 12:43:32,977 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 12:43:32,977 - main - INFO -   - 版本: 2.0.0
2025-06-10 12:43:32,978 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 12:43:32,978 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 12:43:32,978 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 12:43:32,978 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 12:55:55,961 - main - INFO - 正在关闭系统...
2025-06-10 12:55:55,962 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 12:55:56,042 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 12:55:56,093 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 12:55:56,103 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 12:55:56,124 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 12:55:56,142 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 12:55:56,145 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 12:55:56,147 - main - INFO - 数据库连接已关闭
2025-06-10 12:55:56,148 - main - INFO - 系统已关闭
2025-06-10 12:56:04,214 - main - INFO - 静态文件服务已配置
2025-06-10 12:56:04,215 - main - INFO - 模板引擎已配置
2025-06-10 12:56:04,225 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 12:56:04,226 - main - INFO - 初始化系统配置...
2025-06-10 12:56:04,231 - main - INFO - 初始化数据库连接...
2025-06-10 12:56:04,232 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 12:56:04,330 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 12:56:04,350 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 12:56:04,350 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:56:04,352 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 12:56:04,352 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:56:04,356 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 12:56:04,359 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:56:04,361 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:56:04,362 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 12:56:04,363 - sqlalchemy.engine.Engine - INFO - [generated in 0.00058s] ()
2025-06-10 12:56:04,364 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 12:56:04,364 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:56:04,366 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 12:56:04,366 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 12:56:04,367 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:56:04,370 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 12:56:04,374 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 12:56:04,379 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 12:56:04,380 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 12:56:04,381 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 12:56:04,381 - main - INFO - 检查AI服务状态...
2025-06-10 12:56:04,381 - main - INFO - AI服务就绪
2025-06-10 12:56:04,382 - main - INFO - 系统启动完成
2025-06-10 12:56:04,382 - main - INFO - 系统信息:
2025-06-10 12:56:04,382 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 12:56:04,382 - main - INFO -   - 版本: 2.0.0
2025-06-10 12:56:04,382 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 12:56:04,382 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 12:56:04,383 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 12:56:04,383 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 15:33:10,716 - main - INFO - 正在关闭系统...
2025-06-10 15:33:10,720 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 15:33:10,733 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 15:33:10,735 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 15:33:10,735 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 15:33:10,736 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 15:33:10,736 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 15:33:10,740 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 15:33:10,741 - main - INFO - 数据库连接已关闭
2025-06-10 15:33:10,741 - main - INFO - 系统已关闭
2025-06-10 15:33:23,641 - main - INFO - 静态文件服务已配置
2025-06-10 15:33:23,791 - main - INFO - 模板引擎已配置
2025-06-10 15:33:23,824 - main - INFO - 启动数据中台智能测试系统...
2025-06-10 15:33:23,824 - main - INFO - 初始化系统配置...
2025-06-10 15:33:23,828 - main - INFO - 初始化数据库连接...
2025-06-10 15:33:23,829 - app.database.connection - INFO - 开始初始化数据库...
2025-06-10 15:33:23,931 - app.database.connection - INFO - 数据库连接初始化成功
2025-06-10 15:33:23,965 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-06-10 15:33:23,968 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:33:23,970 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-06-10 15:33:23,973 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:33:23,976 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-06-10 15:33:23,977 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:33:23,979 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 15:33:23,986 - sqlalchemy.engine.Engine - INFO - SELECT 1 as test
2025-06-10 15:33:23,986 - sqlalchemy.engine.Engine - INFO - [generated in 0.00062s] ()
2025-06-10 15:33:23,989 - app.database.connection - INFO - 数据库连接测试成功
2025-06-10 15:33:23,992 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 15:33:23,995 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-06-10 15:33:24,002 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`requirements`
2025-06-10 15:33:24,005 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:33:24,011 - sqlalchemy.engine.Engine - INFO - DESCRIBE `data_platform_test`.`ai_analysis_results`
2025-06-10 15:33:24,017 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-06-10 15:33:24,023 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-06-10 15:33:24,028 - app.database.connection - INFO - 数据库表创建成功
2025-06-10 15:33:24,028 - app.database.connection - INFO - 数据库初始化完成
2025-06-10 15:33:24,028 - main - INFO - 检查AI服务状态...
2025-06-10 15:33:24,037 - main - INFO - AI服务就绪
2025-06-10 15:33:24,038 - main - INFO - 系统启动完成
2025-06-10 15:33:24,038 - main - INFO - 系统信息:
2025-06-10 15:33:24,038 - main - INFO -   - 应用名称: 数据中台智能测试系统
2025-06-10 15:33:24,038 - main - INFO -   - 版本: 2.0.0
2025-06-10 15:33:24,039 - main - INFO -   - 服务地址: http://0.0.0.0:8000
2025-06-10 15:33:24,040 - main - INFO -   - API文档: http://0.0.0.0:8000/docs
2025-06-10 15:33:24,040 - main - INFO -   - 数据库: localhost:3306/data_platform_test
2025-06-10 15:33:24,040 - main - INFO -   - AI模型: deepseek-chat
2025-06-10 15:42:09,152 - main - INFO - 正在关闭系统...
2025-06-10 15:42:09,153 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 15:42:09,157 - sqlalchemy.pool.impl.AsyncAdaptedQueuePool - INFO - Pool recreating
2025-06-10 15:42:09,157 - app.database.connection - INFO - 异步数据库引擎已关闭
2025-06-10 15:42:09,158 - sqlalchemy.pool.impl.QueuePool - INFO - Pool disposed. Pool size: 10  Connections in pool: 0 Current Overflow: -10 Current Checked out connections: 0
2025-06-10 15:42:09,158 - sqlalchemy.pool.impl.QueuePool - INFO - Pool recreating
2025-06-10 15:42:09,159 - app.database.connection - INFO - 同步数据库引擎已关闭
2025-06-10 15:42:09,159 - app.database.connection - INFO - 数据库连接已关闭
2025-06-10 15:42:09,159 - main - INFO - 数据库连接已关闭
2025-06-10 15:42:09,159 - main - INFO - 系统已关闭

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库设置脚本
帮助用户设置MySQL数据库和用户
"""

import sys
import os
from pathlib import Path

print("🗄️ 数据中台智能测试系统 - 数据库设置向导")
print("=" * 60)

print("\n📋 数据库设置选项：")
print("1. 使用现有MySQL root用户（需要root密码）")
print("2. 创建专用数据库用户（推荐）")
print("3. 使用空密码的root用户")
print("4. 跳过数据库设置（仅使用AI功能）")

choice = input("\n请选择设置方式 (1-4): ").strip()

if choice == "1":
    print("\n🔐 使用现有MySQL root用户")
    password = input("请输入MySQL root密码: ").strip()
    
    # 更新配置文件
    env_file = Path("Project03/data_platform_test_system/.env")
    if env_file.exists():
        content = env_file.read_text(encoding='utf-8')
        content = content.replace("MYSQL_USER=dataplatform", "MYSQL_USER=root")
        content = content.replace("MYSQL_PASSWORD=dataplatform123", f"MYSQL_PASSWORD={password}")
        env_file.write_text(content, encoding='utf-8')
        print("✅ 配置已更新")
    else:
        print("❌ 配置文件不存在")

elif choice == "2":
    print("\n👤 创建专用数据库用户")
    print("请在MySQL中执行以下命令：")
    print()
    print("-- 1. 连接到MySQL")
    print("mysql -u root -p")
    print()
    print("-- 2. 创建数据库")
    print("CREATE DATABASE IF NOT EXISTS data_platform_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
    print()
    print("-- 3. 创建用户")
    print("CREATE USER 'dataplatform'@'localhost' IDENTIFIED BY 'dataplatform123';")
    print()
    print("-- 4. 授权")
    print("GRANT ALL PRIVILEGES ON data_platform_test.* TO 'dataplatform'@'localhost';")
    print("FLUSH PRIVILEGES;")
    print()
    print("✅ 配置文件已设置为使用专用用户")

elif choice == "3":
    print("\n🔓 使用空密码的root用户")
    
    # 更新配置文件
    env_file = Path("Project03/data_platform_test_system/.env")
    if env_file.exists():
        content = env_file.read_text(encoding='utf-8')
        content = content.replace("MYSQL_USER=dataplatform", "MYSQL_USER=root")
        content = content.replace("MYSQL_PASSWORD=dataplatform123", "MYSQL_PASSWORD=")
        env_file.write_text(content, encoding='utf-8')
        print("✅ 配置已更新为空密码")
        print("⚠️ 注意：请确保MySQL允许空密码连接")
    else:
        print("❌ 配置文件不存在")

elif choice == "4":
    print("\n⏭️ 跳过数据库设置")
    print("✅ 系统将在无数据库模式下运行")
    print("📝 注意：所有AI功能仍然可用，但数据不会持久化")

else:
    print("❌ 无效选择")
    sys.exit(1)

print("\n🔧 数据库设置完成！")
print("\n📋 下一步操作：")
print("1. 确保MySQL服务正在运行")
print("2. 运行测试脚本验证连接：python test_db_config.py")
print("3. 启动系统：python Project03/data_platform_test_system/start_system.py")

print("\n💡 如果仍有问题，请检查：")
print("- MySQL服务状态")
print("- 防火墙设置")
print("- MySQL配置文件中的bind-address设置")
print("- 用户权限设置")

/**
 * 需求分析页面
 * 负责显示AI分析结果和人工调整功能
 */

window.RequirementsAnalysisPage = {
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    
    async init() {
        console.log('初始化需求分析页面...');
        this.bindEvents();
        await this.loadAnalysisResults();
    },
    
    bindEvents() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshAnalysisBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadAnalysisResults();
            });
        }
    },
    
    async loadAnalysisResults() {
        try {
            const params = {
                page: this.currentPage,
                page_size: this.pageSize
            };
            
            const response = await API.getAnalysisResults(params);
            
            this.totalPages = response.total_pages || 0;
            this.renderAnalysisResults(response.analyses || []);
            this.renderPagination(response);
            
        } catch (error) {
            console.error('加载分析结果失败:', error);
            Utils.error('加载分析结果失败');
        }
    },
    
    renderAnalysisResults(analyses) {
        const tbody = document.getElementById('analysisTableBody');
        if (!tbody) return;
        
        if (analyses.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                        暂无分析结果
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = analyses.map(analysis => `
            <tr data-id="${analysis.id}">
                <td>${analysis.id}</td>
                <td>${analysis.req_id}</td>
                <td>
                    <div class="requirement-title" title="${Utils.escapeHtml(analysis.analysis_result)}">
                        需求 #${analysis.req_id}
                    </div>
                </td>
                <td>
                    <span class="model-tag">${analysis.ai_model || 'DeepSeek'}</span>
                </td>
                <td>
                    <span class="status-tag ${this.getAnalysisStatusClass(analysis.analysis_status)}">
                        ${this.getAnalysisStatusText(analysis.analysis_status)}
                    </span>
                </td>
                <td>${Utils.formatDate(analysis.created_time, 'YYYY-MM-DD HH:mm')}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline" onclick="RequirementsAnalysisPage.viewAnalysis(${analysis.id})">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="RequirementsAnalysisPage.adjustAnalysis(${analysis.id})"
                                ${analysis.analysis_status !== 'completed' ? 'disabled' : ''}>
                            <i class="fas fa-edit"></i> 调整
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },
    
    renderPagination(response) {
        const pagination = document.getElementById('analysisPagination');
        if (!pagination) return;
        
        const { total, page, page_size, total_pages } = response;
        
        let paginationHTML = `
            <div class="pagination-info">
                显示 ${((page - 1) * page_size) + 1}-${Math.min(page * page_size, total)} 条，共 ${total} 条
            </div>
        `;
        
        if (total_pages > 1) {
            paginationHTML += '<div class="pagination-buttons">';
            
            // 上一页
            paginationHTML += `
                <button class="pagination-btn" ${page <= 1 ? 'disabled' : ''} 
                        onclick="RequirementsAnalysisPage.goToPage(${page - 1})">
                    <i class="fas fa-chevron-left"></i> 上一页
                </button>
            `;
            
            // 页码按钮
            const startPage = Math.max(1, page - 2);
            const endPage = Math.min(total_pages, page + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `
                    <button class="pagination-btn ${i === page ? 'active' : ''}" 
                            onclick="RequirementsAnalysisPage.goToPage(${i})">
                        ${i}
                    </button>
                `;
            }
            
            // 下一页
            paginationHTML += `
                <button class="pagination-btn" ${page >= total_pages ? 'disabled' : ''} 
                        onclick="RequirementsAnalysisPage.goToPage(${page + 1})">
                    下一页 <i class="fas fa-chevron-right"></i>
                </button>
            `;
            
            paginationHTML += '</div>';
        }
        
        pagination.innerHTML = paginationHTML;
    },
    
    getAnalysisStatusClass(status) {
        const statusMap = {
            'pending': 'pending',
            'processing': 'analyzing',
            'completed': 'analyzed',
            'failed': 'error'
        };
        return statusMap[status] || 'pending';
    },
    
    getAnalysisStatusText(status) {
        const statusMap = {
            'pending': '待分析',
            'processing': '分析中',
            'completed': '已完成',
            'failed': '失败'
        };
        return statusMap[status] || '未知';
    },
    
    async viewAnalysis(analysisId) {
        try {
            // 这里可以获取详细的分析结果
            // const analysis = await API.getAnalysisDetail(analysisId);
            
            // 创建模态框显示分析结果
            this.showAnalysisModal(analysisId);
            
        } catch (error) {
            console.error('查看分析结果失败:', error);
            Utils.error('查看分析结果失败');
        }
    },
    
    showAnalysisModal(analysisId) {
        const modalHTML = `
            <div class="modal-overlay" id="analysisModal">
                <div class="modal-content analysis-modal">
                    <div class="modal-header">
                        <h3>AI分析结果</h3>
                        <button class="modal-close" onclick="RequirementsAnalysisPage.closeModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="analysis-content">
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p>加载分析结果中...</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-outline" onclick="RequirementsAnalysisPage.closeModal()">
                            关闭
                        </button>
                        <button class="btn btn-primary" onclick="RequirementsAnalysisPage.adjustAnalysis(${analysisId})">
                            人工调整
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        const modalContainer = document.getElementById('modalContainer');
        if (modalContainer) {
            modalContainer.innerHTML = modalHTML;
            
            // 加载分析结果详情
            this.loadAnalysisDetail(analysisId);
        }
    },
    
    async loadAnalysisDetail(analysisId) {
        try {
            // 模拟加载分析结果
            setTimeout(() => {
                const content = document.querySelector('.analysis-content');
                if (content) {
                    content.innerHTML = `
                        <div class="analysis-section">
                            <h4>需求分析摘要</h4>
                            <div class="analysis-text">
                                基于AutoGen + DeepSeek的智能分析结果显示，该需求涉及数据处理、用户界面设计和后端API开发等多个方面。
                                建议采用微服务架构，确保系统的可扩展性和维护性。
                            </div>
                        </div>
                        
                        <div class="analysis-section">
                            <h4>关键要点</h4>
                            <ul class="key-points">
                                <li>数据处理性能要求较高</li>
                                <li>需要考虑用户体验优化</li>
                                <li>安全性和权限控制是重点</li>
                                <li>需要完善的错误处理机制</li>
                            </ul>
                        </div>
                        
                        <div class="analysis-section">
                            <h4>建议方案</h4>
                            <ul class="suggestions">
                                <li>采用Redis缓存提升查询性能</li>
                                <li>实现前端组件化开发</li>
                                <li>使用JWT进行身份认证</li>
                                <li>建立完善的日志监控体系</li>
                            </ul>
                        </div>
                        
                        <div class="analysis-section">
                            <h4>技术栈推荐</h4>
                            <div class="tech-stack">
                                <span class="tech-tag">FastAPI</span>
                                <span class="tech-tag">MySQL</span>
                                <span class="tech-tag">Redis</span>
                                <span class="tech-tag">Vue.js</span>
                                <span class="tech-tag">Docker</span>
                            </div>
                        </div>
                    `;
                }
            }, 1000);
            
        } catch (error) {
            console.error('加载分析详情失败:', error);
            const content = document.querySelector('.analysis-content');
            if (content) {
                content.innerHTML = '<div class="error-message">加载分析结果失败</div>';
            }
        }
    },
    
    async adjustAnalysis(analysisId) {
        this.closeModal();
        
        const adjustmentHTML = `
            <div class="modal-overlay" id="adjustmentModal">
                <div class="modal-content adjustment-modal">
                    <div class="modal-header">
                        <h3>人工调整分析结果</h3>
                        <button class="modal-close" onclick="RequirementsAnalysisPage.closeModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="adjustmentForm">
                            <div class="form-group">
                                <label for="adjustmentText">调整内容</label>
                                <textarea id="adjustmentText" rows="8" 
                                          placeholder="请输入您对AI分析结果的补充或修正..."></textarea>
                                <div class="form-help">您可以对AI分析结果进行补充、修正或添加专业见解</div>
                            </div>
                            
                            <div class="form-group">
                                <label for="adjustedBy">调整者</label>
                                <input type="text" id="adjustedBy" placeholder="请输入您的姓名">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-outline" onclick="RequirementsAnalysisPage.closeModal()">
                            取消
                        </button>
                        <button class="btn btn-primary" onclick="RequirementsAnalysisPage.saveAdjustment(${analysisId})">
                            保存调整
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        const modalContainer = document.getElementById('modalContainer');
        if (modalContainer) {
            modalContainer.innerHTML = adjustmentHTML;
        }
    },
    
    async saveAdjustment(analysisId) {
        const adjustmentText = document.getElementById('adjustmentText').value.trim();
        const adjustedBy = document.getElementById('adjustedBy').value.trim();
        
        if (!adjustmentText) {
            Utils.error('请输入调整内容');
            return;
        }
        
        if (!adjustedBy) {
            Utils.error('请输入调整者姓名');
            return;
        }
        
        try {
            await API.updateManualAdjustment(analysisId, {
                manual_adjustment: adjustmentText,
                adjusted_by: adjustedBy
            });
            
            Utils.success('人工调整保存成功');
            this.closeModal();
            await this.loadAnalysisResults();
            
        } catch (error) {
            console.error('保存调整失败:', error);
            Utils.error('保存调整失败');
        }
    },
    
    closeModal() {
        const modalContainer = document.getElementById('modalContainer');
        if (modalContainer) {
            modalContainer.innerHTML = '';
        }
    },
    
    async goToPage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            await this.loadAnalysisResults();
        }
    }
};

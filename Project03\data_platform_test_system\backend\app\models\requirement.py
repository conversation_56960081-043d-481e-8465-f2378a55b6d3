#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
需求管理数据模型
定义需求相关的数据库表结构，包括需求表和AI分析结果表
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Enum, Boolean, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import enum

# 创建数据库基类
Base = declarative_base()

class RequirementPriority(str, enum.Enum):
    """需求优先级枚举类"""
    HIGH = "高"      # 高优先级
    MEDIUM = "中"    # 中优先级  
    LOW = "低"       # 低优先级

class RequirementStatus(str, enum.Enum):
    """需求状态枚举类"""
    DRAFT = "草稿"           # 草稿状态
    PENDING = "待分析"       # 等待AI分析
    ANALYZING = "分析中"     # AI分析进行中
    ANALYZED = "已分析"      # AI分析完成
    REVIEWING = "审核中"     # 人工审核中
    APPROVED = "已通过"      # 审核通过
    REJECTED = "已拒绝"      # 审核拒绝
    COMPLETED = "已完成"     # 需求完成

class Requirement(Base):
    """
    需求表模型
    存储数据开发需求的基本信息
    """
    __tablename__ = "requirements"
    __table_args__ = {'comment': '数据开发需求表'}
    
    # ==================== 基础字段 ====================
    id = Column(
        Integer, 
        primary_key=True, 
        index=True, 
        autoincrement=True,
        comment="需求唯一标识ID"
    )
    
    title = Column(
        String(255), 
        nullable=False, 
        index=True,
        comment="需求标题（必填字段）"
    )
    
    description = Column(
        Text, 
        nullable=True,
        comment="需求详细描述（支持富文本）"
    )
    
    # ==================== 分类字段 ====================
    priority = Column(
        Enum(RequirementPriority), 
        nullable=False,
        default=RequirementPriority.MEDIUM,
        comment="需求优先级（高/中/低）"
    )
    
    status = Column(
        Enum(RequirementStatus), 
        nullable=False,
        default=RequirementStatus.DRAFT,
        index=True,
        comment="需求当前状态"
    )
    
    # ==================== 关联信息 ====================
    modules = Column(
        JSON, 
        nullable=True,
        comment="关联模块列表（JSON格式存储多选标签）"
    )
    
    attachments = Column(
        JSON, 
        nullable=True,
        comment="附件信息（JSON格式存储文件路径和元数据）"
    )
    
    # ==================== 元数据字段 ====================
    created_by = Column(
        String(100), 
        nullable=True,
        comment="需求创建者"
    )
    
    created_time = Column(
        DateTime, 
        nullable=False,
        default=datetime.utcnow,
        index=True,
        comment="需求创建时间"
    )
    
    updated_time = Column(
        DateTime, 
        nullable=False,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="需求最后更新时间"
    )
    
    # ==================== 数据完整性字段 ====================
    is_complete = Column(
        Boolean, 
        nullable=False,
        default=False,
        comment="需求信息是否完整（用于预检查）"
    )
    
    missing_fields = Column(
        JSON, 
        nullable=True,
        comment="缺失的必填字段列表（JSON格式）"
    )
    
    # ==================== 关联关系 ====================
    # 一对多关系：一个需求可以有多个分析结果
    analysis_results = relationship(
        "AIAnalysisResult", 
        back_populates="requirement",
        cascade="all, delete-orphan",
        lazy="select"
    )
    
    def __repr__(self):
        """对象字符串表示"""
        return f"<Requirement(id={self.id}, title='{self.title}', status='{self.status.value}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "priority": self.priority.value if self.priority else None,
            "status": self.status.value if self.status else None,
            "modules": self.modules,
            "attachments": self.attachments,
            "created_by": self.created_by,
            "created_time": self.created_time.isoformat() if self.created_time else None,
            "updated_time": self.updated_time.isoformat() if self.updated_time else None,
            "is_complete": self.is_complete,
            "missing_fields": self.missing_fields
        }

class AIAnalysisResult(Base):
    """
    AI分析结果表模型
    存储AutoGen + DeepSeek对需求的分析结果
    """
    __tablename__ = "ai_analysis_results"
    __table_args__ = {'comment': 'AI需求分析结果表'}
    
    # ==================== 基础字段 ====================
    id = Column(
        Integer, 
        primary_key=True, 
        index=True, 
        autoincrement=True,
        comment="分析结果唯一标识ID"
    )
    
    req_id = Column(
        Integer, 
        ForeignKey("requirements.id", ondelete="CASCADE"), 
        nullable=False,
        index=True,
        comment="关联的需求ID（外键）"
    )
    
    # ==================== AI分析结果 ====================
    analysis_result = Column(
        Text, 
        nullable=True,
        comment="AI生成的完整分析内容"
    )
    
    structured_result = Column(
        JSON, 
        nullable=True,
        comment="结构化分析结果（JSON格式，包含各个维度的分析）"
    )
    
    key_points = Column(
        JSON, 
        nullable=True,
        comment="关键要点提取（JSON数组格式）"
    )
    
    risk_assessment = Column(
        JSON, 
        nullable=True,
        comment="风险评估结果（JSON格式）"
    )
    
    suggestions = Column(
        JSON, 
        nullable=True,
        comment="改进建议列表（JSON数组格式）"
    )
    
    # ==================== 人工调整 ====================
    manual_adjust = Column(
        Text, 
        nullable=True,
        comment="人工调整和补充的内容"
    )
    
    is_manually_adjusted = Column(
        Boolean, 
        nullable=False,
        default=False,
        comment="是否经过人工调整"
    )
    
    adjusted_by = Column(
        String(100), 
        nullable=True,
        comment="人工调整操作者"
    )
    
    adjusted_time = Column(
        DateTime, 
        nullable=True,
        comment="人工调整时间"
    )
    
    # ==================== AI模型信息 ====================
    ai_model = Column(
        String(100), 
        nullable=True,
        comment="使用的AI模型名称"
    )
    
    ai_version = Column(
        String(50), 
        nullable=True,
        comment="AI模型版本"
    )
    
    analysis_config = Column(
        JSON, 
        nullable=True,
        comment="分析时使用的配置参数（JSON格式）"
    )
    
    # ==================== 分析状态 ====================
    analysis_status = Column(
        String(50), 
        nullable=False,
        default="pending",
        index=True,
        comment="分析状态（pending/processing/completed/failed）"
    )
    
    error_message = Column(
        Text, 
        nullable=True,
        comment="分析失败时的错误信息"
    )
    
    processing_time = Column(
        Integer, 
        nullable=True,
        comment="分析处理耗时（秒）"
    )
    
    # ==================== 时间字段 ====================
    created_time = Column(
        DateTime, 
        nullable=False,
        default=datetime.utcnow,
        comment="分析结果创建时间"
    )
    
    update_time = Column(
        DateTime, 
        nullable=False,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="分析结果最后更新时间"
    )
    
    # ==================== 关联关系 ====================
    # 多对一关系：多个分析结果属于一个需求
    requirement = relationship(
        "Requirement", 
        back_populates="analysis_results",
        lazy="select"
    )
    
    def __repr__(self):
        """对象字符串表示"""
        return f"<AIAnalysisResult(id={self.id}, req_id={self.req_id}, status='{self.analysis_status}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "req_id": self.req_id,
            "analysis_result": self.analysis_result,
            "structured_result": self.structured_result,
            "key_points": self.key_points,
            "risk_assessment": self.risk_assessment,
            "suggestions": self.suggestions,
            "manual_adjust": self.manual_adjust,
            "is_manually_adjusted": self.is_manually_adjusted,
            "adjusted_by": self.adjusted_by,
            "adjusted_time": self.adjusted_time.isoformat() if self.adjusted_time else None,
            "ai_model": self.ai_model,
            "ai_version": self.ai_version,
            "analysis_config": self.analysis_config,
            "analysis_status": self.analysis_status,
            "error_message": self.error_message,
            "processing_time": self.processing_time,
            "created_time": self.created_time.isoformat() if self.created_time else None,
            "update_time": self.update_time.isoformat() if self.update_time else None
        }

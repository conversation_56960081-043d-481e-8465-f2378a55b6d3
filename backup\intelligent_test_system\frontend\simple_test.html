<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .hidden {
            display: none;
        }
        .progress {
            margin: 20px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 4px;
        }
        .log {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 8px;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .log-entry:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .log-entry.log-info {
            border-left-color: #17a2b8;
            background: #f8fdff;
        }

        .log-entry.log-success {
            border-left-color: #28a745;
            background: #f8fff9;
        }

        .log-entry.log-error {
            border-left-color: #dc3545;
            background: #fff8f8;
        }

        .log-entry.log-progress {
            border-left-color: #ffc107;
            background: #fffdf5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简化AI测试页面</h1>
        <p>测试AI生成功能，检查DOM操作错误</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="projectName">项目名称:</label>
                <input type="text" id="projectName" name="project_name" value="测试项目" required>
            </div>
            
            <div class="form-group">
                <label for="requirements">需求描述:</label>
                <textarea id="requirements" name="requirements" required>用户管理系统，包括注册、登录、权限管理功能</textarea>
            </div>
            
            <div class="form-group">
                <label for="testCaseCount">测试用例数量:</label>
                <input type="number" id="testCaseCount" name="test_case_count" value="2" min="1" max="5">
            </div>
            
            <button type="submit" id="generateBtn">🚀 开始生成</button>
        </form>
        
        <div id="generationProgress" class="progress hidden">
            <h3>生成进度</h3>
            <div id="progressText">准备中...</div>
        </div>
        
        <div id="aiLog" class="log hidden">
            <h3>生成日志</h3>
            <div id="logContent"></div>
        </div>
    </div>

    <script>
        // 安全的DOM操作函数
        function safeGetElement(id) {
            try {
                const element = document.getElementById(id);
                if (!element) {
                    console.warn(`元素 ${id} 不存在`);
                    return null;
                }
                return element;
            } catch (error) {
                console.error(`获取元素 ${id} 时出错:`, error);
                return null;
            }
        }
        
        function safeToggleClass(elementId, className, remove = false) {
            try {
                const element = safeGetElement(elementId);
                if (element && element.classList) {
                    if (remove) {
                        element.classList.remove(className);
                    } else {
                        element.classList.add(className);
                    }
                    return true;
                }
            } catch (error) {
                console.error(`切换类 ${className} 在元素 ${elementId} 时出错:`, error);
            }
            return false;
        }
        
        function safeSetProperty(elementId, property, value) {
            try {
                const element = safeGetElement(elementId);
                if (element) {
                    element[property] = value;
                    return true;
                }
            } catch (error) {
                console.error(`设置属性 ${property} 在元素 ${elementId} 时出错:`, error);
            }
            return false;
        }
        
        function addLog(message, type = 'info') {
            const logContent = safeGetElement('logContent');
            if (!logContent) return;
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;

            const typeIcons = {
                'info': '💡',
                'success': '✅',
                'error': '❌',
                'progress': '⚡'
            };

            const icon = typeIcons[type] || '📝';
            logEntry.innerHTML = `<span style="color: #666; font-size: 12px;">[${timestamp}]</span> ${icon} ${message}`;
            
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        document.getElementById('testForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            console.log('开始测试AI生成...');
            
            // 显示进度和日志区域
            safeToggleClass('generationProgress', 'hidden', true);
            safeToggleClass('aiLog', 'hidden', true);
            safeSetProperty('generateBtn', 'disabled', true);
            
            // 清空日志
            const logContent = safeGetElement('logContent');
            if (logContent) {
                logContent.innerHTML = '';
            }
            
            addLog('🚀 开始AI测试用例生成...');
            
            try {
                const formData = new FormData(e.target);
                
                addLog('📤 发送请求到服务器...');
                
                const response = await fetch('/api/generate-test-cases', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                addLog('📡 开始接收流式响应...');
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        addLog('✅ 流式响应接收完成');
                        break;
                    }
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                addLog(`📨 收到数据: ${data.type} - ${data.message}`);
                                
                                if (data.type === 'complete') {
                                    addLog('🎉 AI生成完成！');
                                    if (data.result) {
                                        addLog(`📊 生成了 ${data.result.test_case_count} 个测试用例`);
                                    }
                                } else if (data.type === 'error') {
                                    addLog(`❌ 错误: ${data.message}`);
                                }
                                
                            } catch (parseError) {
                                console.error('解析数据失败:', parseError);
                                addLog(`❌ 数据解析失败: ${parseError.message}`);
                            }
                        }
                    }
                }
                
            } catch (error) {
                addLog(`❌ 请求失败: ${error.message}`);
                console.error('请求错误:', error);
            } finally {
                // 恢复按钮
                safeSetProperty('generateBtn', 'disabled', false);
                addLog('🔄 测试完成，按钮已恢复');
            }
        });
        
        // 页面加载完成后的测试
        window.addEventListener('load', () => {
            console.log('页面加载完成');
            addLog('📄 页面加载完成，DOM元素检查中...');
            
            // 检查关键DOM元素
            const elements = ['testForm', 'generateBtn', 'generationProgress', 'aiLog', 'logContent'];
            elements.forEach(id => {
                const element = safeGetElement(id);
                if (element) {
                    addLog(`✅ 元素 ${id} 存在`);
                } else {
                    addLog(`❌ 元素 ${id} 不存在`);
                }
            });
            
            addLog('🎯 DOM检查完成，可以开始测试');
        });
    </script>
</body>
</html>

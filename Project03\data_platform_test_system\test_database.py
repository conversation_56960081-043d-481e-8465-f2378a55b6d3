#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接测试脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
backend_dir = project_root / "backend"
sys.path.insert(0, str(backend_dir))

async def test_database():
    """测试数据库连接和基本操作"""
    print("开始测试数据库连接...")
    
    try:
        # 导入模块
        from app.database.connection import db_manager, init_database, health_check
        from app.core.config import settings
        
        print(f"数据库配置:")
        print(f"  主机: {settings.MYSQL_HOST}")
        print(f"  端口: {settings.MYSQL_PORT}")
        print(f"  数据库: {settings.MYSQL_DATABASE}")
        print(f"  用户: {settings.MYSQL_USER}")
        print()
        
        # 1. 测试数据库初始化
        print("1. 测试数据库初始化...")
        init_success = await init_database()
        if init_success:
            print("✅ 数据库初始化成功")
        else:
            print("❌ 数据库初始化失败")
            return False
        
        # 2. 测试健康检查
        print("\n2. 测试健康检查...")
        health_result = await health_check()
        print(f"健康检查结果: {health_result}")
        
        if health_result.get('database', {}).get('connected'):
            print("✅ 数据库连接正常")
        else:
            print("❌ 数据库连接异常")
            return False
        
        # 3. 测试需求服务
        print("\n3. 测试需求服务...")
        from app.services.requirement_service import requirement_service
        from app.database.connection import get_async_db
        
        async with get_async_db() as session:
            # 测试获取需求列表
            requirements, total = await requirement_service.get_requirements_list(
                session=session,
                page=1,
                page_size=10
            )
            print(f"✅ 需求列表查询成功，共 {total} 条记录")
            
            # 测试获取分析结果
            analyses, analysis_total = await requirement_service.get_analysis_results(
                session=session,
                page=1,
                page_size=10
            )
            print(f"✅ 分析结果查询成功，共 {analysis_total} 条记录")
        
        # 4. 测试创建示例数据
        print("\n4. 测试创建示例数据...")
        if total == 0:
            print("数据库为空，创建示例数据...")
            await create_sample_data()
        else:
            print(f"数据库已有 {total} 条需求记录")
        
        print("\n🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def create_sample_data():
    """创建示例数据"""
    try:
        from app.services.requirement_service import requirement_service
        from app.database.connection import get_async_db
        
        sample_requirements = [
            {
                "title": "用户数据分析平台",
                "description": "构建用户行为数据分析平台，支持实时数据处理和可视化展示",
                "priority": "高",
                "modules": ["数据采集", "数据处理", "可视化"],
                "created_by": "张三"
            },
            {
                "title": "订单数据ETL流程",
                "description": "建立订单数据的ETL流程，从多个数据源抽取、转换和加载数据",
                "priority": "中",
                "modules": ["ETL", "数据仓库"],
                "created_by": "李四"
            },
            {
                "title": "实时监控大屏",
                "description": "开发业务实时监控大屏，展示关键业务指标和告警信息",
                "priority": "高",
                "modules": ["实时计算", "可视化", "告警"],
                "created_by": "王五"
            }
        ]
        
        async with get_async_db() as session:
            for req_data in sample_requirements:
                requirement = await requirement_service.create_requirement(req_data, session)
                print(f"✅ 创建示例需求: {requirement.title}")
        
        print("✅ 示例数据创建完成")
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")

def main():
    """主函数"""
    print("数据库测试工具")
    print("=" * 50)
    
    # 运行异步测试
    success = asyncio.run(test_database())
    
    if success:
        print("\n✅ 数据库测试完成，系统可以正常使用")
    else:
        print("\n❌ 数据库测试失败，请检查配置和连接")
        sys.exit(1)

if __name__ == "__main__":
    main()

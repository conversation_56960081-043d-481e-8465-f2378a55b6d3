<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .success { border-color: #4CAF50; background: #f8fff8; }
        .error { border-color: #f44336; background: #fff8f8; }
        .info { border-color: #2196F3; background: #f8f8ff; }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976D2; }
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 DOM修复验证测试</h1>
        <p>测试所有可能导致 "Cannot read properties of null (reading 'classList')" 错误的场景</p>
        
        <div class="test-section info" id="test1">
            <h3>测试1: 安全DOM操作函数</h3>
            <p>测试 safeGetElement, safeToggleClass, safeSetProperty 函数</p>
            <button onclick="testSafeFunctions()">运行测试</button>
            <div id="test1-result"></div>
        </div>
        
        <div class="test-section info" id="test2">
            <h3>测试2: 模拟AI生成流程</h3>
            <p>模拟智能测试用例生成的完整DOM操作流程</p>
            <button onclick="testAIGenerationFlow()">运行测试</button>
            <div id="test2-result"></div>
        </div>
        
        <div class="test-section info" id="test3">
            <h3>测试3: 流式响应处理</h3>
            <p>测试流式响应中的DOM更新操作</p>
            <button onclick="testStreamResponse()">运行测试</button>
            <div id="test3-result"></div>
        </div>
        
        <div class="test-section info" id="test4">
            <h3>测试4: 错误处理</h3>
            <p>测试各种错误情况下的DOM操作</p>
            <button onclick="testErrorHandling()">运行测试</button>
            <div id="test4-result"></div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div class="log" id="testLog"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <!-- 模拟的DOM元素 -->
        <div id="generationProgress" class="hidden">进度区域</div>
        <div id="progressFill" style="width: 0%;">进度条</div>
        <div id="progressText">进度文本</div>
        <button id="generateBtn">生成按钮</button>
        <div id="aiLog">AI日志</div>
    </div>

    <script>
        // 复制主页面的安全DOM操作函数
        function safeGetElement(id) {
            try {
                const element = document.getElementById(id);
                if (!element) {
                    console.warn(`元素 ${id} 不存在`);
                    return null;
                }
                return element;
            } catch (error) {
                console.error(`获取元素 ${id} 时出错:`, error);
                return null;
            }
        }
        
        function safeToggleClass(elementId, className, remove = false) {
            try {
                const element = safeGetElement(elementId);
                if (element && element.classList) {
                    if (remove) {
                        element.classList.remove(className);
                    } else {
                        element.classList.add(className);
                    }
                    return true;
                }
            } catch (error) {
                console.error(`切换类 ${className} 在元素 ${elementId} 时出错:`, error);
            }
            return false;
        }
        
        function safeSetProperty(elementId, property, value) {
            try {
                const element = safeGetElement(elementId);
                if (element) {
                    element[property] = value;
                    return true;
                }
            } catch (error) {
                console.error(`设置属性 ${property} 在元素 ${elementId} 时出错:`, error);
            }
            return false;
        }
        
        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }
        
        function setTestResult(testId, success, message) {
            const resultDiv = document.getElementById(`${testId}-result`);
            const testDiv = document.getElementById(testId);
            
            if (success) {
                testDiv.className = 'test-section success';
                resultDiv.innerHTML = `<p style="color: green;">✅ ${message}</p>`;
            } else {
                testDiv.className = 'test-section error';
                resultDiv.innerHTML = `<p style="color: red;">❌ ${message}</p>`;
            }
        }
        
        function testSafeFunctions() {
            log('开始测试安全DOM操作函数...');
            let allPassed = true;
            
            try {
                // 测试存在的元素
                const element = safeGetElement('generateBtn');
                if (!element) {
                    allPassed = false;
                    log('❌ safeGetElement 获取存在元素失败');
                } else {
                    log('✅ safeGetElement 获取存在元素成功');
                }
                
                // 测试不存在的元素
                const nonExistent = safeGetElement('nonExistentElement');
                if (nonExistent !== null) {
                    allPassed = false;
                    log('❌ safeGetElement 应该返回null对于不存在的元素');
                } else {
                    log('✅ safeGetElement 正确处理不存在的元素');
                }
                
                // 测试类切换
                const toggleResult = safeToggleClass('generationProgress', 'hidden', true);
                if (!toggleResult) {
                    allPassed = false;
                    log('❌ safeToggleClass 失败');
                } else {
                    log('✅ safeToggleClass 成功');
                }
                
                // 测试属性设置
                const propResult = safeSetProperty('generateBtn', 'disabled', true);
                if (!propResult) {
                    allPassed = false;
                    log('❌ safeSetProperty 失败');
                } else {
                    log('✅ safeSetProperty 成功');
                }
                
                setTestResult('test1', allPassed, allPassed ? '所有安全函数测试通过' : '部分安全函数测试失败');
                
            } catch (error) {
                log(`❌ 测试过程中出错: ${error.message}`);
                setTestResult('test1', false, `测试出错: ${error.message}`);
            }
        }
        
        function testAIGenerationFlow() {
            log('开始测试AI生成流程...');
            let allPassed = true;
            
            try {
                // 模拟AI生成开始
                log('模拟显示进度区域...');
                safeToggleClass('generationProgress', 'hidden', true);
                safeSetProperty('generateBtn', 'disabled', true);
                
                // 模拟清空日志
                const aiLogElement = safeGetElement('aiLog');
                if (aiLogElement) {
                    aiLogElement.innerHTML = '';
                    log('✅ 清空AI日志成功');
                } else {
                    allPassed = false;
                    log('❌ 获取AI日志元素失败');
                }
                
                // 模拟进度更新
                const progressFill = safeGetElement('progressFill');
                if (progressFill) {
                    progressFill.style.width = '50%';
                    log('✅ 更新进度条成功');
                } else {
                    allPassed = false;
                    log('❌ 获取进度条元素失败');
                }
                
                // 模拟进度文本更新
                const progressText = safeGetElement('progressText');
                if (progressText) {
                    progressText.textContent = '测试进度文本';
                    log('✅ 更新进度文本成功');
                } else {
                    allPassed = false;
                    log('❌ 获取进度文本元素失败');
                }
                
                // 模拟完成
                safeSetProperty('generateBtn', 'disabled', false);
                log('✅ 恢复按钮状态成功');
                
                setTestResult('test2', allPassed, allPassed ? 'AI生成流程测试通过' : 'AI生成流程测试失败');
                
            } catch (error) {
                log(`❌ AI生成流程测试出错: ${error.message}`);
                setTestResult('test2', false, `测试出错: ${error.message}`);
            }
        }
        
        function testStreamResponse() {
            log('开始测试流式响应处理...');
            let allPassed = true;
            
            try {
                // 模拟流式响应数据
                const mockData = [
                    { type: 'progress', progress: 20, message: '需求分析中...' },
                    { type: 'progress', progress: 60, message: '生成测试用例中...' },
                    { type: 'complete', progress: 100, message: '生成完成' }
                ];
                
                mockData.forEach((data, index) => {
                    setTimeout(() => {
                        // 更新进度条
                        if (data.progress !== undefined) {
                            const progressFill = safeGetElement('progressFill');
                            if (progressFill) {
                                progressFill.style.width = data.progress + '%';
                                log(`✅ 更新进度到 ${data.progress}%`);
                            } else {
                                allPassed = false;
                                log('❌ 更新进度条失败');
                            }
                        }
                        
                        // 更新进度文本
                        if (data.message) {
                            const progressText = safeGetElement('progressText');
                            if (progressText) {
                                progressText.textContent = data.message;
                                log(`✅ 更新进度文本: ${data.message}`);
                            } else {
                                allPassed = false;
                                log('❌ 更新进度文本失败');
                            }
                        }
                        
                        // 最后一个数据时设置结果
                        if (index === mockData.length - 1) {
                            setTimeout(() => {
                                setTestResult('test3', allPassed, allPassed ? '流式响应处理测试通过' : '流式响应处理测试失败');
                            }, 100);
                        }
                    }, index * 500);
                });
                
            } catch (error) {
                log(`❌ 流式响应测试出错: ${error.message}`);
                setTestResult('test3', false, `测试出错: ${error.message}`);
            }
        }
        
        function testErrorHandling() {
            log('开始测试错误处理...');
            let allPassed = true;
            
            try {
                // 测试不存在元素的操作
                log('测试操作不存在的元素...');
                
                const result1 = safeToggleClass('nonExistentElement', 'hidden', true);
                if (result1 === false) {
                    log('✅ 正确处理不存在元素的类切换');
                } else {
                    allPassed = false;
                    log('❌ 未正确处理不存在元素的类切换');
                }
                
                const result2 = safeSetProperty('nonExistentElement', 'disabled', true);
                if (result2 === false) {
                    log('✅ 正确处理不存在元素的属性设置');
                } else {
                    allPassed = false;
                    log('❌ 未正确处理不存在元素的属性设置');
                }
                
                // 测试null元素的操作
                log('测试null元素的操作...');
                try {
                    const nullElement = null;
                    if (nullElement && nullElement.classList) {
                        nullElement.classList.add('test');
                    }
                    log('✅ 正确处理null元素操作');
                } catch (error) {
                    log(`⚠️ null元素操作产生错误（这是预期的）: ${error.message}`);
                }
                
                setTestResult('test4', allPassed, allPassed ? '错误处理测试通过' : '错误处理测试失败');
                
            } catch (error) {
                log(`❌ 错误处理测试出错: ${error.message}`);
                setTestResult('test4', false, `测试出错: ${error.message}`);
            }
        }
        
        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            log('🚀 DOM修复验证测试开始');
            log('页面加载完成，所有DOM元素已就绪');
            
            // 检查关键元素是否存在
            const elements = ['generationProgress', 'progressFill', 'progressText', 'generateBtn', 'aiLog'];
            elements.forEach(id => {
                const element = safeGetElement(id);
                if (element) {
                    log(`✅ 元素 ${id} 存在`);
                } else {
                    log(`❌ 元素 ${id} 不存在`);
                }
            });
            
            log('🎯 可以开始手动测试各个功能');
        });
    </script>
</body>
</html>

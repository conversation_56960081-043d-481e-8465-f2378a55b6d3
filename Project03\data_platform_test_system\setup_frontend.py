#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置前端文件结构
"""

import shutil
from pathlib import Path

def setup_frontend():
    """设置前端文件结构"""
    project_root = Path(__file__).parent
    frontend_dir = project_root / "frontend"
    
    # 确保前端目录存在
    frontend_dir.mkdir(exist_ok=True)
    
    # 复制HTML文件到根目录
    source_html = frontend_dir / "templates" / "index.html"
    target_html = frontend_dir / "index.html"
    
    if source_html.exists():
        shutil.copy2(source_html, target_html)
        print(f"✅ 复制HTML文件: {target_html}")
    
    # 检查静态文件目录
    static_dir = frontend_dir / "static"
    if static_dir.exists():
        print(f"✅ 静态文件目录存在: {static_dir}")
        
        # 检查CSS文件
        css_dir = static_dir / "css"
        if css_dir.exists():
            css_files = list(css_dir.glob("*.css"))
            print(f"✅ CSS文件: {len(css_files)} 个")
        
        # 检查JS文件
        js_dir = static_dir / "js"
        if js_dir.exists():
            js_files = list(js_dir.glob("**/*.js"))
            print(f"✅ JavaScript文件: {len(js_files)} 个")
    
    print(f"\n前端目录结构:")
    print(f"  {frontend_dir}/")
    print(f"  ├── index.html")
    print(f"  ├── static/")
    print(f"  │   ├── css/")
    print(f"  │   └── js/")
    print(f"  └── templates/")
    
    return frontend_dir

if __name__ == "__main__":
    frontend_path = setup_frontend()
    print(f"\n🎉 前端设置完成!")
    print(f"前端路径: {frontend_path}")
    print(f"启动命令: python -m http.server 3000")
    print(f"访问地址: http://localhost:3000")

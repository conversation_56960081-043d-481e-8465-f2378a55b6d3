#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库表结构修复脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
backend_dir = project_root / "backend"
sys.path.insert(0, str(backend_dir))

async def check_and_fix_database():
    """检查并修复数据库表结构"""
    print("开始检查和修复数据库表结构...")
    
    try:
        # 导入模块
        from app.database.connection import db_manager
        from app.core.config import settings
        from sqlalchemy import text
        
        print(f"数据库配置:")
        print(f"  主机: {settings.MYSQL_HOST}")
        print(f"  端口: {settings.MYSQL_PORT}")
        print(f"  数据库: {settings.MYSQL_DATABASE}")
        print(f"  用户: {settings.MYSQL_USER}")
        print()
        
        # 1. 初始化数据库连接
        print("1. 初始化数据库连接...")
        if not db_manager.initialize():
            print("❌ 数据库连接初始化失败")
            return False
        
        # 2. 检查当前表结构
        print("\n2. 检查当前表结构...")
        async with db_manager.async_engine.begin() as conn:
            # 检查requirements表结构
            try:
                result = await conn.execute(text("DESCRIBE requirements"))
                columns = result.fetchall()
                print("当前requirements表字段:")
                for col in columns:
                    print(f"  - {col[0]} ({col[1]})")
                
                existing_columns = [col[0] for col in columns]
                
                # 检查缺失的字段
                required_columns = [
                    'id', 'title', 'description', 'priority', 'status', 
                    'modules', 'attachments', 'created_by', 'created_time', 
                    'updated_time', 'is_complete', 'missing_fields'
                ]
                
                missing_columns = [col for col in required_columns if col not in existing_columns]
                
                if missing_columns:
                    print(f"\n❌ 缺失字段: {missing_columns}")
                    print("需要重建表结构...")
                    
                    # 3. 备份现有数据
                    print("\n3. 备份现有数据...")
                    backup_data = []
                    try:
                        result = await conn.execute(text("SELECT * FROM requirements"))
                        backup_data = result.fetchall()
                        print(f"✅ 备份了 {len(backup_data)} 条记录")
                    except Exception as e:
                        print(f"⚠️ 备份数据失败: {e}")
                    
                    # 4. 删除旧表
                    print("\n4. 删除旧表...")
                    await conn.execute(text("DROP TABLE IF EXISTS ai_analysis_results"))
                    await conn.execute(text("DROP TABLE IF EXISTS requirements"))
                    print("✅ 旧表已删除")
                    
                    # 5. 创建新表
                    print("\n5. 创建新表...")
                    from app.models.requirement import Base
                    await conn.run_sync(Base.metadata.create_all)
                    print("✅ 新表已创建")
                    
                    # 6. 恢复数据（如果有的话）
                    if backup_data:
                        print("\n6. 恢复数据...")
                        try:
                            for row in backup_data:
                                # 构建插入语句，只插入存在的字段
                                insert_sql = """
                                INSERT INTO requirements (title, description, priority, status, created_by, created_time, updated_time)
                                VALUES (:title, :description, :priority, :status, :created_by, :created_time, :updated_time)
                                """
                                await conn.execute(text(insert_sql), {
                                    'title': row[1] if len(row) > 1 else '未知标题',
                                    'description': row[2] if len(row) > 2 else '',
                                    'priority': row[3] if len(row) > 3 else '中',
                                    'status': row[4] if len(row) > 4 else '草稿',
                                    'created_by': row[5] if len(row) > 5 else '系统',
                                    'created_time': row[6] if len(row) > 6 else 'NOW()',
                                    'updated_time': row[7] if len(row) > 7 else 'NOW()'
                                })
                            print(f"✅ 恢复了 {len(backup_data)} 条记录")
                        except Exception as e:
                            print(f"⚠️ 恢复数据失败: {e}")
                else:
                    print("✅ 表结构完整，无需修复")
                
            except Exception as e:
                print(f"requirements表不存在或有问题: {e}")
                print("创建新的表结构...")
                
                # 创建新表
                from app.models.requirement import Base
                await conn.execute(text("DROP TABLE IF EXISTS ai_analysis_results"))
                await conn.execute(text("DROP TABLE IF EXISTS requirements"))
                await conn.run_sync(Base.metadata.create_all)
                print("✅ 新表已创建")
        
        # 7. 验证表结构
        print("\n7. 验证新表结构...")
        async with db_manager.async_engine.begin() as conn:
            result = await conn.execute(text("DESCRIBE requirements"))
            columns = result.fetchall()
            print("新requirements表字段:")
            for col in columns:
                print(f"  - {col[0]} ({col[1]})")
            
            result = await conn.execute(text("DESCRIBE ai_analysis_results"))
            columns = result.fetchall()
            print("ai_analysis_results表字段:")
            for col in columns:
                print(f"  - {col[0]} ({col[1]})")
        
        # 8. 创建示例数据
        print("\n8. 创建示例数据...")
        await create_sample_data()
        
        print("\n🎉 数据库表结构修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def create_sample_data():
    """创建示例数据"""
    try:
        from app.services.requirement_service import requirement_service
        from app.database.connection import get_async_db
        
        sample_requirements = [
            {
                "title": "用户数据分析平台",
                "description": "构建用户行为数据分析平台，支持实时数据处理和可视化展示。需要包含数据采集、清洗、分析和展示等完整功能。",
                "priority": "高",
                "modules": ["数据采集", "数据处理", "可视化", "用户分析"],
                "created_by": "张三"
            },
            {
                "title": "订单数据ETL流程",
                "description": "建立订单数据的ETL流程，从多个数据源抽取、转换和加载数据到数据仓库。支持增量更新和全量同步。",
                "priority": "中",
                "modules": ["ETL", "数据仓库", "调度系统"],
                "created_by": "李四"
            },
            {
                "title": "实时监控大屏",
                "description": "开发业务实时监控大屏，展示关键业务指标和告警信息。支持多维度数据展示和实时刷新。",
                "priority": "高",
                "modules": ["实时计算", "可视化", "告警系统", "监控"],
                "created_by": "王五"
            },
            {
                "title": "数据质量监控系统",
                "description": "建立数据质量监控体系，自动检测数据异常、缺失和不一致问题。",
                "priority": "中",
                "modules": ["数据质量", "监控", "告警"],
                "created_by": "赵六"
            }
        ]
        
        async with get_async_db() as session:
            for req_data in sample_requirements:
                requirement = await requirement_service.create_requirement(req_data, session)
                print(f"✅ 创建示例需求: {requirement.title}")
        
        print("✅ 示例数据创建完成")
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")

def main():
    """主函数"""
    print("数据库表结构修复工具")
    print("=" * 50)
    
    # 运行异步修复
    success = asyncio.run(check_and_fix_database())
    
    if success:
        print("\n✅ 数据库修复完成，可以重新测试系统")
    else:
        print("\n❌ 数据库修复失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()

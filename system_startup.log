2025-06-09 21:12:39,573 - INFO - ============================================================
2025-06-09 21:12:39,590 - INFO - 🎉 数据中台智能测试系统
2025-06-09 21:12:39,590 - INFO - ============================================================
2025-06-09 21:12:39,590 - INFO - 📋 系统功能:
2025-06-09 21:12:39,590 - INFO -   • 数据开发需求管理
2025-06-09 21:12:39,590 - INFO -   • AI智能需求分析
2025-06-09 21:12:39,590 - INFO -   • AI测试方案生成
2025-06-09 21:12:39,591 - INFO -   • AI测试用例设计
2025-06-09 21:12:39,592 - INFO -   • 可执行测试脚本生成
2025-06-09 21:12:39,592 - INFO - ============================================================
2025-06-09 21:12:39,605 - INFO - 🔧 技术栈:
2025-06-09 21:12:39,618 - INFO -   • 后端: FastAPI + SQLAlchemy + MySQL
2025-06-09 21:12:39,618 - INFO -   • AI引擎: AutoGen + DeepSeek
2025-06-09 21:12:39,618 - INFO -   • 前端: Vue.js + Element Plus
2025-06-09 21:12:39,618 - INFO - ============================================================
2025-06-09 21:12:39,618 - INFO - 🔍 开始系统检查...
2025-06-09 21:12:39,618 - INFO - 🔍 检查Python版本...
2025-06-09 21:12:39,618 - INFO - ✅ Python版本: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-06-09 21:12:39,618 - INFO - 🔍 检查Python依赖包...
2025-06-09 21:12:41,445 - INFO - ✅ 关键依赖包检查通过
2025-06-09 21:12:41,445 - INFO - 🔍 检查数据库配置...
2025-06-09 21:12:41,482 - INFO - 📊 数据库类型: MySQL
2025-06-09 21:12:41,482 - INFO - 📊 数据库地址: localhost:3306
2025-06-09 21:12:41,483 - INFO - 📊 数据库名称: data_platform_test
2025-06-09 21:12:41,483 - INFO - 🔍 检查AI配置...
2025-06-09 21:12:41,483 - INFO - 🤖 AI模型: deepseek-chat
2025-06-09 21:12:41,483 - INFO - 🤖 API地址: https://api.deepseek.com/v1
2025-06-09 21:12:41,484 - INFO - 🤖 API密钥: sk-1c010f6...
2025-06-09 21:12:41,485 - INFO - 📁 创建必要目录...
2025-06-09 21:12:41,485 - INFO - ✅ 创建目录: d:\Project\Python_project\Project03\Project03\data_platform_test_system\backend\uploads
2025-06-09 21:12:41,486 - INFO - ✅ 创建目录: d:\Project\Python_project\Project03\Project03\data_platform_test_system\backend\exports
2025-06-09 21:12:41,487 - INFO - ✅ 创建目录: d:\Project\Python_project\Project03\Project03\data_platform_test_system\backend\logs
2025-06-09 21:12:41,488 - INFO - ✅ 创建目录: d:\Project\Python_project\Project03\Project03\data_platform_test_system\logs
2025-06-09 21:12:41,489 - INFO - 🔗 测试数据库连接...
2025-06-09 21:12:41,647 - ERROR - ❌ 数据库连接测试失败: No module named 'aiomysql'
2025-06-09 21:12:41,648 - WARNING - ⚠️ 数据库连接检查失败，但系统仍可启动
2025-06-09 21:12:41,648 - INFO - 🤖 测试AI连接...
2025-06-09 21:12:41,682 - ERROR - 初始化AI代理失败: 1 validation error for _LLMConfig
stream
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-06-09 21:12:41,683 - ERROR - ❌ AI连接测试失败: 1 validation error for _LLMConfig
stream
  Extra inputs are not permitted [type=extra_forbidden, input_value=True, input_type=bool]
    For further information visit https://errors.pydantic.dev/2.11/v/extra_forbidden
2025-06-09 21:12:41,684 - WARNING - ⚠️ AI连接检查失败，但系统仍可启动
2025-06-09 21:12:41,684 - INFO - ✅ 系统检查完成
2025-06-09 21:12:41,684 - INFO - 🚀 开始启动系统...
2025-06-09 21:12:41,684 - INFO - 🚀 启动后端服务...
2025-06-09 21:12:41,685 - INFO - 🌐 后端服务地址: http://0.0.0.0:8000
2025-06-09 21:12:41,685 - INFO - 📚 API文档地址: http://0.0.0.0:8000/docs
2025-06-09 21:12:41,724 - ERROR - ❌ 后端服务启动失败: asyncio.run() cannot be called from a running event loop
2025-06-09 21:12:41,726 - ERROR - ❌ 系统启动失败: asyncio.run() cannot be called from a running event loop

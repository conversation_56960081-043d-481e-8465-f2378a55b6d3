<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .data-item {
            margin: 5px 0;
            padding: 5px;
            background: white;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>API功能测试</h1>
    
    <div class="test-container">
        <h2>基础API测试</h2>
        <button onclick="testHealthCheck()">健康检查</button>
        <button onclick="testRequirementsList()">需求列表</button>
        <button onclick="testAnalysisList()">分析列表</button>
        <button onclick="testCreateRequirement()">创建需求</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="results"></div>
    </div>
    
    <div class="test-container">
        <h2>数据显示</h2>
        <div id="dataDisplay"></div>
    </div>
    
    <div class="test-container">
        <h2>详细日志</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        const API_PREFIX = '/api/v1';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function showData(title, data) {
            const dataDiv = document.getElementById('dataDisplay');
            const container = document.createElement('div');
            container.innerHTML = `
                <h3>${title}</h3>
                <div class="data-display">
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            dataDiv.appendChild(container);
        }
        
        function clearResults() {
            document.getElementById('log').textContent = '';
            document.getElementById('results').innerHTML = '';
            document.getElementById('dataDisplay').innerHTML = '';
        }
        
        async function testHealthCheck() {
            log('测试健康检查API...');
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('✅ 健康检查成功', 'success');
                    showData('健康检查结果', data);
                    log(`健康检查成功: ${JSON.stringify(data)}`);
                } else {
                    showResult(`❌ 健康检查失败: ${response.status}`, 'error');
                    log(`健康检查失败: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                showResult(`❌ 健康检查异常: ${error.message}`, 'error');
                log(`健康检查异常: ${error.message}`);
            }
        }
        
        async function testRequirementsList() {
            log('测试需求列表API...');
            try {
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/?page=1&page_size=10`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`✅ 需求列表获取成功，共 ${data.total} 条`, 'success');
                    showData('需求列表', data);
                    log(`需求列表获取成功: 总数=${data.total}, 当前页=${data.page}`);
                } else {
                    showResult(`❌ 需求列表获取失败: ${response.status}`, 'error');
                    log(`需求列表获取失败: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                showResult(`❌ 需求列表异常: ${error.message}`, 'error');
                log(`需求列表异常: ${error.message}`);
            }
        }
        
        async function testAnalysisList() {
            log('测试分析列表API...');
            try {
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/analyses/?page=1&page_size=10`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`✅ 分析列表获取成功，共 ${data.total} 条`, 'success');
                    showData('分析列表', data);
                    log(`分析列表获取成功: 总数=${data.total}, 当前页=${data.page}`);
                } else {
                    showResult(`❌ 分析列表获取失败: ${response.status}`, 'error');
                    log(`分析列表获取失败: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                showResult(`❌ 分析列表异常: ${error.message}`, 'error');
                log(`分析列表异常: ${error.message}`);
            }
        }
        
        async function testCreateRequirement() {
            log('测试创建需求API...');
            try {
                const testData = {
                    title: `测试需求 ${new Date().toLocaleTimeString()}`,
                    description: '这是一个API测试创建的需求',
                    priority: '中',
                    modules: ['测试模块'],
                    created_by: 'API测试'
                };
                
                const response = await fetch(`${API_BASE_URL}${API_PREFIX}/requirements/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`✅ 需求创建成功，ID: ${data.id}`, 'success');
                    showData('创建的需求', data);
                    log(`需求创建成功: ID=${data.id}, 标题=${data.title}`);
                } else {
                    showResult(`❌ 需求创建失败: ${response.status}`, 'error');
                    log(`需求创建失败: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                showResult(`❌ 需求创建异常: ${error.message}`, 'error');
                log(`需求创建异常: ${error.message}`);
            }
        }
        
        // 页面加载时自动测试健康检查
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动测试...');
            setTimeout(testHealthCheck, 1000);
        });
    </script>
</body>
</html>

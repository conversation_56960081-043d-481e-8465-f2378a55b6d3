/**
 * 需求理解模块 - 负责查询需求理解功能
 */

class RequirementUnderstandingModule {
    constructor() {
        this.uploadedFiles = [];
        this.currentTaskId = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupFileUpload();
    }

    setupEventListeners() {
        // 需求理解表单提交
        const form = document.getElementById('understandingForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    }

    setupFileUpload() {
        // 文件上传处理
        const uploadArea = document.getElementById('understandingUploadArea');
        const fileInput = document.getElementById('understandingFileInput');
        
        if (uploadArea && fileInput) {
            uploadArea.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
            
            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                this.handleFileSelect({ target: { files: e.dataTransfer.files } });
            });
        }
    }

    handleFileSelect(event) {
        const files = Array.from(event.target.files);
        const fileList = document.getElementById('understandingFileList');
        
        files.forEach(file => {
            if (file.size > 10 * 1024 * 1024) {
                alert(`文件 ${file.name} 超过10MB限制`);
                return;
            }
            
            this.uploadedFiles.push(file);
            
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <span class="file-icon">${this.getFileIcon(file.name)}</span>
                    <span>${file.name}</span>
                    <span style="color: var(--text-secondary); font-size: 12px;">(${(file.size / 1024).toFixed(1)} KB)</span>
                </div>
                <button class="file-remove" onclick="requirementModule.removeFile(this, '${file.name}')">✕</button>
            `;
            fileList.appendChild(fileItem);
        });
    }

    removeFile(button, filename) {
        this.uploadedFiles = this.uploadedFiles.filter(file => file.name !== filename);
        button.parentElement.remove();
    }

    getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const icons = {
            'png': '🖼️', 'jpg': '🖼️', 'jpeg': '🖼️', 'gif': '🖼️', 'bmp': '🖼️',
            'pdf': '📄', 'doc': '📝', 'docx': '📝', 'xls': '📊', 'xlsx': '📊',
            'ppt': '📊', 'pptx': '📊'
        };
        return icons[ext] || '📎';
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        
        // 添加上传的文件
        this.uploadedFiles.forEach(file => {
            formData.append('files', file);
        });
        
        // 显示进度区域
        document.getElementById('understandingProgress').classList.remove('hidden');
        document.getElementById('understandingBtn').disabled = true;
        
        // 清空之前的日志
        document.getElementById('understandingAiLog').innerHTML = '';
        this.addLog('🧠 启动需求智能理解分析...', 'info');
        
        try {
            const response = await fetch('/api/requirement-understanding', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            await this.processStreamResponse(response);
            
        } catch (error) {
            this.addLog(`生成失败: ${error.message}`, 'error');
            console.error('生成失败:', error);
        } finally {
            document.getElementById('understandingBtn').disabled = false;
        }
    }

    async processStreamResponse(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        this.handleStreamData(data);
                    } catch (parseError) {
                        console.error('解析数据失败:', parseError);
                        this.addLog(`数据解析失败: ${parseError.message}`, 'error');
                    }
                }
            }
        }
    }

    handleStreamData(data) {
        // 更新进度条
        if (data.progress !== undefined) {
            document.getElementById('understandingProgressFill').style.width = data.progress + '%';
        }
        
        // 更新进度文本
        if (data.message) {
            document.getElementById('understandingProgressText').textContent = data.message;
        }
        
        // 处理不同类型的数据
        switch (data.type) {
            case 'progress':
                this.addLog(data.message, 'progress', data.agent);
                break;
            case 'prompt':
                this.addLog(data.message, 'info', data.agent);
                this.showPromptDetails(data.agent, data.prompt);
                break;
            case 'ai_response':
                this.addLog(data.message, 'info', data.agent);
                this.showAIResponseDetails(data.agent, data.response);
                break;
            case 'complete':
                this.handleComplete(data);
                break;
            case 'error':
                this.addLog(data.message, 'error');
                break;
            case 'info':
                this.addLog(data.message, 'info');
                break;
        }
    }

    handleComplete(data) {
        this.addLog(data.message, 'success');
        
        if (data.result) {
            this.currentTaskId = data.result.task_id;
            
            // 保存到需求资产库
            if (window.requirementAssetsModule) {
                window.requirementAssetsModule.saveAsset(data.result);
            }
            
            // 显示结构化需求
            this.displayStructuredRequirements(data.result.structured_requirements, data.result);
            
            this.addLog(`🎉 需求智能理解完成！已保存到需求资产库`, 'success');
        }
    }

    addLog(message, type = 'info', agent = null) {
        const log = document.getElementById('understandingAiLog');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        
        const typeIcons = {
            'info': '💡',
            'success': '✅',
            'error': '❌',
            'progress': '⚡',
            'analysis': '🔍',
            'agent': '🤖'
        };
        
        let agentInfo = '';
        if (agent) {
            agentInfo = `<span class="log-agent">[${agent}]</span> `;
        }
        
        logEntry.innerHTML = `
            <span class="log-timestamp">[${timestamp}]</span> 
            ${agentInfo}${typeIcons[type] || '💡'} ${message}
        `;
        
        log.appendChild(logEntry);
        log.scrollTop = log.scrollHeight;
    }

    showPromptDetails(agent, prompt) {
        // 实现提示词详情显示
        console.log('Show prompt details:', agent, prompt);
    }

    showAIResponseDetails(agent, response) {
        // 实现AI响应详情显示
        console.log('Show AI response details:', agent, response);
    }

    displayStructuredRequirements(requirements, result = null) {
        // 实现结构化需求显示
        console.log('Display structured requirements:', requirements, result);
    }
}

// 全局实例
window.requirementModule = new RequirementUnderstandingModule();
